import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { ChattrixRoutingModule } from './chattrix-routing.module';
import { UserManagementComponent } from './Pages/user-management/user-management.component';
import { DashboardComponent } from './Pages/dashboard/dashboard.component';
import { SideBarComponent } from './pages/side-bar/side-bar.component';
import { ChatWindowComponent } from './pages/chat-window/chat-window.component';
import { ChatUsersComponent } from './pages/chat-users/chat-users.component';

@NgModule({
  declarations: [UserManagementComponent, DashboardComponent, SideBarComponent, ChatWindowComponent, ChatUsersComponent],
  imports: [CommonModule, ChattrixRoutingModule],
})
export class ChattrixModule {}
