import{Ab as Gt,D as zt,Db as Wt,Eb as Yt,Fa as Qt,Fb as Xe,G as v,I as ie,Ib as xt,J as ne,Jb as Xt,L as H,X as Kt,a as ve,c as Rt,ca as xe,g as Ft,k as Te,ka as qt,kb as Vt,qa as Bt,r as Ot,ra as re,t as It,tb as jt,ub as $t,v as Lt,vb as be,xb as Ut,zb as Ht}from"./chunk-UM6LOK3U.js";var _=function(n){return n[n.State=0]="State",n[n.Transition=1]="Transition",n[n.Sequence=2]="Sequence",n[n.Group=3]="Group",n[n.Animate=4]="Animate",n[n.Keyframes=5]="Keyframes",n[n.Style=6]="Style",n[n.Trigger=7]="Trigger",n[n.Reference=8]="Reference",n[n.AnimateChild=9]="AnimateChild",n[n.AnimateRef=10]="AnimateRef",n[n.Query=11]="Query",n[n.Stagger=12]="Stagger",n}(_||{}),z="*";function Zt(n,e=null){return{type:_.Sequence,steps:n,options:e}}function Ze(n){return{type:_.Style,styles:n,offset:null}}var j=class{_onDoneFns=[];_onStartFns=[];_onDestroyFns=[];_originalOnDoneFns=[];_originalOnStartFns=[];_started=!1;_destroyed=!1;_finished=!1;_position=0;parentPlayer=null;totalTime;constructor(e=0,t=0){this.totalTime=e+t}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(e=>e()),this._onDoneFns=[])}onStart(e){this._originalOnStartFns.push(e),this._onStartFns.push(e)}onDone(e){this._originalOnDoneFns.push(e),this._onDoneFns.push(e)}onDestroy(e){this._onDestroyFns.push(e)}hasStarted(){return this._started}init(){}play(){this.hasStarted()||(this._onStart(),this.triggerMicrotask()),this._started=!0}triggerMicrotask(){queueMicrotask(()=>this._onFinish())}_onStart(){this._onStartFns.forEach(e=>e()),this._onStartFns=[]}pause(){}restart(){}finish(){this._onFinish()}destroy(){this._destroyed||(this._destroyed=!0,this.hasStarted()||this._onStart(),this.finish(),this._onDestroyFns.forEach(e=>e()),this._onDestroyFns=[])}reset(){this._started=!1,this._finished=!1,this._onStartFns=this._originalOnStartFns,this._onDoneFns=this._originalOnDoneFns}setPosition(e){this._position=this.totalTime?e*this.totalTime:1}getPosition(){return this.totalTime?this._position/this.totalTime:1}triggerCallback(e){let t=e=="start"?this._onStartFns:this._onDoneFns;t.forEach(s=>s()),t.length=0}},oe=class{_onDoneFns=[];_onStartFns=[];_finished=!1;_started=!1;_destroyed=!1;_onDestroyFns=[];parentPlayer=null;totalTime=0;players;constructor(e){this.players=e;let t=0,s=0,i=0,r=this.players.length;r==0?queueMicrotask(()=>this._onFinish()):this.players.forEach(o=>{o.onDone(()=>{++t==r&&this._onFinish()}),o.onDestroy(()=>{++s==r&&this._onDestroy()}),o.onStart(()=>{++i==r&&this._onStart()})}),this.totalTime=this.players.reduce((o,a)=>Math.max(o,a.totalTime),0)}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(e=>e()),this._onDoneFns=[])}init(){this.players.forEach(e=>e.init())}onStart(e){this._onStartFns.push(e)}_onStart(){this.hasStarted()||(this._started=!0,this._onStartFns.forEach(e=>e()),this._onStartFns=[])}onDone(e){this._onDoneFns.push(e)}onDestroy(e){this._onDestroyFns.push(e)}hasStarted(){return this._started}play(){this.parentPlayer||this.init(),this._onStart(),this.players.forEach(e=>e.play())}pause(){this.players.forEach(e=>e.pause())}restart(){this.players.forEach(e=>e.restart())}finish(){this._onFinish(),this.players.forEach(e=>e.finish())}destroy(){this._onDestroy()}_onDestroy(){this._destroyed||(this._destroyed=!0,this._onFinish(),this.players.forEach(e=>e.destroy()),this._onDestroyFns.forEach(e=>e()),this._onDestroyFns=[])}reset(){this.players.forEach(e=>e.reset()),this._destroyed=!1,this._finished=!1,this._started=!1}setPosition(e){let t=e*this.totalTime;this.players.forEach(s=>{let i=s.totalTime?Math.min(1,t/s.totalTime):1;s.setPosition(i)})}getPosition(){let e=this.players.reduce((t,s)=>t===null||s.totalTime>t.totalTime?s:t,null);return e!=null?e.getPosition():0}beforeDestroy(){this.players.forEach(e=>{e.beforeDestroy&&e.beforeDestroy()})}triggerCallback(e){let t=e=="start"?this._onStartFns:this._onDoneFns;t.forEach(s=>s()),t.length=0}},he="!";function Jt(n){return new v(3e3,!1)}function Zs(){return new v(3100,!1)}function Js(){return new v(3101,!1)}function ei(n){return new v(3001,!1)}function ti(n){return new v(3003,!1)}function si(n){return new v(3004,!1)}function ts(n,e){return new v(3005,!1)}function ss(){return new v(3006,!1)}function is(){return new v(3007,!1)}function ns(n,e){return new v(3008,!1)}function rs(n){return new v(3002,!1)}function os(n,e,t,s,i){return new v(3010,!1)}function as(){return new v(3011,!1)}function ls(){return new v(3012,!1)}function us(){return new v(3200,!1)}function cs(){return new v(3202,!1)}function hs(){return new v(3013,!1)}function fs(n){return new v(3014,!1)}function ds(n){return new v(3015,!1)}function ms(n){return new v(3016,!1)}function ps(n,e){return new v(3404,!1)}function ii(n){return new v(3502,!1)}function gs(n){return new v(3503,!1)}function ys(){return new v(3300,!1)}function _s(n){return new v(3504,!1)}function Ss(n){return new v(3301,!1)}function Es(n,e){return new v(3302,!1)}function vs(n){return new v(3303,!1)}function Ts(n,e){return new v(3400,!1)}function bs(n){return new v(3401,!1)}function ws(n){return new v(3402,!1)}function As(n,e){return new v(3505,!1)}function $(n){switch(n.length){case 0:return new j;case 1:return n[0];default:return new oe(n)}}function st(n,e,t=new Map,s=new Map){let i=[],r=[],o=-1,a=null;if(e.forEach(l=>{let u=l.get("offset"),h=u==o,c=h&&a||new Map;l.forEach((S,y)=>{let d=y,g=S;if(y!=="offset")switch(d=n.normalizePropertyName(d,i),g){case he:g=t.get(y);break;case z:g=s.get(y);break;default:g=n.normalizeStyleValue(y,d,g,i);break}c.set(d,g)}),h||r.push(c),a=c,o=u}),i.length)throw ii(i);return r}function we(n,e,t,s){switch(e){case"start":n.onStart(()=>s(t&&Je(t,"start",n)));break;case"done":n.onDone(()=>s(t&&Je(t,"done",n)));break;case"destroy":n.onDestroy(()=>s(t&&Je(t,"destroy",n)));break}}function Je(n,e,t){let s=t.totalTime,i=!!t.disabled,r=Ae(n.element,n.triggerName,n.fromState,n.toState,e||n.phaseName,s??n.totalTime,i),o=n._data;return o!=null&&(r._data=o),r}function Ae(n,e,t,s,i="",r=0,o){return{element:n,triggerName:e,fromState:t,toState:s,phaseName:i,totalTime:r,disabled:!!o}}function R(n,e,t){let s=n.get(e);return s||n.set(e,s=t),s}function it(n){let e=n.indexOf(":"),t=n.substring(1,e),s=n.slice(e+1);return[t,s]}var ni=typeof document>"u"?null:document.documentElement;function Pe(n){let e=n.parentNode||n.host||null;return e===ni?null:e}function ri(n){return n.substring(1,6)=="ebkit"}var X=null,es=!1;function Ps(n){X||(X=oi()||{},es=X.style?"WebkitAppearance"in X.style:!1);let e=!0;return X.style&&!ri(n)&&(e=n in X.style,!e&&es&&(e="Webkit"+n.charAt(0).toUpperCase()+n.slice(1)in X.style)),e}function oi(){return typeof document<"u"?document.body:null}function nt(n,e){for(;e;){if(e===n)return!0;e=Pe(e)}return!1}function rt(n,e,t){if(t)return Array.from(n.querySelectorAll(e));let s=n.querySelector(e);return s?[s]:[]}var ai=1e3,ot="{{",li="}}",at="ng-enter",Ne="ng-leave",fe="ng-trigger",de=".ng-trigger",lt="ng-animating",Me=".ng-animating";function V(n){if(typeof n=="number")return n;let e=n.match(/^(-?[\.\d]+)(m?s)/);return!e||e.length<2?0:et(parseFloat(e[1]),e[2])}function et(n,e){switch(e){case"s":return n*ai;default:return n}}function me(n,e,t){return n.hasOwnProperty("duration")?n:ui(n,e,t)}function ui(n,e,t){let s=/^(-?[\.\d]+)(m?s)(?:\s+(-?[\.\d]+)(m?s))?(?:\s+([-a-z]+(?:\(.+?\))?))?$/i,i,r=0,o="";if(typeof n=="string"){let a=n.match(s);if(a===null)return e.push(Jt(n)),{duration:0,delay:0,easing:""};i=et(parseFloat(a[1]),a[2]);let l=a[3];l!=null&&(r=et(parseFloat(l),a[4]));let u=a[5];u&&(o=u)}else i=n;if(!t){let a=!1,l=e.length;i<0&&(e.push(Zs()),a=!0),r<0&&(e.push(Js()),a=!0),a&&e.splice(l,0,Jt(n))}return{duration:i,delay:r,easing:o}}function Ns(n){return n.length?n[0]instanceof Map?n:n.map(e=>new Map(Object.entries(e))):[]}function K(n,e,t){e.forEach((s,i)=>{let r=De(i);t&&!t.has(i)&&t.set(i,n.style[r]),n.style[r]=s})}function G(n,e){e.forEach((t,s)=>{let i=De(s);n.style[i]=""})}function ae(n){return Array.isArray(n)?n.length==1?n[0]:Zt(n):n}function Ms(n,e,t){let s=e.params||{},i=ut(n);i.length&&i.forEach(r=>{s.hasOwnProperty(r)||t.push(ei(r))})}var tt=new RegExp(`${ot}\\s*(.+?)\\s*${li}`,"g");function ut(n){let e=[];if(typeof n=="string"){let t;for(;t=tt.exec(n);)e.push(t[1]);tt.lastIndex=0}return e}function le(n,e,t){let s=`${n}`,i=s.replace(tt,(r,o)=>{let a=e[o];return a==null&&(t.push(ti(o)),a=""),a.toString()});return i==s?n:i}var ci=/-+([a-z0-9])/g;function De(n){return n.replace(ci,(...e)=>e[1].toUpperCase())}function Ds(n,e){return n===0||e===0}function Cs(n,e,t){if(t.size&&e.length){let s=e[0],i=[];if(t.forEach((r,o)=>{s.has(o)||i.push(o),s.set(o,r)}),i.length)for(let r=1;r<e.length;r++){let o=e[r];i.forEach(a=>o.set(a,Ce(n,a)))}}return e}function F(n,e,t){switch(e.type){case _.Trigger:return n.visitTrigger(e,t);case _.State:return n.visitState(e,t);case _.Transition:return n.visitTransition(e,t);case _.Sequence:return n.visitSequence(e,t);case _.Group:return n.visitGroup(e,t);case _.Animate:return n.visitAnimate(e,t);case _.Keyframes:return n.visitKeyframes(e,t);case _.Style:return n.visitStyle(e,t);case _.Reference:return n.visitReference(e,t);case _.AnimateChild:return n.visitAnimateChild(e,t);case _.AnimateRef:return n.visitAnimateRef(e,t);case _.Query:return n.visitQuery(e,t);case _.Stagger:return n.visitStagger(e,t);default:throw si(e.type)}}function Ce(n,e){return window.getComputedStyle(n)[e]}var Pt=(()=>{class n{validateStyleProperty(t){return Ps(t)}containsElement(t,s){return nt(t,s)}getParentElement(t){return Pe(t)}query(t,s,i){return rt(t,s,i)}computeStyle(t,s,i){return i||""}animate(t,s,i,r,o,a=[],l){return new j(i,r)}static \u0275fac=function(s){return new(s||n)};static \u0275prov=ie({token:n,factory:n.\u0275fac})}return n})(),J=class{static NOOP=new Pt},ee=class{};var hi=new Set(["width","height","minWidth","minHeight","maxWidth","maxHeight","left","top","bottom","right","fontSize","outlineWidth","outlineOffset","paddingTop","paddingLeft","paddingBottom","paddingRight","marginTop","marginLeft","marginBottom","marginRight","borderRadius","borderWidth","borderTopWidth","borderLeftWidth","borderRightWidth","borderBottomWidth","textIndent","perspective"]),Ie=class extends ee{normalizePropertyName(e,t){return De(e)}normalizeStyleValue(e,t,s,i){let r="",o=s.toString().trim();if(hi.has(t)&&s!==0&&s!=="0")if(typeof s=="number")r="px";else{let a=s.match(/^[+-]?[\d\.]+([a-z]*)$/);a&&a[1].length==0&&i.push(ts(e,s))}return o+r}};var Le="*";function fi(n,e){let t=[];return typeof n=="string"?n.split(/\s*,\s*/).forEach(s=>di(s,t,e)):t.push(n),t}function di(n,e,t){if(n[0]==":"){let l=mi(n,t);if(typeof l=="function"){e.push(l);return}n=l}let s=n.match(/^(\*|[-\w]+)\s*(<?[=-]>)\s*(\*|[-\w]+)$/);if(s==null||s.length<4)return t.push(ds(n)),e;let i=s[1],r=s[2],o=s[3];e.push(ks(i,o));let a=i==Le&&o==Le;r[0]=="<"&&!a&&e.push(ks(o,i))}function mi(n,e){switch(n){case":enter":return"void => *";case":leave":return"* => void";case":increment":return(t,s)=>parseFloat(s)>parseFloat(t);case":decrement":return(t,s)=>parseFloat(s)<parseFloat(t);default:return e.push(ms(n)),"* => *"}}var ke=new Set(["true","1"]),Re=new Set(["false","0"]);function ks(n,e){let t=ke.has(n)||Re.has(n),s=ke.has(e)||Re.has(e);return(i,r)=>{let o=n==Le||n==i,a=e==Le||e==r;return!o&&t&&typeof i=="boolean"&&(o=i?ke.has(n):Re.has(n)),!a&&s&&typeof r=="boolean"&&(a=r?ke.has(e):Re.has(e)),o&&a}}var Qs=":self",pi=new RegExp(`s*${Qs}s*,?`,"g");function Vs(n,e,t,s){return new pt(n).build(e,t,s)}var Rs="",pt=class{_driver;constructor(e){this._driver=e}build(e,t,s){let i=new gt(t);return this._resetContextStyleTimingState(i),F(this,ae(e),i)}_resetContextStyleTimingState(e){e.currentQuerySelector=Rs,e.collectedStyles=new Map,e.collectedStyles.set(Rs,new Map),e.currentTime=0}visitTrigger(e,t){let s=t.queryCount=0,i=t.depCount=0,r=[],o=[];return e.name.charAt(0)=="@"&&t.errors.push(ss()),e.definitions.forEach(a=>{if(this._resetContextStyleTimingState(t),a.type==_.State){let l=a,u=l.name;u.toString().split(/\s*,\s*/).forEach(h=>{l.name=h,r.push(this.visitState(l,t))}),l.name=u}else if(a.type==_.Transition){let l=this.visitTransition(a,t);s+=l.queryCount,i+=l.depCount,o.push(l)}else t.errors.push(is())}),{type:_.Trigger,name:e.name,states:r,transitions:o,queryCount:s,depCount:i,options:null}}visitState(e,t){let s=this.visitStyle(e.styles,t),i=e.options&&e.options.params||null;if(s.containsDynamicStyles){let r=new Set,o=i||{};s.styles.forEach(a=>{a instanceof Map&&a.forEach(l=>{ut(l).forEach(u=>{o.hasOwnProperty(u)||r.add(u)})})}),r.size&&t.errors.push(ns(e.name,[...r.values()]))}return{type:_.State,name:e.name,style:s,options:i?{params:i}:null}}visitTransition(e,t){t.queryCount=0,t.depCount=0;let s=F(this,ae(e.animation),t),i=fi(e.expr,t.errors);return{type:_.Transition,matchers:i,animation:s,queryCount:t.queryCount,depCount:t.depCount,options:Z(e.options)}}visitSequence(e,t){return{type:_.Sequence,steps:e.steps.map(s=>F(this,s,t)),options:Z(e.options)}}visitGroup(e,t){let s=t.currentTime,i=0,r=e.steps.map(o=>{t.currentTime=s;let a=F(this,o,t);return i=Math.max(i,t.currentTime),a});return t.currentTime=i,{type:_.Group,steps:r,options:Z(e.options)}}visitAnimate(e,t){let s=Si(e.timings,t.errors);t.currentAnimateTimings=s;let i,r=e.styles?e.styles:Ze({});if(r.type==_.Keyframes)i=this.visitKeyframes(r,t);else{let o=e.styles,a=!1;if(!o){a=!0;let u={};s.easing&&(u.easing=s.easing),o=Ze(u)}t.currentTime+=s.duration+s.delay;let l=this.visitStyle(o,t);l.isEmptyStep=a,i=l}return t.currentAnimateTimings=null,{type:_.Animate,timings:s,style:i,options:null}}visitStyle(e,t){let s=this._makeStyleAst(e,t);return this._validateStyleAst(s,t),s}_makeStyleAst(e,t){let s=[],i=Array.isArray(e.styles)?e.styles:[e.styles];for(let a of i)typeof a=="string"?a===z?s.push(a):t.errors.push(rs(a)):s.push(new Map(Object.entries(a)));let r=!1,o=null;return s.forEach(a=>{if(a instanceof Map&&(a.has("easing")&&(o=a.get("easing"),a.delete("easing")),!r)){for(let l of a.values())if(l.toString().indexOf(ot)>=0){r=!0;break}}}),{type:_.Style,styles:s,easing:o,offset:e.offset,containsDynamicStyles:r,options:null}}_validateStyleAst(e,t){let s=t.currentAnimateTimings,i=t.currentTime,r=t.currentTime;s&&r>0&&(r-=s.duration+s.delay),e.styles.forEach(o=>{typeof o!="string"&&o.forEach((a,l)=>{let u=t.collectedStyles.get(t.currentQuerySelector),h=u.get(l),c=!0;h&&(r!=i&&r>=h.startTime&&i<=h.endTime&&(t.errors.push(os(l,h.startTime,h.endTime,r,i)),c=!1),r=h.startTime),c&&u.set(l,{startTime:r,endTime:i}),t.options&&Ms(a,t.options,t.errors)})})}visitKeyframes(e,t){let s={type:_.Keyframes,styles:[],options:null};if(!t.currentAnimateTimings)return t.errors.push(as()),s;let i=1,r=0,o=[],a=!1,l=!1,u=0,h=e.steps.map(w=>{let A=this._makeStyleAst(w,t),D=A.offset!=null?A.offset:_i(A.styles),N=0;return D!=null&&(r++,N=A.offset=D),l=l||N<0||N>1,a=a||N<u,u=N,o.push(N),A});l&&t.errors.push(ls()),a&&t.errors.push(us());let c=e.steps.length,S=0;r>0&&r<c?t.errors.push(cs()):r==0&&(S=i/(c-1));let y=c-1,d=t.currentTime,g=t.currentAnimateTimings,T=g.duration;return h.forEach((w,A)=>{let D=S>0?A==y?1:S*A:o[A],N=D*T;t.currentTime=d+g.delay+N,g.duration=N,this._validateStyleAst(w,t),w.offset=D,s.styles.push(w)}),s}visitReference(e,t){return{type:_.Reference,animation:F(this,ae(e.animation),t),options:Z(e.options)}}visitAnimateChild(e,t){return t.depCount++,{type:_.AnimateChild,options:Z(e.options)}}visitAnimateRef(e,t){return{type:_.AnimateRef,animation:this.visitReference(e.animation,t),options:Z(e.options)}}visitQuery(e,t){let s=t.currentQuerySelector,i=e.options||{};t.queryCount++,t.currentQuery=e;let[r,o]=gi(e.selector);t.currentQuerySelector=s.length?s+" "+r:r,R(t.collectedStyles,t.currentQuerySelector,new Map);let a=F(this,ae(e.animation),t);return t.currentQuery=null,t.currentQuerySelector=s,{type:_.Query,selector:r,limit:i.limit||0,optional:!!i.optional,includeSelf:o,animation:a,originalSelector:e.selector,options:Z(e.options)}}visitStagger(e,t){t.currentQuery||t.errors.push(hs());let s=e.timings==="full"?{duration:0,delay:0,easing:"full"}:me(e.timings,t.errors,!0);return{type:_.Stagger,animation:F(this,ae(e.animation),t),timings:s,options:null}}};function gi(n){let e=!!n.split(/\s*,\s*/).find(t=>t==Qs);return e&&(n=n.replace(pi,"")),n=n.replace(/@\*/g,de).replace(/@\w+/g,t=>de+"-"+t.slice(1)).replace(/:animating/g,Me),[n,e]}function yi(n){return n?ve({},n):null}var gt=class{errors;queryCount=0;depCount=0;currentTransition=null;currentQuery=null;currentQuerySelector=null;currentAnimateTimings=null;currentTime=0;collectedStyles=new Map;options=null;unsupportedCSSPropertiesFound=new Set;constructor(e){this.errors=e}};function _i(n){if(typeof n=="string")return null;let e=null;if(Array.isArray(n))n.forEach(t=>{if(t instanceof Map&&t.has("offset")){let s=t;e=parseFloat(s.get("offset")),s.delete("offset")}});else if(n instanceof Map&&n.has("offset")){let t=n;e=parseFloat(t.get("offset")),t.delete("offset")}return e}function Si(n,e){if(n.hasOwnProperty("duration"))return n;if(typeof n=="number"){let r=me(n,e).duration;return ct(r,0,"")}let t=n;if(t.split(/\s+/).some(r=>r.charAt(0)=="{"&&r.charAt(1)=="{")){let r=ct(0,0,"");return r.dynamic=!0,r.strValue=t,r}let i=me(t,e);return ct(i.duration,i.delay,i.easing)}function Z(n){return n?(n=ve({},n),n.params&&(n.params=yi(n.params))):n={},n}function ct(n,e,t){return{duration:n,delay:e,easing:t}}function Nt(n,e,t,s,i,r,o=null,a=!1){return{type:1,element:n,keyframes:e,preStyleProps:t,postStyleProps:s,duration:i,delay:r,totalTime:i+r,easing:o,subTimeline:a}}var ge=class{_map=new Map;get(e){return this._map.get(e)||[]}append(e,t){let s=this._map.get(e);s||this._map.set(e,s=[]),s.push(...t)}has(e){return this._map.has(e)}clear(){this._map.clear()}},Ei=1,vi=":enter",Ti=new RegExp(vi,"g"),bi=":leave",wi=new RegExp(bi,"g");function js(n,e,t,s,i,r=new Map,o=new Map,a,l,u=[]){return new yt().buildKeyframes(n,e,t,s,i,r,o,a,l,u)}var yt=class{buildKeyframes(e,t,s,i,r,o,a,l,u,h=[]){u=u||new ge;let c=new _t(e,t,u,i,r,h,[]);c.options=l;let S=l.delay?V(l.delay):0;c.currentTimeline.delayNextStep(S),c.currentTimeline.setStyles([o],null,c.errors,l),F(this,s,c);let y=c.timelines.filter(d=>d.containsAnimation());if(y.length&&a.size){let d;for(let g=y.length-1;g>=0;g--){let T=y[g];if(T.element===t){d=T;break}}d&&!d.allowOnlyTimelineStyles()&&d.setStyles([a],null,c.errors,l)}return y.length?y.map(d=>d.buildKeyframes()):[Nt(t,[],[],[],0,S,"",!1)]}visitTrigger(e,t){}visitState(e,t){}visitTransition(e,t){}visitAnimateChild(e,t){let s=t.subInstructions.get(t.element);if(s){let i=t.createSubContext(e.options),r=t.currentTimeline.currentTime,o=this._visitSubInstructions(s,i,i.options);r!=o&&t.transformIntoNewTimeline(o)}t.previousNode=e}visitAnimateRef(e,t){let s=t.createSubContext(e.options);s.transformIntoNewTimeline(),this._applyAnimationRefDelays([e.options,e.animation.options],t,s),this.visitReference(e.animation,s),t.transformIntoNewTimeline(s.currentTimeline.currentTime),t.previousNode=e}_applyAnimationRefDelays(e,t,s){for(let i of e){let r=i?.delay;if(r){let o=typeof r=="number"?r:V(le(r,i?.params??{},t.errors));s.delayNextStep(o)}}}_visitSubInstructions(e,t,s){let r=t.currentTimeline.currentTime,o=s.duration!=null?V(s.duration):null,a=s.delay!=null?V(s.delay):null;return o!==0&&e.forEach(l=>{let u=t.appendInstructionToTimeline(l,o,a);r=Math.max(r,u.duration+u.delay)}),r}visitReference(e,t){t.updateOptions(e.options,!0),F(this,e.animation,t),t.previousNode=e}visitSequence(e,t){let s=t.subContextCount,i=t,r=e.options;if(r&&(r.params||r.delay)&&(i=t.createSubContext(r),i.transformIntoNewTimeline(),r.delay!=null)){i.previousNode.type==_.Style&&(i.currentTimeline.snapshotCurrentStyles(),i.previousNode=ze);let o=V(r.delay);i.delayNextStep(o)}e.steps.length&&(e.steps.forEach(o=>F(this,o,i)),i.currentTimeline.applyStylesToKeyframe(),i.subContextCount>s&&i.transformIntoNewTimeline()),t.previousNode=e}visitGroup(e,t){let s=[],i=t.currentTimeline.currentTime,r=e.options&&e.options.delay?V(e.options.delay):0;e.steps.forEach(o=>{let a=t.createSubContext(e.options);r&&a.delayNextStep(r),F(this,o,a),i=Math.max(i,a.currentTimeline.currentTime),s.push(a.currentTimeline)}),s.forEach(o=>t.currentTimeline.mergeTimelineCollectedStyles(o)),t.transformIntoNewTimeline(i),t.previousNode=e}_visitTiming(e,t){if(e.dynamic){let s=e.strValue,i=t.params?le(s,t.params,t.errors):s;return me(i,t.errors)}else return{duration:e.duration,delay:e.delay,easing:e.easing}}visitAnimate(e,t){let s=t.currentAnimateTimings=this._visitTiming(e.timings,t),i=t.currentTimeline;s.delay&&(t.incrementTime(s.delay),i.snapshotCurrentStyles());let r=e.style;r.type==_.Keyframes?this.visitKeyframes(r,t):(t.incrementTime(s.duration),this.visitStyle(r,t),i.applyStylesToKeyframe()),t.currentAnimateTimings=null,t.previousNode=e}visitStyle(e,t){let s=t.currentTimeline,i=t.currentAnimateTimings;!i&&s.hasCurrentStyleProperties()&&s.forwardFrame();let r=i&&i.easing||e.easing;e.isEmptyStep?s.applyEmptyStep(r):s.setStyles(e.styles,r,t.errors,t.options),t.previousNode=e}visitKeyframes(e,t){let s=t.currentAnimateTimings,i=t.currentTimeline.duration,r=s.duration,a=t.createSubContext().currentTimeline;a.easing=s.easing,e.styles.forEach(l=>{let u=l.offset||0;a.forwardTime(u*r),a.setStyles(l.styles,l.easing,t.errors,t.options),a.applyStylesToKeyframe()}),t.currentTimeline.mergeTimelineCollectedStyles(a),t.transformIntoNewTimeline(i+r),t.previousNode=e}visitQuery(e,t){let s=t.currentTimeline.currentTime,i=e.options||{},r=i.delay?V(i.delay):0;r&&(t.previousNode.type===_.Style||s==0&&t.currentTimeline.hasCurrentStyleProperties())&&(t.currentTimeline.snapshotCurrentStyles(),t.previousNode=ze);let o=s,a=t.invokeQuery(e.selector,e.originalSelector,e.limit,e.includeSelf,!!i.optional,t.errors);t.currentQueryTotal=a.length;let l=null;a.forEach((u,h)=>{t.currentQueryIndex=h;let c=t.createSubContext(e.options,u);r&&c.delayNextStep(r),u===t.element&&(l=c.currentTimeline),F(this,e.animation,c),c.currentTimeline.applyStylesToKeyframe();let S=c.currentTimeline.currentTime;o=Math.max(o,S)}),t.currentQueryIndex=0,t.currentQueryTotal=0,t.transformIntoNewTimeline(o),l&&(t.currentTimeline.mergeTimelineCollectedStyles(l),t.currentTimeline.snapshotCurrentStyles()),t.previousNode=e}visitStagger(e,t){let s=t.parentContext,i=t.currentTimeline,r=e.timings,o=Math.abs(r.duration),a=o*(t.currentQueryTotal-1),l=o*t.currentQueryIndex;switch(r.duration<0?"reverse":r.easing){case"reverse":l=a-l;break;case"full":l=s.currentStaggerTime;break}let h=t.currentTimeline;l&&h.delayNextStep(l);let c=h.currentTime;F(this,e.animation,t),t.previousNode=e,s.currentStaggerTime=i.currentTime-c+(i.startTime-s.currentTimeline.startTime)}},ze={},_t=class n{_driver;element;subInstructions;_enterClassName;_leaveClassName;errors;timelines;parentContext=null;currentTimeline;currentAnimateTimings=null;previousNode=ze;subContextCount=0;options={};currentQueryIndex=0;currentQueryTotal=0;currentStaggerTime=0;constructor(e,t,s,i,r,o,a,l){this._driver=e,this.element=t,this.subInstructions=s,this._enterClassName=i,this._leaveClassName=r,this.errors=o,this.timelines=a,this.currentTimeline=l||new Ke(this._driver,t,0),a.push(this.currentTimeline)}get params(){return this.options.params}updateOptions(e,t){if(!e)return;let s=e,i=this.options;s.duration!=null&&(i.duration=V(s.duration)),s.delay!=null&&(i.delay=V(s.delay));let r=s.params;if(r){let o=i.params;o||(o=this.options.params={}),Object.keys(r).forEach(a=>{(!t||!o.hasOwnProperty(a))&&(o[a]=le(r[a],o,this.errors))})}}_copyOptions(){let e={};if(this.options){let t=this.options.params;if(t){let s=e.params={};Object.keys(t).forEach(i=>{s[i]=t[i]})}}return e}createSubContext(e=null,t,s){let i=t||this.element,r=new n(this._driver,i,this.subInstructions,this._enterClassName,this._leaveClassName,this.errors,this.timelines,this.currentTimeline.fork(i,s||0));return r.previousNode=this.previousNode,r.currentAnimateTimings=this.currentAnimateTimings,r.options=this._copyOptions(),r.updateOptions(e),r.currentQueryIndex=this.currentQueryIndex,r.currentQueryTotal=this.currentQueryTotal,r.parentContext=this,this.subContextCount++,r}transformIntoNewTimeline(e){return this.previousNode=ze,this.currentTimeline=this.currentTimeline.fork(this.element,e),this.timelines.push(this.currentTimeline),this.currentTimeline}appendInstructionToTimeline(e,t,s){let i={duration:t??e.duration,delay:this.currentTimeline.currentTime+(s??0)+e.delay,easing:""},r=new St(this._driver,e.element,e.keyframes,e.preStyleProps,e.postStyleProps,i,e.stretchStartingKeyframe);return this.timelines.push(r),i}incrementTime(e){this.currentTimeline.forwardTime(this.currentTimeline.duration+e)}delayNextStep(e){e>0&&this.currentTimeline.delayNextStep(e)}invokeQuery(e,t,s,i,r,o){let a=[];if(i&&a.push(this.element),e.length>0){e=e.replace(Ti,"."+this._enterClassName),e=e.replace(wi,"."+this._leaveClassName);let l=s!=1,u=this._driver.query(this.element,e,l);s!==0&&(u=s<0?u.slice(u.length+s,u.length):u.slice(0,s)),a.push(...u)}return!r&&a.length==0&&o.push(fs(t)),a}},Ke=class n{_driver;element;startTime;_elementTimelineStylesLookup;duration=0;easing=null;_previousKeyframe=new Map;_currentKeyframe=new Map;_keyframes=new Map;_styleSummary=new Map;_localTimelineStyles=new Map;_globalTimelineStyles;_pendingStyles=new Map;_backFill=new Map;_currentEmptyStepKeyframe=null;constructor(e,t,s,i){this._driver=e,this.element=t,this.startTime=s,this._elementTimelineStylesLookup=i,this._elementTimelineStylesLookup||(this._elementTimelineStylesLookup=new Map),this._globalTimelineStyles=this._elementTimelineStylesLookup.get(t),this._globalTimelineStyles||(this._globalTimelineStyles=this._localTimelineStyles,this._elementTimelineStylesLookup.set(t,this._localTimelineStyles)),this._loadKeyframe()}containsAnimation(){switch(this._keyframes.size){case 0:return!1;case 1:return this.hasCurrentStyleProperties();default:return!0}}hasCurrentStyleProperties(){return this._currentKeyframe.size>0}get currentTime(){return this.startTime+this.duration}delayNextStep(e){let t=this._keyframes.size===1&&this._pendingStyles.size;this.duration||t?(this.forwardTime(this.currentTime+e),t&&this.snapshotCurrentStyles()):this.startTime+=e}fork(e,t){return this.applyStylesToKeyframe(),new n(this._driver,e,t||this.currentTime,this._elementTimelineStylesLookup)}_loadKeyframe(){this._currentKeyframe&&(this._previousKeyframe=this._currentKeyframe),this._currentKeyframe=this._keyframes.get(this.duration),this._currentKeyframe||(this._currentKeyframe=new Map,this._keyframes.set(this.duration,this._currentKeyframe))}forwardFrame(){this.duration+=Ei,this._loadKeyframe()}forwardTime(e){this.applyStylesToKeyframe(),this.duration=e,this._loadKeyframe()}_updateStyle(e,t){this._localTimelineStyles.set(e,t),this._globalTimelineStyles.set(e,t),this._styleSummary.set(e,{time:this.currentTime,value:t})}allowOnlyTimelineStyles(){return this._currentEmptyStepKeyframe!==this._currentKeyframe}applyEmptyStep(e){e&&this._previousKeyframe.set("easing",e);for(let[t,s]of this._globalTimelineStyles)this._backFill.set(t,s||z),this._currentKeyframe.set(t,z);this._currentEmptyStepKeyframe=this._currentKeyframe}setStyles(e,t,s,i){t&&this._previousKeyframe.set("easing",t);let r=i&&i.params||{},o=Ai(e,this._globalTimelineStyles);for(let[a,l]of o){let u=le(l,r,s);this._pendingStyles.set(a,u),this._localTimelineStyles.has(a)||this._backFill.set(a,this._globalTimelineStyles.get(a)??z),this._updateStyle(a,u)}}applyStylesToKeyframe(){this._pendingStyles.size!=0&&(this._pendingStyles.forEach((e,t)=>{this._currentKeyframe.set(t,e)}),this._pendingStyles.clear(),this._localTimelineStyles.forEach((e,t)=>{this._currentKeyframe.has(t)||this._currentKeyframe.set(t,e)}))}snapshotCurrentStyles(){for(let[e,t]of this._localTimelineStyles)this._pendingStyles.set(e,t),this._updateStyle(e,t)}getFinalKeyframe(){return this._keyframes.get(this.duration)}get properties(){let e=[];for(let t in this._currentKeyframe)e.push(t);return e}mergeTimelineCollectedStyles(e){e._styleSummary.forEach((t,s)=>{let i=this._styleSummary.get(s);(!i||t.time>i.time)&&this._updateStyle(s,t.value)})}buildKeyframes(){this.applyStylesToKeyframe();let e=new Set,t=new Set,s=this._keyframes.size===1&&this.duration===0,i=[];this._keyframes.forEach((a,l)=>{let u=new Map([...this._backFill,...a]);u.forEach((h,c)=>{h===he?e.add(c):h===z&&t.add(c)}),s||u.set("offset",l/this.duration),i.push(u)});let r=[...e.values()],o=[...t.values()];if(s){let a=i[0],l=new Map(a);a.set("offset",0),l.set("offset",1),i=[a,l]}return Nt(this.element,i,r,o,this.duration,this.startTime,this.easing,!1)}},St=class extends Ke{keyframes;preStyleProps;postStyleProps;_stretchStartingKeyframe;timings;constructor(e,t,s,i,r,o,a=!1){super(e,t,o.delay),this.keyframes=s,this.preStyleProps=i,this.postStyleProps=r,this._stretchStartingKeyframe=a,this.timings={duration:o.duration,delay:o.delay,easing:o.easing}}containsAnimation(){return this.keyframes.length>1}buildKeyframes(){let e=this.keyframes,{delay:t,duration:s,easing:i}=this.timings;if(this._stretchStartingKeyframe&&t){let r=[],o=s+t,a=t/o,l=new Map(e[0]);l.set("offset",0),r.push(l);let u=new Map(e[0]);u.set("offset",Fs(a)),r.push(u);let h=e.length-1;for(let c=1;c<=h;c++){let S=new Map(e[c]),y=S.get("offset"),d=t+y*s;S.set("offset",Fs(d/o)),r.push(S)}s=o,t=0,i="",e=r}return Nt(this.element,e,this.preStyleProps,this.postStyleProps,s,t,i,!0)}};function Fs(n,e=3){let t=Math.pow(10,e-1);return Math.round(n*t)/t}function Ai(n,e){let t=new Map,s;return n.forEach(i=>{if(i==="*"){s??=e.keys();for(let r of s)t.set(r,z)}else for(let[r,o]of i)t.set(r,o)}),t}function Os(n,e,t,s,i,r,o,a,l,u,h,c,S){return{type:0,element:n,triggerName:e,isRemovalTransition:i,fromState:t,fromStyles:r,toState:s,toStyles:o,timelines:a,queriedElements:l,preStyleProps:u,postStyleProps:h,totalTime:c,errors:S}}var ht={},qe=class{_triggerName;ast;_stateStyles;constructor(e,t,s){this._triggerName=e,this.ast=t,this._stateStyles=s}match(e,t,s,i){return Pi(this.ast.matchers,e,t,s,i)}buildStyles(e,t,s){let i=this._stateStyles.get("*");return e!==void 0&&(i=this._stateStyles.get(e?.toString())||i),i?i.buildStyles(t,s):new Map}build(e,t,s,i,r,o,a,l,u,h){let c=[],S=this.ast.options&&this.ast.options.params||ht,y=a&&a.params||ht,d=this.buildStyles(s,y,c),g=l&&l.params||ht,T=this.buildStyles(i,g,c),w=new Set,A=new Map,D=new Map,N=i==="void",te={params:$s(g,S),delay:this.ast.options?.delay},B=h?[]:js(e,t,this.ast.animation,r,o,d,T,te,u,c),C=0;return B.forEach(k=>{C=Math.max(k.duration+k.delay,C)}),c.length?Os(t,this._triggerName,s,i,N,d,T,[],[],A,D,C,c):(B.forEach(k=>{let W=k.element,se=R(A,W,new Set);k.preStyleProps.forEach(Y=>se.add(Y));let Mt=R(D,W,new Set);k.postStyleProps.forEach(Y=>Mt.add(Y)),W!==t&&w.add(W)}),Os(t,this._triggerName,s,i,N,d,T,B,[...w.values()],A,D,C))}};function Pi(n,e,t,s,i){return n.some(r=>r(e,t,s,i))}function $s(n,e){let t=ve({},e);return Object.entries(n).forEach(([s,i])=>{i!=null&&(t[s]=i)}),t}var Et=class{styles;defaultParams;normalizer;constructor(e,t,s){this.styles=e,this.defaultParams=t,this.normalizer=s}buildStyles(e,t){let s=new Map,i=$s(e,this.defaultParams);return this.styles.styles.forEach(r=>{typeof r!="string"&&r.forEach((o,a)=>{o&&(o=le(o,i,t));let l=this.normalizer.normalizePropertyName(a,t);o=this.normalizer.normalizeStyleValue(a,l,o,t),s.set(a,o)})}),s}};function Ni(n,e,t){return new vt(n,e,t)}var vt=class{name;ast;_normalizer;transitionFactories=[];fallbackTransition;states=new Map;constructor(e,t,s){this.name=e,this.ast=t,this._normalizer=s,t.states.forEach(i=>{let r=i.options&&i.options.params||{};this.states.set(i.name,new Et(i.style,r,s))}),Is(this.states,"true","1"),Is(this.states,"false","0"),t.transitions.forEach(i=>{this.transitionFactories.push(new qe(e,i,this.states))}),this.fallbackTransition=Mi(e,this.states)}get containsQueries(){return this.ast.queryCount>0}matchTransition(e,t,s,i){return this.transitionFactories.find(o=>o.match(e,t,s,i))||null}matchStyles(e,t,s){return this.fallbackTransition.buildStyles(e,t,s)}};function Mi(n,e,t){let s=[(o,a)=>!0],i={type:_.Sequence,steps:[],options:null},r={type:_.Transition,animation:i,matchers:s,options:null,queryCount:0,depCount:0};return new qe(n,r,e)}function Is(n,e,t){n.has(e)?n.has(t)||n.set(t,n.get(e)):n.has(t)&&n.set(e,n.get(t))}var Di=new ge,Tt=class{bodyNode;_driver;_normalizer;_animations=new Map;_playersById=new Map;players=[];constructor(e,t,s){this.bodyNode=e,this._driver=t,this._normalizer=s}register(e,t){let s=[],i=[],r=Vs(this._driver,t,s,i);if(s.length)throw gs(s);this._animations.set(e,r)}_buildPlayer(e,t,s){let i=e.element,r=st(this._normalizer,e.keyframes,t,s);return this._driver.animate(i,r,e.duration,e.delay,e.easing,[],!0)}create(e,t,s={}){let i=[],r=this._animations.get(e),o,a=new Map;if(r?(o=js(this._driver,t,r,at,Ne,new Map,new Map,s,Di,i),o.forEach(h=>{let c=R(a,h.element,new Map);h.postStyleProps.forEach(S=>c.set(S,null))})):(i.push(ys()),o=[]),i.length)throw _s(i);a.forEach((h,c)=>{h.forEach((S,y)=>{h.set(y,this._driver.computeStyle(c,y,z))})});let l=o.map(h=>{let c=a.get(h.element);return this._buildPlayer(h,new Map,c)}),u=$(l);return this._playersById.set(e,u),u.onDestroy(()=>this.destroy(e)),this.players.push(u),u}destroy(e){let t=this._getPlayer(e);t.destroy(),this._playersById.delete(e);let s=this.players.indexOf(t);s>=0&&this.players.splice(s,1)}_getPlayer(e){let t=this._playersById.get(e);if(!t)throw Ss(e);return t}listen(e,t,s,i){let r=Ae(t,"","","");return we(this._getPlayer(e),s,r,i),()=>{}}command(e,t,s,i){if(s=="register"){this.register(e,i[0]);return}if(s=="create"){let o=i[0]||{};this.create(e,t,o);return}let r=this._getPlayer(e);switch(s){case"play":r.play();break;case"pause":r.pause();break;case"reset":r.reset();break;case"restart":r.restart();break;case"finish":r.finish();break;case"init":r.init();break;case"setPosition":r.setPosition(parseFloat(i[0]));break;case"destroy":this.destroy(e);break}}},Ls="ng-animate-queued",Ci=".ng-animate-queued",ft="ng-animate-disabled",ki=".ng-animate-disabled",Ri="ng-star-inserted",Fi=".ng-star-inserted",Oi=[],Us={namespaceId:"",setForRemoval:!1,setForMove:!1,hasAnimation:!1,removedBeforeQueried:!1},Ii={namespaceId:"",setForMove:!1,setForRemoval:!1,hasAnimation:!1,removedBeforeQueried:!0},q="__ng_removed",ye=class{namespaceId;value;options;get params(){return this.options.params}constructor(e,t=""){this.namespaceId=t;let s=e&&e.hasOwnProperty("value"),i=s?e.value:e;if(this.value=zi(i),s){let r=e,{value:o}=r,a=Rt(r,["value"]);this.options=a}else this.options={};this.options.params||(this.options.params={})}absorbOptions(e){let t=e.params;if(t){let s=this.options.params;Object.keys(t).forEach(i=>{s[i]==null&&(s[i]=t[i])})}}},pe="void",dt=new ye(pe),bt=class{id;hostElement;_engine;players=[];_triggers=new Map;_queue=[];_elementListeners=new Map;_hostClassName;constructor(e,t,s){this.id=e,this.hostElement=t,this._engine=s,this._hostClassName="ng-tns-"+e,L(t,this._hostClassName)}listen(e,t,s,i){if(!this._triggers.has(t))throw Es(s,t);if(s==null||s.length==0)throw vs(t);if(!Ki(s))throw Ts(s,t);let r=R(this._elementListeners,e,[]),o={name:t,phase:s,callback:i};r.push(o);let a=R(this._engine.statesByElement,e,new Map);return a.has(t)||(L(e,fe),L(e,fe+"-"+t),a.set(t,dt)),()=>{this._engine.afterFlush(()=>{let l=r.indexOf(o);l>=0&&r.splice(l,1),this._triggers.has(t)||a.delete(t)})}}register(e,t){return this._triggers.has(e)?!1:(this._triggers.set(e,t),!0)}_getTrigger(e){let t=this._triggers.get(e);if(!t)throw bs(e);return t}trigger(e,t,s,i=!0){let r=this._getTrigger(t),o=new _e(this.id,t,e),a=this._engine.statesByElement.get(e);a||(L(e,fe),L(e,fe+"-"+t),this._engine.statesByElement.set(e,a=new Map));let l=a.get(t),u=new ye(s,this.id);if(!(s&&s.hasOwnProperty("value"))&&l&&u.absorbOptions(l.options),a.set(t,u),l||(l=dt),!(u.value===pe)&&l.value===u.value){if(!Qi(l.params,u.params)){let g=[],T=r.matchStyles(l.value,l.params,g),w=r.matchStyles(u.value,u.params,g);g.length?this._engine.reportError(g):this._engine.afterFlush(()=>{G(e,T),K(e,w)})}return}let S=R(this._engine.playersByElement,e,[]);S.forEach(g=>{g.namespaceId==this.id&&g.triggerName==t&&g.queued&&g.destroy()});let y=r.matchTransition(l.value,u.value,e,u.params),d=!1;if(!y){if(!i)return;y=r.fallbackTransition,d=!0}return this._engine.totalQueuedPlayers++,this._queue.push({element:e,triggerName:t,transition:y,fromState:l,toState:u,player:o,isFallbackTransition:d}),d||(L(e,Ls),o.onStart(()=>{ue(e,Ls)})),o.onDone(()=>{let g=this.players.indexOf(o);g>=0&&this.players.splice(g,1);let T=this._engine.playersByElement.get(e);if(T){let w=T.indexOf(o);w>=0&&T.splice(w,1)}}),this.players.push(o),S.push(o),o}deregister(e){this._triggers.delete(e),this._engine.statesByElement.forEach(t=>t.delete(e)),this._elementListeners.forEach((t,s)=>{this._elementListeners.set(s,t.filter(i=>i.name!=e))})}clearElementCache(e){this._engine.statesByElement.delete(e),this._elementListeners.delete(e);let t=this._engine.playersByElement.get(e);t&&(t.forEach(s=>s.destroy()),this._engine.playersByElement.delete(e))}_signalRemovalForInnerTriggers(e,t){let s=this._engine.driver.query(e,de,!0);s.forEach(i=>{if(i[q])return;let r=this._engine.fetchNamespacesByElement(i);r.size?r.forEach(o=>o.triggerLeaveAnimation(i,t,!1,!0)):this.clearElementCache(i)}),this._engine.afterFlushAnimationsDone(()=>s.forEach(i=>this.clearElementCache(i)))}triggerLeaveAnimation(e,t,s,i){let r=this._engine.statesByElement.get(e),o=new Map;if(r){let a=[];if(r.forEach((l,u)=>{if(o.set(u,l.value),this._triggers.has(u)){let h=this.trigger(e,u,pe,i);h&&a.push(h)}}),a.length)return this._engine.markElementAsRemoved(this.id,e,!0,t,o),s&&$(a).onDone(()=>this._engine.processLeaveNode(e)),!0}return!1}prepareLeaveAnimationListeners(e){let t=this._elementListeners.get(e),s=this._engine.statesByElement.get(e);if(t&&s){let i=new Set;t.forEach(r=>{let o=r.name;if(i.has(o))return;i.add(o);let l=this._triggers.get(o).fallbackTransition,u=s.get(o)||dt,h=new ye(pe),c=new _e(this.id,o,e);this._engine.totalQueuedPlayers++,this._queue.push({element:e,triggerName:o,transition:l,fromState:u,toState:h,player:c,isFallbackTransition:!0})})}}removeNode(e,t){let s=this._engine;if(e.childElementCount&&this._signalRemovalForInnerTriggers(e,t),this.triggerLeaveAnimation(e,t,!0))return;let i=!1;if(s.totalAnimations){let r=s.players.length?s.playersByQueriedElement.get(e):[];if(r&&r.length)i=!0;else{let o=e;for(;o=o.parentNode;)if(s.statesByElement.get(o)){i=!0;break}}}if(this.prepareLeaveAnimationListeners(e),i)s.markElementAsRemoved(this.id,e,!1,t);else{let r=e[q];(!r||r===Us)&&(s.afterFlush(()=>this.clearElementCache(e)),s.destroyInnerAnimations(e),s._onRemovalComplete(e,t))}}insertNode(e,t){L(e,this._hostClassName)}drainQueuedTransitions(e){let t=[];return this._queue.forEach(s=>{let i=s.player;if(i.destroyed)return;let r=s.element,o=this._elementListeners.get(r);o&&o.forEach(a=>{if(a.name==s.triggerName){let l=Ae(r,s.triggerName,s.fromState.value,s.toState.value);l._data=e,we(s.player,a.phase,l,a.callback)}}),i.markedForDestroy?this._engine.afterFlush(()=>{i.destroy()}):t.push(s)}),this._queue=[],t.sort((s,i)=>{let r=s.transition.ast.depCount,o=i.transition.ast.depCount;return r==0||o==0?r-o:this._engine.driver.containsElement(s.element,i.element)?1:-1})}destroy(e){this.players.forEach(t=>t.destroy()),this._signalRemovalForInnerTriggers(this.hostElement,e)}},wt=class{bodyNode;driver;_normalizer;players=[];newHostElements=new Map;playersByElement=new Map;playersByQueriedElement=new Map;statesByElement=new Map;disabledNodes=new Set;totalAnimations=0;totalQueuedPlayers=0;_namespaceLookup={};_namespaceList=[];_flushFns=[];_whenQuietFns=[];namespacesByHostElement=new Map;collectedEnterElements=[];collectedLeaveElements=[];onRemovalComplete=(e,t)=>{};_onRemovalComplete(e,t){this.onRemovalComplete(e,t)}constructor(e,t,s){this.bodyNode=e,this.driver=t,this._normalizer=s}get queuedPlayers(){let e=[];return this._namespaceList.forEach(t=>{t.players.forEach(s=>{s.queued&&e.push(s)})}),e}createNamespace(e,t){let s=new bt(e,t,this);return this.bodyNode&&this.driver.containsElement(this.bodyNode,t)?this._balanceNamespaceList(s,t):(this.newHostElements.set(t,s),this.collectEnterElement(t)),this._namespaceLookup[e]=s}_balanceNamespaceList(e,t){let s=this._namespaceList,i=this.namespacesByHostElement;if(s.length-1>=0){let o=!1,a=this.driver.getParentElement(t);for(;a;){let l=i.get(a);if(l){let u=s.indexOf(l);s.splice(u+1,0,e),o=!0;break}a=this.driver.getParentElement(a)}o||s.unshift(e)}else s.push(e);return i.set(t,e),e}register(e,t){let s=this._namespaceLookup[e];return s||(s=this.createNamespace(e,t)),s}registerTrigger(e,t,s){let i=this._namespaceLookup[e];i&&i.register(t,s)&&this.totalAnimations++}destroy(e,t){e&&(this.afterFlush(()=>{}),this.afterFlushAnimationsDone(()=>{let s=this._fetchNamespace(e);this.namespacesByHostElement.delete(s.hostElement);let i=this._namespaceList.indexOf(s);i>=0&&this._namespaceList.splice(i,1),s.destroy(t),delete this._namespaceLookup[e]}))}_fetchNamespace(e){return this._namespaceLookup[e]}fetchNamespacesByElement(e){let t=new Set,s=this.statesByElement.get(e);if(s){for(let i of s.values())if(i.namespaceId){let r=this._fetchNamespace(i.namespaceId);r&&t.add(r)}}return t}trigger(e,t,s,i){if(Fe(t)){let r=this._fetchNamespace(e);if(r)return r.trigger(t,s,i),!0}return!1}insertNode(e,t,s,i){if(!Fe(t))return;let r=t[q];if(r&&r.setForRemoval){r.setForRemoval=!1,r.setForMove=!0;let o=this.collectedLeaveElements.indexOf(t);o>=0&&this.collectedLeaveElements.splice(o,1)}if(e){let o=this._fetchNamespace(e);o&&o.insertNode(t,s)}i&&this.collectEnterElement(t)}collectEnterElement(e){this.collectedEnterElements.push(e)}markElementAsDisabled(e,t){t?this.disabledNodes.has(e)||(this.disabledNodes.add(e),L(e,ft)):this.disabledNodes.has(e)&&(this.disabledNodes.delete(e),ue(e,ft))}removeNode(e,t,s){if(Fe(t)){let i=e?this._fetchNamespace(e):null;i?i.removeNode(t,s):this.markElementAsRemoved(e,t,!1,s);let r=this.namespacesByHostElement.get(t);r&&r.id!==e&&r.removeNode(t,s)}else this._onRemovalComplete(t,s)}markElementAsRemoved(e,t,s,i,r){this.collectedLeaveElements.push(t),t[q]={namespaceId:e,setForRemoval:i,hasAnimation:s,removedBeforeQueried:!1,previousTriggersValues:r}}listen(e,t,s,i,r){return Fe(t)?this._fetchNamespace(e).listen(t,s,i,r):()=>{}}_buildInstruction(e,t,s,i,r){return e.transition.build(this.driver,e.element,e.fromState.value,e.toState.value,s,i,e.fromState.options,e.toState.options,t,r)}destroyInnerAnimations(e){let t=this.driver.query(e,de,!0);t.forEach(s=>this.destroyActiveAnimationsForElement(s)),this.playersByQueriedElement.size!=0&&(t=this.driver.query(e,Me,!0),t.forEach(s=>this.finishActiveQueriedAnimationOnElement(s)))}destroyActiveAnimationsForElement(e){let t=this.playersByElement.get(e);t&&t.forEach(s=>{s.queued?s.markedForDestroy=!0:s.destroy()})}finishActiveQueriedAnimationOnElement(e){let t=this.playersByQueriedElement.get(e);t&&t.forEach(s=>s.finish())}whenRenderingDone(){return new Promise(e=>{if(this.players.length)return $(this.players).onDone(()=>e());e()})}processLeaveNode(e){let t=e[q];if(t&&t.setForRemoval){if(e[q]=Us,t.namespaceId){this.destroyInnerAnimations(e);let s=this._fetchNamespace(t.namespaceId);s&&s.clearElementCache(e)}this._onRemovalComplete(e,t.setForRemoval)}e.classList?.contains(ft)&&this.markElementAsDisabled(e,!1),this.driver.query(e,ki,!0).forEach(s=>{this.markElementAsDisabled(s,!1)})}flush(e=-1){let t=[];if(this.newHostElements.size&&(this.newHostElements.forEach((s,i)=>this._balanceNamespaceList(s,i)),this.newHostElements.clear()),this.totalAnimations&&this.collectedEnterElements.length)for(let s=0;s<this.collectedEnterElements.length;s++){let i=this.collectedEnterElements[s];L(i,Ri)}if(this._namespaceList.length&&(this.totalQueuedPlayers||this.collectedLeaveElements.length)){let s=[];try{t=this._flushAnimations(s,e)}finally{for(let i=0;i<s.length;i++)s[i]()}}else for(let s=0;s<this.collectedLeaveElements.length;s++){let i=this.collectedLeaveElements[s];this.processLeaveNode(i)}if(this.totalQueuedPlayers=0,this.collectedEnterElements.length=0,this.collectedLeaveElements.length=0,this._flushFns.forEach(s=>s()),this._flushFns=[],this._whenQuietFns.length){let s=this._whenQuietFns;this._whenQuietFns=[],t.length?$(t).onDone(()=>{s.forEach(i=>i())}):s.forEach(i=>i())}}reportError(e){throw ws(e)}_flushAnimations(e,t){let s=new ge,i=[],r=new Map,o=[],a=new Map,l=new Map,u=new Map,h=new Set;this.disabledNodes.forEach(f=>{h.add(f);let m=this.driver.query(f,Ci,!0);for(let p=0;p<m.length;p++)h.add(m[p])});let c=this.bodyNode,S=Array.from(this.statesByElement.keys()),y=qs(S,this.collectedEnterElements),d=new Map,g=0;y.forEach((f,m)=>{let p=at+g++;d.set(m,p),f.forEach(E=>L(E,p))});let T=[],w=new Set,A=new Set;for(let f=0;f<this.collectedLeaveElements.length;f++){let m=this.collectedLeaveElements[f],p=m[q];p&&p.setForRemoval&&(T.push(m),w.add(m),p.hasAnimation?this.driver.query(m,Fi,!0).forEach(E=>w.add(E)):A.add(m))}let D=new Map,N=qs(S,Array.from(w));N.forEach((f,m)=>{let p=Ne+g++;D.set(m,p),f.forEach(E=>L(E,p))}),e.push(()=>{y.forEach((f,m)=>{let p=d.get(m);f.forEach(E=>ue(E,p))}),N.forEach((f,m)=>{let p=D.get(m);f.forEach(E=>ue(E,p))}),T.forEach(f=>{this.processLeaveNode(f)})});let te=[],B=[];for(let f=this._namespaceList.length-1;f>=0;f--)this._namespaceList[f].drainQueuedTransitions(t).forEach(p=>{let E=p.player,P=p.element;if(te.push(E),this.collectedEnterElements.length){let M=P[q];if(M&&M.setForMove){if(M.previousTriggersValues&&M.previousTriggersValues.has(p.triggerName)){let x=M.previousTriggersValues.get(p.triggerName),I=this.statesByElement.get(p.element);if(I&&I.has(p.triggerName)){let Ee=I.get(p.triggerName);Ee.value=x,I.set(p.triggerName,Ee)}}E.destroy();return}}let Q=!c||!this.driver.containsElement(c,P),O=D.get(P),U=d.get(P),b=this._buildInstruction(p,s,U,O,Q);if(b.errors&&b.errors.length){B.push(b);return}if(Q){E.onStart(()=>G(P,b.fromStyles)),E.onDestroy(()=>K(P,b.toStyles)),i.push(E);return}if(p.isFallbackTransition){E.onStart(()=>G(P,b.fromStyles)),E.onDestroy(()=>K(P,b.toStyles)),i.push(E);return}let kt=[];b.timelines.forEach(M=>{M.stretchStartingKeyframe=!0,this.disabledNodes.has(M.element)||kt.push(M)}),b.timelines=kt,s.append(P,b.timelines);let Xs={instruction:b,player:E,element:P};o.push(Xs),b.queriedElements.forEach(M=>R(a,M,[]).push(E)),b.preStyleProps.forEach((M,x)=>{if(M.size){let I=l.get(x);I||l.set(x,I=new Set),M.forEach((Ee,Ye)=>I.add(Ye))}}),b.postStyleProps.forEach((M,x)=>{let I=u.get(x);I||u.set(x,I=new Set),M.forEach((Ee,Ye)=>I.add(Ye))})});if(B.length){let f=[];B.forEach(m=>{f.push(As(m.triggerName,m.errors))}),te.forEach(m=>m.destroy()),this.reportError(f)}let C=new Map,k=new Map;o.forEach(f=>{let m=f.element;s.has(m)&&(k.set(m,m),this._beforeAnimationBuild(f.player.namespaceId,f.instruction,C))}),i.forEach(f=>{let m=f.element;this._getPreviousPlayers(m,!1,f.namespaceId,f.triggerName,null).forEach(E=>{R(C,m,[]).push(E),E.destroy()})});let W=T.filter(f=>Bs(f,l,u)),se=new Map;Ks(se,this.driver,A,u,z).forEach(f=>{Bs(f,l,u)&&W.push(f)});let Y=new Map;y.forEach((f,m)=>{Ks(Y,this.driver,new Set(f),l,he)}),W.forEach(f=>{let m=se.get(f),p=Y.get(f);se.set(f,new Map([...m?.entries()??[],...p?.entries()??[]]))});let We=[],Dt=[],Ct={};o.forEach(f=>{let{element:m,player:p,instruction:E}=f;if(s.has(m)){if(h.has(m)){p.onDestroy(()=>K(m,E.toStyles)),p.disabled=!0,p.overrideTotalTime(E.totalTime),i.push(p);return}let P=Ct;if(k.size>1){let O=m,U=[];for(;O=O.parentNode;){let b=k.get(O);if(b){P=b;break}U.push(O)}U.forEach(b=>k.set(b,P))}let Q=this._buildAnimation(p.namespaceId,E,C,r,Y,se);if(p.setRealPlayer(Q),P===Ct)We.push(p);else{let O=this.playersByElement.get(P);O&&O.length&&(p.parentPlayer=$(O)),i.push(p)}}else G(m,E.fromStyles),p.onDestroy(()=>K(m,E.toStyles)),Dt.push(p),h.has(m)&&i.push(p)}),Dt.forEach(f=>{let m=r.get(f.element);if(m&&m.length){let p=$(m);f.setRealPlayer(p)}}),i.forEach(f=>{f.parentPlayer?f.syncPlayerEvents(f.parentPlayer):f.destroy()});for(let f=0;f<T.length;f++){let m=T[f],p=m[q];if(ue(m,Ne),p&&p.hasAnimation)continue;let E=[];if(a.size){let Q=a.get(m);Q&&Q.length&&E.push(...Q);let O=this.driver.query(m,Me,!0);for(let U=0;U<O.length;U++){let b=a.get(O[U]);b&&b.length&&E.push(...b)}}let P=E.filter(Q=>!Q.destroyed);P.length?qi(this,m,P):this.processLeaveNode(m)}return T.length=0,We.forEach(f=>{this.players.push(f),f.onDone(()=>{f.destroy();let m=this.players.indexOf(f);this.players.splice(m,1)}),f.play()}),We}afterFlush(e){this._flushFns.push(e)}afterFlushAnimationsDone(e){this._whenQuietFns.push(e)}_getPreviousPlayers(e,t,s,i,r){let o=[];if(t){let a=this.playersByQueriedElement.get(e);a&&(o=a)}else{let a=this.playersByElement.get(e);if(a){let l=!r||r==pe;a.forEach(u=>{u.queued||!l&&u.triggerName!=i||o.push(u)})}}return(s||i)&&(o=o.filter(a=>!(s&&s!=a.namespaceId||i&&i!=a.triggerName))),o}_beforeAnimationBuild(e,t,s){let i=t.triggerName,r=t.element,o=t.isRemovalTransition?void 0:e,a=t.isRemovalTransition?void 0:i;for(let l of t.timelines){let u=l.element,h=u!==r,c=R(s,u,[]);this._getPreviousPlayers(u,h,o,a,t.toState).forEach(y=>{let d=y.getRealPlayer();d.beforeDestroy&&d.beforeDestroy(),y.destroy(),c.push(y)})}G(r,t.fromStyles)}_buildAnimation(e,t,s,i,r,o){let a=t.triggerName,l=t.element,u=[],h=new Set,c=new Set,S=t.timelines.map(d=>{let g=d.element;h.add(g);let T=g[q];if(T&&T.removedBeforeQueried)return new j(d.duration,d.delay);let w=g!==l,A=Bi((s.get(g)||Oi).map(C=>C.getRealPlayer())).filter(C=>{let k=C;return k.element?k.element===g:!1}),D=r.get(g),N=o.get(g),te=st(this._normalizer,d.keyframes,D,N),B=this._buildPlayer(d,te,A);if(d.subTimeline&&i&&c.add(g),w){let C=new _e(e,a,g);C.setRealPlayer(B),u.push(C)}return B});u.forEach(d=>{R(this.playersByQueriedElement,d.element,[]).push(d),d.onDone(()=>Li(this.playersByQueriedElement,d.element,d))}),h.forEach(d=>L(d,lt));let y=$(S);return y.onDestroy(()=>{h.forEach(d=>ue(d,lt)),K(l,t.toStyles)}),c.forEach(d=>{R(i,d,[]).push(y)}),y}_buildPlayer(e,t,s){return t.length>0?this.driver.animate(e.element,t,e.duration,e.delay,e.easing,s):new j(e.duration,e.delay)}},_e=class{namespaceId;triggerName;element;_player=new j;_containsRealPlayer=!1;_queuedCallbacks=new Map;destroyed=!1;parentPlayer=null;markedForDestroy=!1;disabled=!1;queued=!0;totalTime=0;constructor(e,t,s){this.namespaceId=e,this.triggerName=t,this.element=s}setRealPlayer(e){this._containsRealPlayer||(this._player=e,this._queuedCallbacks.forEach((t,s)=>{t.forEach(i=>we(e,s,void 0,i))}),this._queuedCallbacks.clear(),this._containsRealPlayer=!0,this.overrideTotalTime(e.totalTime),this.queued=!1)}getRealPlayer(){return this._player}overrideTotalTime(e){this.totalTime=e}syncPlayerEvents(e){let t=this._player;t.triggerCallback&&e.onStart(()=>t.triggerCallback("start")),e.onDone(()=>this.finish()),e.onDestroy(()=>this.destroy())}_queueEvent(e,t){R(this._queuedCallbacks,e,[]).push(t)}onDone(e){this.queued&&this._queueEvent("done",e),this._player.onDone(e)}onStart(e){this.queued&&this._queueEvent("start",e),this._player.onStart(e)}onDestroy(e){this.queued&&this._queueEvent("destroy",e),this._player.onDestroy(e)}init(){this._player.init()}hasStarted(){return this.queued?!1:this._player.hasStarted()}play(){!this.queued&&this._player.play()}pause(){!this.queued&&this._player.pause()}restart(){!this.queued&&this._player.restart()}finish(){this._player.finish()}destroy(){this.destroyed=!0,this._player.destroy()}reset(){!this.queued&&this._player.reset()}setPosition(e){this.queued||this._player.setPosition(e)}getPosition(){return this.queued?0:this._player.getPosition()}triggerCallback(e){let t=this._player;t.triggerCallback&&t.triggerCallback(e)}};function Li(n,e,t){let s=n.get(e);if(s){if(s.length){let i=s.indexOf(t);s.splice(i,1)}s.length==0&&n.delete(e)}return s}function zi(n){return n??null}function Fe(n){return n&&n.nodeType===1}function Ki(n){return n=="start"||n=="done"}function zs(n,e){let t=n.style.display;return n.style.display=e??"none",t}function Ks(n,e,t,s,i){let r=[];t.forEach(l=>r.push(zs(l)));let o=[];s.forEach((l,u)=>{let h=new Map;l.forEach(c=>{let S=e.computeStyle(u,c,i);h.set(c,S),(!S||S.length==0)&&(u[q]=Ii,o.push(u))}),n.set(u,h)});let a=0;return t.forEach(l=>zs(l,r[a++])),o}function qs(n,e){let t=new Map;if(n.forEach(a=>t.set(a,[])),e.length==0)return t;let s=1,i=new Set(e),r=new Map;function o(a){if(!a)return s;let l=r.get(a);if(l)return l;let u=a.parentNode;return t.has(u)?l=u:i.has(u)?l=s:l=o(u),r.set(a,l),l}return e.forEach(a=>{let l=o(a);l!==s&&t.get(l).push(a)}),t}function L(n,e){n.classList?.add(e)}function ue(n,e){n.classList?.remove(e)}function qi(n,e,t){$(t).onDone(()=>n.processLeaveNode(e))}function Bi(n){let e=[];return Hs(n,e),e}function Hs(n,e){for(let t=0;t<n.length;t++){let s=n[t];s instanceof oe?Hs(s.players,e):e.push(s)}}function Qi(n,e){let t=Object.keys(n),s=Object.keys(e);if(t.length!=s.length)return!1;for(let i=0;i<t.length;i++){let r=t[i];if(!e.hasOwnProperty(r)||n[r]!==e[r])return!1}return!0}function Bs(n,e,t){let s=t.get(n);if(!s)return!1;let i=e.get(n);return i?s.forEach(r=>i.add(r)):e.set(n,s),t.delete(n),!0}var ce=class{_driver;_normalizer;_transitionEngine;_timelineEngine;_triggerCache={};onRemovalComplete=(e,t)=>{};constructor(e,t,s){this._driver=t,this._normalizer=s,this._transitionEngine=new wt(e.body,t,s),this._timelineEngine=new Tt(e.body,t,s),this._transitionEngine.onRemovalComplete=(i,r)=>this.onRemovalComplete(i,r)}registerTrigger(e,t,s,i,r){let o=e+"-"+i,a=this._triggerCache[o];if(!a){let l=[],u=[],h=Vs(this._driver,r,l,u);if(l.length)throw ps(i,l);a=Ni(i,h,this._normalizer),this._triggerCache[o]=a}this._transitionEngine.registerTrigger(t,i,a)}register(e,t){this._transitionEngine.register(e,t)}destroy(e,t){this._transitionEngine.destroy(e,t)}onInsert(e,t,s,i){this._transitionEngine.insertNode(e,t,s,i)}onRemove(e,t,s){this._transitionEngine.removeNode(e,t,s)}disableAnimations(e,t){this._transitionEngine.markElementAsDisabled(e,t)}process(e,t,s,i){if(s.charAt(0)=="@"){let[r,o]=it(s),a=i;this._timelineEngine.command(r,t,o,a)}else this._transitionEngine.trigger(e,t,s,i)}listen(e,t,s,i,r){if(s.charAt(0)=="@"){let[o,a]=it(s);return this._timelineEngine.listen(o,t,a,r)}return this._transitionEngine.listen(e,t,s,i,r)}flush(e=-1){this._transitionEngine.flush(e)}get players(){return[...this._transitionEngine.players,...this._timelineEngine.players]}whenRenderingDone(){return this._transitionEngine.whenRenderingDone()}afterFlushAnimationsDone(e){this._transitionEngine.afterFlushAnimationsDone(e)}};function Vi(n,e){let t=null,s=null;return Array.isArray(e)&&e.length?(t=mt(e[0]),e.length>1&&(s=mt(e[e.length-1]))):e instanceof Map&&(t=mt(e)),t||s?new ji(n,t,s):null}var ji=(()=>{class n{_element;_startStyles;_endStyles;static initialStylesByElement=new WeakMap;_state=0;_initialStyles;constructor(t,s,i){this._element=t,this._startStyles=s,this._endStyles=i;let r=n.initialStylesByElement.get(t);r||n.initialStylesByElement.set(t,r=new Map),this._initialStyles=r}start(){this._state<1&&(this._startStyles&&K(this._element,this._startStyles,this._initialStyles),this._state=1)}finish(){this.start(),this._state<2&&(K(this._element,this._initialStyles),this._endStyles&&(K(this._element,this._endStyles),this._endStyles=null),this._state=1)}destroy(){this.finish(),this._state<3&&(n.initialStylesByElement.delete(this._element),this._startStyles&&(G(this._element,this._startStyles),this._endStyles=null),this._endStyles&&(G(this._element,this._endStyles),this._endStyles=null),K(this._element,this._initialStyles),this._state=3)}}return n})();function mt(n){let e=null;return n.forEach((t,s)=>{$i(s)&&(e=e||new Map,e.set(s,t))}),e}function $i(n){return n==="display"||n==="position"}var Be=class{element;keyframes;options;_specialStyles;_onDoneFns=[];_onStartFns=[];_onDestroyFns=[];_duration;_delay;_initialized=!1;_finished=!1;_started=!1;_destroyed=!1;_finalKeyframe;_originalOnDoneFns=[];_originalOnStartFns=[];domPlayer;time=0;parentPlayer=null;currentSnapshot=new Map;constructor(e,t,s,i){this.element=e,this.keyframes=t,this.options=s,this._specialStyles=i,this._duration=s.duration,this._delay=s.delay||0,this.time=this._duration+this._delay}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(e=>e()),this._onDoneFns=[])}init(){this._buildPlayer(),this._preparePlayerBeforeStart()}_buildPlayer(){if(this._initialized)return;this._initialized=!0;let e=this.keyframes;this.domPlayer=this._triggerWebAnimation(this.element,e,this.options),this._finalKeyframe=e.length?e[e.length-1]:new Map;let t=()=>this._onFinish();this.domPlayer.addEventListener("finish",t),this.onDestroy(()=>{this.domPlayer.removeEventListener("finish",t)})}_preparePlayerBeforeStart(){this._delay?this._resetDomPlayerState():this.domPlayer.pause()}_convertKeyframesToObject(e){let t=[];return e.forEach(s=>{t.push(Object.fromEntries(s))}),t}_triggerWebAnimation(e,t,s){return e.animate(this._convertKeyframesToObject(t),s)}onStart(e){this._originalOnStartFns.push(e),this._onStartFns.push(e)}onDone(e){this._originalOnDoneFns.push(e),this._onDoneFns.push(e)}onDestroy(e){this._onDestroyFns.push(e)}play(){this._buildPlayer(),this.hasStarted()||(this._onStartFns.forEach(e=>e()),this._onStartFns=[],this._started=!0,this._specialStyles&&this._specialStyles.start()),this.domPlayer.play()}pause(){this.init(),this.domPlayer.pause()}finish(){this.init(),this._specialStyles&&this._specialStyles.finish(),this._onFinish(),this.domPlayer.finish()}reset(){this._resetDomPlayerState(),this._destroyed=!1,this._finished=!1,this._started=!1,this._onStartFns=this._originalOnStartFns,this._onDoneFns=this._originalOnDoneFns}_resetDomPlayerState(){this.domPlayer&&this.domPlayer.cancel()}restart(){this.reset(),this.play()}hasStarted(){return this._started}destroy(){this._destroyed||(this._destroyed=!0,this._resetDomPlayerState(),this._onFinish(),this._specialStyles&&this._specialStyles.destroy(),this._onDestroyFns.forEach(e=>e()),this._onDestroyFns=[])}setPosition(e){this.domPlayer===void 0&&this.init(),this.domPlayer.currentTime=e*this.time}getPosition(){return+(this.domPlayer.currentTime??0)/this.time}get totalTime(){return this._delay+this._duration}beforeDestroy(){let e=new Map;this.hasStarted()&&this._finalKeyframe.forEach((s,i)=>{i!=="offset"&&e.set(i,this._finished?s:Ce(this.element,i))}),this.currentSnapshot=e}triggerCallback(e){let t=e==="start"?this._onStartFns:this._onDoneFns;t.forEach(s=>s()),t.length=0}},Qe=class{validateStyleProperty(e){return!0}validateAnimatableStyleProperty(e){return!0}containsElement(e,t){return nt(e,t)}getParentElement(e){return Pe(e)}query(e,t,s){return rt(e,t,s)}computeStyle(e,t,s){return Ce(e,t)}animate(e,t,s,i,r,o=[]){let a=i==0?"both":"forwards",l={duration:s,delay:i,fill:a};r&&(l.easing=r);let u=new Map,h=o.filter(y=>y instanceof Be);Ds(s,i)&&h.forEach(y=>{y.currentSnapshot.forEach((d,g)=>u.set(g,d))});let c=Ns(t).map(y=>new Map(y));c=Cs(e,c,u);let S=Vi(e,c);return new Be(e,c,l,S)}};var Oe="@",Gs="@.disabled",Ve=class{namespaceId;delegate;engine;_onDestroy;\u0275type=0;constructor(e,t,s,i){this.namespaceId=e,this.delegate=t,this.engine=s,this._onDestroy=i}get data(){return this.delegate.data}destroyNode(e){this.delegate.destroyNode?.(e)}destroy(){this.engine.destroy(this.namespaceId,this.delegate),this.engine.afterFlushAnimationsDone(()=>{queueMicrotask(()=>{this.delegate.destroy()})}),this._onDestroy?.()}createElement(e,t){return this.delegate.createElement(e,t)}createComment(e){return this.delegate.createComment(e)}createText(e){return this.delegate.createText(e)}appendChild(e,t){this.delegate.appendChild(e,t),this.engine.onInsert(this.namespaceId,t,e,!1)}insertBefore(e,t,s,i=!0){this.delegate.insertBefore(e,t,s),this.engine.onInsert(this.namespaceId,t,e,i)}removeChild(e,t,s){this.parentNode(t)&&this.engine.onRemove(this.namespaceId,t,this.delegate)}selectRootElement(e,t){return this.delegate.selectRootElement(e,t)}parentNode(e){return this.delegate.parentNode(e)}nextSibling(e){return this.delegate.nextSibling(e)}setAttribute(e,t,s,i){this.delegate.setAttribute(e,t,s,i)}removeAttribute(e,t,s){this.delegate.removeAttribute(e,t,s)}addClass(e,t){this.delegate.addClass(e,t)}removeClass(e,t){this.delegate.removeClass(e,t)}setStyle(e,t,s,i){this.delegate.setStyle(e,t,s,i)}removeStyle(e,t,s){this.delegate.removeStyle(e,t,s)}setProperty(e,t,s){t.charAt(0)==Oe&&t==Gs?this.disableAnimations(e,!!s):this.delegate.setProperty(e,t,s)}setValue(e,t){this.delegate.setValue(e,t)}listen(e,t,s,i){return this.delegate.listen(e,t,s,i)}disableAnimations(e,t){this.engine.disableAnimations(e,t)}},At=class extends Ve{factory;constructor(e,t,s,i,r){super(t,s,i,r),this.factory=e,this.namespaceId=t}setProperty(e,t,s){t.charAt(0)==Oe?t.charAt(1)=="."&&t==Gs?(s=s===void 0?!0:!!s,this.disableAnimations(e,s)):this.engine.process(this.namespaceId,e,t.slice(1),s):this.delegate.setProperty(e,t,s)}listen(e,t,s,i){if(t.charAt(0)==Oe){let r=Ui(e),o=t.slice(1),a="";return o.charAt(0)!=Oe&&([o,a]=Hi(o)),this.engine.listen(this.namespaceId,r,o,a,l=>{let u=l._data||-1;this.factory.scheduleListenerCallback(u,s,l)})}return this.delegate.listen(e,t,s,i)}};function Ui(n){switch(n){case"body":return document.body;case"document":return document;case"window":return window;default:return n}}function Hi(n){let e=n.indexOf("."),t=n.substring(0,e),s=n.slice(e+1);return[t,s]}var je=class{delegate;engine;_zone;_currentId=0;_microtaskId=1;_animationCallbacksBuffer=[];_rendererCache=new Map;_cdRecurDepth=0;constructor(e,t,s){this.delegate=e,this.engine=t,this._zone=s,t.onRemovalComplete=(i,r)=>{r?.removeChild(null,i)}}createRenderer(e,t){let s="",i=this.delegate.createRenderer(e,t);if(!e||!t?.data?.animation){let u=this._rendererCache,h=u.get(i);if(!h){let c=()=>u.delete(i);h=new Ve(s,i,this.engine,c),u.set(i,h)}return h}let r=t.id,o=t.id+"-"+this._currentId;this._currentId++,this.engine.register(o,e);let a=u=>{Array.isArray(u)?u.forEach(a):this.engine.registerTrigger(r,o,e,u.name,u)};return t.data.animation.forEach(a),new At(this,o,i,this.engine)}begin(){this._cdRecurDepth++,this.delegate.begin&&this.delegate.begin()}_scheduleCountTask(){queueMicrotask(()=>{this._microtaskId++})}scheduleListenerCallback(e,t,s){if(e>=0&&e<this._microtaskId){this._zone.run(()=>t(s));return}let i=this._animationCallbacksBuffer;i.length==0&&queueMicrotask(()=>{this._zone.run(()=>{i.forEach(r=>{let[o,a]=r;o(a)}),this._animationCallbacksBuffer=[]})}),i.push([t,s])}end(){this._cdRecurDepth--,this._cdRecurDepth==0&&this._zone.runOutsideAngular(()=>{this._scheduleCountTask(),this.engine.flush(this._microtaskId)}),this.delegate.end&&this.delegate.end()}whenRenderingDone(){return this.engine.whenRenderingDone()}componentReplaced(e){this.engine.flush(),this.delegate.componentReplaced?.(e)}};var Wi=(()=>{class n extends ce{constructor(t,s,i){super(t,s,i)}ngOnDestroy(){this.flush()}static \u0275fac=function(s){return new(s||n)(H(Vt),H(J),H(ee))};static \u0275prov=ie({token:n,factory:n.\u0275fac})}return n})();function Yi(){return new Ie}function xi(n,e,t){return new je(n,e,t)}var Ys=[{provide:ee,useFactory:Yi},{provide:ce,useClass:Wi},{provide:qt,useFactory:xi,deps:[jt,ce,Kt]}],Xi=[{provide:J,useClass:Pt},{provide:xe,useValue:"NoopAnimations"},...Ys],Ws=[{provide:J,useFactory:()=>new Qe},{provide:xe,useFactory:()=>"BrowserAnimations"},...Ys],xs=(()=>{class n{static withConfig(t){return{ngModule:n,providers:t.disableAnimations?Xi:Ws}}static \u0275fac=function(s){return new(s||n)};static \u0275mod=re({type:n});static \u0275inj=ne({providers:Ws,imports:[be]})}return n})();var Zi=[{path:"",redirectTo:"/auth/login",pathMatch:"full"},{path:"auth",loadChildren:()=>import("./chunk-FOSMQVTS.js").then(n=>n.AuthenticationModule)},{path:"dashboard",loadChildren:()=>import("./chunk-IPWHJHZN.js").then(n=>n.ChattrixModule)},{path:"**",redirectTo:"/auth/login"}],$e=class n{static \u0275fac=function(t){return new(t||n)};static \u0275mod=re({type:n});static \u0275inj=ne({imports:[Xe.forRoot(Zi),Xe]})};var Ue=class n{title="ChattrixFrontEnd";static \u0275fac=function(t){return new(t||n)};static \u0275cmp=Bt({type:n,selectors:[["app-root"]],standalone:!1,decls:1,vars:0,template:function(t,s){t&1&&Qt(0,"router-outlet")},dependencies:[Wt],encapsulation:2})};var He=class n{constructor(e,t,s){this.authService=e;this.authState=t;this.router=s}isRefreshing=!1;refreshTokenSubject=new Ft(null);intercept(e,t){let s=this.authService.getToken(),i=this.isApiRequest(e.url);return s&&i&&(e=this.addTokenHeader(e,s)),t.handle(e).pipe(It(r=>r instanceof Ut&&r.status===401&&i?this.handle401Error(e,t):Te(()=>r)))}addTokenHeader(e,t){return e.clone({headers:e.headers.set("Authorization",`Bearer ${t}`)})}isApiRequest(e){return e.includes("/api/")||e.includes("localhost:5000")||e.includes("your-api-domain.com")}handle401Error(e,t){return this.isRefreshing?this.refreshTokenSubject.pipe(Ot(s=>s!==null),Lt(1),zt(s=>t.handle(this.addTokenHeader(e,s)))):(this.isRefreshing=!0,this.refreshTokenSubject.next(null),this.authService.isTokenExpired()?(this.authService.logout(),this.router.navigate(["/auth/login"]),Te(()=>new Error("Token expired"))):(this.authService.logout(),this.router.navigate(["/auth/login"]),Te(()=>new Error("Authentication failed"))))}static \u0275fac=function(t){return new(t||n)(H(Xt),H(xt),H(Yt))};static \u0275prov=ie({token:n,factory:n.\u0275fac})};var Ge=class n{static \u0275fac=function(t){return new(t||n)};static \u0275mod=re({type:n,bootstrap:[Ue]});static \u0275inj=ne({providers:[{provide:Ht,useClass:He,multi:!0}],imports:[be,xs,Gt,$e]})};$t().bootstrapModule(Ge,{ngZoneEventCoalescing:!0}).catch(n=>console.error(n));
