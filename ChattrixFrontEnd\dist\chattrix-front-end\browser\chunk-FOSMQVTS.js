import{$ as Z,B as _e,C as vi,D as me,E as W,F as bi,G as Ee,H as kt,M as ve,O as D,P as ot,Q,R as xi,S as Ot,T as yi,U as Ci,V as Ft,W as wi,Y as Pi,Z as Mi,_ as Si,aa as ki,b as ne,d as _,e as di,f as oe,g as U,h as ci,i as G,j as L,k as ae,l as nt,m as mi,n as $,o as pi,p as fi,q as ui,r as ge,t as Mt,v as hi,w as St,y as gi,z as _i}from"./chunk-MSJACPQM.js";import{A as Ht,Aa as v,Ba as Jt,C as Ut,Ca as A,Cb as Fe,Da as o,E as C,Ea as a,Eb as R,Fa as h,Fb as Pt,Gb as Oi,Hb as Fi,I as J,Ia as Je,Ib as O,J as j,Ja as de,Jb as z,<PERSON> as T,<PERSON> as Ge,<PERSON> as he,La as u,M as p,Ma as y,Na as yt,O as Gt,Oa as ce,P as ee,Pa as ke,Q as te,Qa as E,R as xt,Ra as M,S as $t,Sa as S,Ta as ei,U as Me,Ua as ti,Va as $e,Wa as d,X as K,Xa as re,Ya as ue,Z as ie,Za as ii,_ as Wt,_a as Oe,a as Pe,bb as et,ca as Se,d as Lt,db as tt,e as Dt,ea as Xe,eb as Ct,f as x,fb as it,h as zt,ha as Qt,hb as wt,ia as l,ib as ri,j as Bt,ja as Zt,ka as Yt,kb as ni,l as Ze,la as Ue,ma as g,nb as oi,oa as Kt,ob as H,p as Ye,pb as rt,q as Nt,qa as w,r as Ke,ra as q,rb as ai,sa as B,t as Vt,ta as Xt,ua as f,v as He,wb as si,x as jt,xa as k,y as qt,ya as c,yb as li,za as P}from"./chunk-UM6LOK3U.js";var Et=class{_box;_destroyed=new x;_resizeSubject=new x;_resizeObserver;_elementObservables=new Map;constructor(r){this._box=r,typeof ResizeObserver<"u"&&(this._resizeObserver=new ResizeObserver(e=>this._resizeSubject.next(e)))}observe(r){return this._elementObservables.has(r)||this._elementObservables.set(r,new Dt(e=>{let t=this._resizeSubject.subscribe(e);return this._resizeObserver?.observe(r,{box:this._box}),()=>{this._resizeObserver?.unobserve(r),t.unsubscribe(),this._elementObservables.delete(r)}}).pipe(Ke(e=>e.some(t=>t.target===r)),Ht({bufferSize:1,refCount:!0}),C(this._destroyed))),this._elementObservables.get(r)}destroy(){this._destroyed.next(),this._destroyed.complete(),this._resizeSubject.complete(),this._elementObservables.clear()}},Ei=(()=>{class i{_cleanupErrorListener;_observers=new Map;_ngZone=p(K);constructor(){typeof ResizeObserver<"u"}ngOnDestroy(){for(let[,e]of this._observers)e.destroy();this._observers.clear(),this._cleanupErrorListener?.()}observe(e,t){let n=t?.box||"content-box";return this._observers.has(n)||this._observers.set(n,new Et(n)),this._observers.get(n).observe(e)}static \u0275fac=function(t){return new(t||i)};static \u0275prov=J({token:i,factory:i.\u0275fac,providedIn:"root"})}return i})();var ir=["notch"],rr=["matFormFieldNotchedOutline",""],nr=["*"],or=["textField"],ar=["iconPrefixContainer"],sr=["textPrefixContainer"],lr=["iconSuffixContainer"],dr=["textSuffixContainer"],cr=["*",[["mat-label"]],[["","matPrefix",""],["","matIconPrefix",""]],[["","matTextPrefix",""]],[["","matTextSuffix",""]],[["","matSuffix",""],["","matIconSuffix",""]],[["mat-error"],["","matError",""]],[["mat-hint",3,"align","end"]],[["mat-hint","align","end"]]],mr=["*","mat-label","[matPrefix], [matIconPrefix]","[matTextPrefix]","[matTextSuffix]","[matSuffix], [matIconSuffix]","mat-error, [matError]","mat-hint:not([align='end'])","mat-hint[align='end']"];function pr(i,r){i&1&&h(0,"span",20)}function fr(i,r){if(i&1&&(o(0,"label",19),ce(1,1),f(2,pr,1,0,"span",20),a()),i&2){let e=y(2);c("floating",e._shouldLabelFloat())("monitorResize",e._hasOutline())("id",e._labelId),k("for",e._control.disableAutomaticLabeling?null:e._control.id),l(2),A(!e.hideRequiredMarker&&e._control.required?2:-1)}}function ur(i,r){if(i&1&&f(0,fr,3,5,"label",19),i&2){let e=y();A(e._hasFloatingLabel()?0:-1)}}function hr(i,r){i&1&&h(0,"div",7)}function gr(i,r){}function _r(i,r){if(i&1&&f(0,gr,0,0,"ng-template",13),i&2){y(2);let e=$e(1);c("ngTemplateOutlet",e)}}function vr(i,r){if(i&1&&(o(0,"div",9),f(1,_r,1,1,null,13),a()),i&2){let e=y();c("matFormFieldNotchedOutlineOpen",e._shouldLabelFloat()),l(),A(e._forceDisplayInfixLabel()?-1:1)}}function br(i,r){i&1&&(o(0,"div",10,2),ce(2,2),a())}function xr(i,r){i&1&&(o(0,"div",11,3),ce(2,3),a())}function yr(i,r){}function Cr(i,r){if(i&1&&f(0,yr,0,0,"ng-template",13),i&2){y();let e=$e(1);c("ngTemplateOutlet",e)}}function wr(i,r){i&1&&(o(0,"div",14,4),ce(2,4),a())}function Pr(i,r){i&1&&(o(0,"div",15,5),ce(2,5),a())}function Mr(i,r){i&1&&h(0,"div",16)}function Sr(i,r){i&1&&ce(0,6)}function kr(i,r){if(i&1&&(o(0,"mat-hint",21),d(1),a()),i&2){let e=y(2);c("id",e._hintLabelId),l(),re(e.hintLabel)}}function Or(i,r){if(i&1&&(f(0,kr,2,2,"mat-hint",21),ce(1,7),h(2,"div",22),ce(3,8)),i&2){let e=y();A(e.hintLabel?0:-1)}}var N=(()=>{class i{static \u0275fac=function(t){return new(t||i)};static \u0275dir=B({type:i,selectors:[["mat-label"]]})}return i})(),It=new T("MatError"),pe=(()=>{class i{id=p(_e).getId("mat-mdc-error-");constructor(){}static \u0275fac=function(t){return new(t||i)};static \u0275dir=B({type:i,selectors:[["mat-error"],["","matError",""]],hostAttrs:[1,"mat-mdc-form-field-error","mat-mdc-form-field-bottom-align"],hostVars:1,hostBindings:function(t,n){t&2&&Ge("id",n.id)},inputs:{id:"id"},features:[Oe([{provide:It,useExisting:i}])]})}return i})(),We=(()=>{class i{align="start";id=p(_e).getId("mat-mdc-hint-");static \u0275fac=function(t){return new(t||i)};static \u0275dir=B({type:i,selectors:[["mat-hint"]],hostAttrs:[1,"mat-mdc-form-field-hint","mat-mdc-form-field-bottom-align"],hostVars:4,hostBindings:function(t,n){t&2&&(Ge("id",n.id),k("align",null),v("mat-mdc-form-field-hint-end",n.align==="end"))},inputs:{align:"align",id:"id"}})}return i})(),zi=new T("MatPrefix");var Tt=new T("MatSuffix"),fe=(()=>{class i{set _isTextSelector(e){this._isText=!0}_isText=!1;static \u0275fac=function(t){return new(t||i)};static \u0275dir=B({type:i,selectors:[["","matSuffix",""],["","matIconSuffix",""],["","matTextSuffix",""]],inputs:{_isTextSelector:[0,"matTextSuffix","_isTextSelector"]},features:[Oe([{provide:Tt,useExisting:i}])]})}return i})(),Bi=new T("FloatingLabelParent"),Ii=(()=>{class i{_elementRef=p(ie);get floating(){return this._floating}set floating(e){this._floating=e,this.monitorResize&&this._handleResize()}_floating=!1;get monitorResize(){return this._monitorResize}set monitorResize(e){this._monitorResize=e,this._monitorResize?this._subscribeToResize():this._resizeSubscription.unsubscribe()}_monitorResize=!1;_resizeObserver=p(Ei);_ngZone=p(K);_parent=p(Bi);_resizeSubscription=new Lt;constructor(){}ngOnDestroy(){this._resizeSubscription.unsubscribe()}getWidth(){return Fr(this._elementRef.nativeElement)}get element(){return this._elementRef.nativeElement}_handleResize(){setTimeout(()=>this._parent._handleLabelResized())}_subscribeToResize(){this._resizeSubscription.unsubscribe(),this._ngZone.runOutsideAngular(()=>{this._resizeSubscription=this._resizeObserver.observe(this._elementRef.nativeElement,{box:"border-box"}).subscribe(()=>this._handleResize())})}static \u0275fac=function(t){return new(t||i)};static \u0275dir=B({type:i,selectors:[["label","matFormFieldFloatingLabel",""]],hostAttrs:[1,"mdc-floating-label","mat-mdc-floating-label"],hostVars:2,hostBindings:function(t,n){t&2&&v("mdc-floating-label--float-above",n.floating)},inputs:{floating:"floating",monitorResize:"monitorResize"}})}return i})();function Fr(i){let r=i;if(r.offsetParent!==null)return r.scrollWidth;let e=r.cloneNode(!0);e.style.setProperty("position","absolute"),e.style.setProperty("transform","translate(-9999px, -9999px)"),document.documentElement.appendChild(e);let t=e.scrollWidth;return e.remove(),t}var Ti="mdc-line-ripple--active",at="mdc-line-ripple--deactivating",Ai=(()=>{class i{_elementRef=p(ie);_cleanupTransitionEnd;constructor(){let e=p(K),t=p(Ue);e.runOutsideAngular(()=>{this._cleanupTransitionEnd=t.listen(this._elementRef.nativeElement,"transitionend",this._handleTransitionEnd)})}activate(){let e=this._elementRef.nativeElement.classList;e.remove(at),e.add(Ti)}deactivate(){this._elementRef.nativeElement.classList.add(at)}_handleTransitionEnd=e=>{let t=this._elementRef.nativeElement.classList,n=t.contains(at);e.propertyName==="opacity"&&n&&t.remove(Ti,at)};ngOnDestroy(){this._cleanupTransitionEnd()}static \u0275fac=function(t){return new(t||i)};static \u0275dir=B({type:i,selectors:[["div","matFormFieldLineRipple",""]],hostAttrs:[1,"mdc-line-ripple"]})}return i})(),Ri=(()=>{class i{_elementRef=p(ie);_ngZone=p(K);open=!1;_notch;constructor(){}ngAfterViewInit(){let e=this._elementRef.nativeElement.querySelector(".mdc-floating-label");e?(this._elementRef.nativeElement.classList.add("mdc-notched-outline--upgraded"),typeof requestAnimationFrame=="function"&&(e.style.transitionDuration="0s",this._ngZone.runOutsideAngular(()=>{requestAnimationFrame(()=>e.style.transitionDuration="")}))):this._elementRef.nativeElement.classList.add("mdc-notched-outline--no-label")}_setNotchWidth(e){!this.open||!e?this._notch.nativeElement.style.width="":this._notch.nativeElement.style.width=`calc(${e}px * var(--mat-mdc-form-field-floating-label-scale, 0.75) + 9px)`}static \u0275fac=function(t){return new(t||i)};static \u0275cmp=w({type:i,selectors:[["div","matFormFieldNotchedOutline",""]],viewQuery:function(t,n){if(t&1&&E(ir,5),t&2){let s;M(s=S())&&(n._notch=s.first)}},hostAttrs:[1,"mdc-notched-outline"],hostVars:2,hostBindings:function(t,n){t&2&&v("mdc-notched-outline--notched",n.open)},inputs:{open:[0,"matFormFieldNotchedOutlineOpen","open"]},attrs:rr,ngContentSelectors:nr,decls:5,vars:0,consts:[["notch",""],[1,"mat-mdc-notch-piece","mdc-notched-outline__leading"],[1,"mat-mdc-notch-piece","mdc-notched-outline__notch"],[1,"mat-mdc-notch-piece","mdc-notched-outline__trailing"]],template:function(t,n){t&1&&(yt(),h(0,"div",1),o(1,"div",2,0),ce(3),a(),h(4,"div",3))},encapsulation:2,changeDetection:0})}return i})(),st=(()=>{class i{value;stateChanges;id;placeholder;ngControl;focused;empty;shouldLabelFloat;required;disabled;errorState;controlType;autofilled;userAriaDescribedBy;disableAutomaticLabeling;static \u0275fac=function(t){return new(t||i)};static \u0275dir=B({type:i})}return i})();var lt=new T("MatFormField"),Ni=new T("MAT_FORM_FIELD_DEFAULT_OPTIONS"),Li="fill",Er="auto",Di="fixed",Ir="translateY(-50%)",X=(()=>{class i{_elementRef=p(ie);_changeDetectorRef=p(tt);_dir=p(vi);_platform=p(ge);_idGenerator=p(_e);_ngZone=p(K);_injector=p(Me);_defaults=p(Ni,{optional:!0});_textField;_iconPrefixContainer;_textPrefixContainer;_iconSuffixContainer;_textSuffixContainer;_floatingLabel;_notchedOutline;_lineRipple;_formFieldControl;_prefixChildren;_suffixChildren;_errorChildren;_hintChildren;_labelChild=Kt(N);get hideRequiredMarker(){return this._hideRequiredMarker}set hideRequiredMarker(e){this._hideRequiredMarker=Ee(e)}_hideRequiredMarker=!1;color="primary";get floatLabel(){return this._floatLabel||this._defaults?.floatLabel||Er}set floatLabel(e){e!==this._floatLabel&&(this._floatLabel=e,this._changeDetectorRef.markForCheck())}_floatLabel;get appearance(){return this._appearance}set appearance(e){let t=this._appearance,n=e||this._defaults?.appearance||Li;this._appearance=n,this._appearance==="outline"&&this._appearance!==t&&(this._needsOutlineLabelOffsetUpdate=!0)}_appearance=Li;get subscriptSizing(){return this._subscriptSizing||this._defaults?.subscriptSizing||Di}set subscriptSizing(e){this._subscriptSizing=e||this._defaults?.subscriptSizing||Di}_subscriptSizing=null;get hintLabel(){return this._hintLabel}set hintLabel(e){this._hintLabel=e,this._processHints()}_hintLabel="";_hasIconPrefix=!1;_hasTextPrefix=!1;_hasIconSuffix=!1;_hasTextSuffix=!1;_labelId=this._idGenerator.getId("mat-mdc-form-field-label-");_hintLabelId=this._idGenerator.getId("mat-mdc-hint-");get _control(){return this._explicitFormFieldControl||this._formFieldControl}set _control(e){this._explicitFormFieldControl=e}_destroyed=new x;_isFocused=null;_explicitFormFieldControl;_needsOutlineLabelOffsetUpdate=!1;_previousControl=null;_previousControlValidatorFn=null;_stateChanges;_valueChanges;_describedByChanges;_animationsDisabled;constructor(){let e=this._defaults;e&&(e.appearance&&(this.appearance=e.appearance),this._hideRequiredMarker=!!e?.hideRequiredMarker,e.color&&(this.color=e.color)),this._animationsDisabled=p(Se,{optional:!0})==="NoopAnimations"}ngAfterViewInit(){this._updateFocusState(),this._animationsDisabled||this._ngZone.runOutsideAngular(()=>{setTimeout(()=>{this._elementRef.nativeElement.classList.add("mat-form-field-animations-enabled")},300)}),this._changeDetectorRef.detectChanges()}ngAfterContentInit(){this._assertFormFieldControl(),this._initializeSubscript(),this._initializePrefixAndSuffix(),this._initializeOutlineLabelOffsetSubscriptions()}ngAfterContentChecked(){this._assertFormFieldControl(),this._control!==this._previousControl&&(this._initializeControl(this._previousControl),this._control.ngControl&&this._control.ngControl.control&&(this._previousControlValidatorFn=this._control.ngControl.control.validator),this._previousControl=this._control),this._control.ngControl&&this._control.ngControl.control&&this._control.ngControl.control.validator!==this._previousControlValidatorFn&&this._changeDetectorRef.markForCheck()}ngOnDestroy(){this._stateChanges?.unsubscribe(),this._valueChanges?.unsubscribe(),this._describedByChanges?.unsubscribe(),this._destroyed.next(),this._destroyed.complete()}getLabelId=wt(()=>this._hasFloatingLabel()?this._labelId:null);getConnectedOverlayOrigin(){return this._textField||this._elementRef}_animateAndLockLabel(){this._hasFloatingLabel()&&(this.floatLabel="always")}_initializeControl(e){let t=this._control,n="mat-mdc-form-field-type-";e&&this._elementRef.nativeElement.classList.remove(n+e.controlType),t.controlType&&this._elementRef.nativeElement.classList.add(n+t.controlType),this._stateChanges?.unsubscribe(),this._stateChanges=t.stateChanges.subscribe(()=>{this._updateFocusState(),this._changeDetectorRef.markForCheck()}),this._describedByChanges?.unsubscribe(),this._describedByChanges=t.stateChanges.pipe(Ut([void 0,void 0]),Ze(()=>[t.errorState,t.userAriaDescribedBy]),qt(),Ke(([[s,m],[b,I]])=>s!==b||m!==I)).subscribe(()=>this._syncDescribedByIds()),this._valueChanges?.unsubscribe(),t.ngControl&&t.ngControl.valueChanges&&(this._valueChanges=t.ngControl.valueChanges.pipe(C(this._destroyed)).subscribe(()=>this._changeDetectorRef.markForCheck()))}_checkPrefixAndSuffixTypes(){this._hasIconPrefix=!!this._prefixChildren.find(e=>!e._isText),this._hasTextPrefix=!!this._prefixChildren.find(e=>e._isText),this._hasIconSuffix=!!this._suffixChildren.find(e=>!e._isText),this._hasTextSuffix=!!this._suffixChildren.find(e=>e._isText)}_initializePrefixAndSuffix(){this._checkPrefixAndSuffixTypes(),Nt(this._prefixChildren.changes,this._suffixChildren.changes).subscribe(()=>{this._checkPrefixAndSuffixTypes(),this._changeDetectorRef.markForCheck()})}_initializeSubscript(){this._hintChildren.changes.subscribe(()=>{this._processHints(),this._changeDetectorRef.markForCheck()}),this._errorChildren.changes.subscribe(()=>{this._syncDescribedByIds(),this._changeDetectorRef.markForCheck()}),this._validateHints(),this._syncDescribedByIds()}_assertFormFieldControl(){this._control}_updateFocusState(){this._control.focused&&!this._isFocused?(this._isFocused=!0,this._lineRipple?.activate()):!this._control.focused&&(this._isFocused||this._isFocused===null)&&(this._isFocused=!1,this._lineRipple?.deactivate()),this._textField?.nativeElement.classList.toggle("mdc-text-field--focused",this._control.focused)}_initializeOutlineLabelOffsetSubscriptions(){this._prefixChildren.changes.subscribe(()=>this._needsOutlineLabelOffsetUpdate=!0),Xe(()=>{this._needsOutlineLabelOffsetUpdate&&(this._needsOutlineLabelOffsetUpdate=!1,this._updateOutlineLabelOffset())},{injector:this._injector}),this._dir.change.pipe(C(this._destroyed)).subscribe(()=>this._needsOutlineLabelOffsetUpdate=!0)}_shouldAlwaysFloat(){return this.floatLabel==="always"}_hasOutline(){return this.appearance==="outline"}_forceDisplayInfixLabel(){return!this._platform.isBrowser&&this._prefixChildren.length&&!this._shouldLabelFloat()}_hasFloatingLabel=wt(()=>!!this._labelChild());_shouldLabelFloat(){return this._hasFloatingLabel()?this._control.shouldLabelFloat||this._shouldAlwaysFloat():!1}_shouldForward(e){let t=this._control?this._control.ngControl:null;return t&&t[e]}_getSubscriptMessageType(){return this._errorChildren&&this._errorChildren.length>0&&this._control.errorState?"error":"hint"}_handleLabelResized(){this._refreshOutlineNotchWidth()}_refreshOutlineNotchWidth(){!this._hasOutline()||!this._floatingLabel||!this._shouldLabelFloat()?this._notchedOutline?._setNotchWidth(0):this._notchedOutline?._setNotchWidth(this._floatingLabel.getWidth())}_processHints(){this._validateHints(),this._syncDescribedByIds()}_validateHints(){this._hintChildren}_syncDescribedByIds(){if(this._control){let e=[];if(this._control.userAriaDescribedBy&&typeof this._control.userAriaDescribedBy=="string"&&e.push(...this._control.userAriaDescribedBy.split(" ")),this._getSubscriptMessageType()==="hint"){let t=this._hintChildren?this._hintChildren.find(s=>s.align==="start"):null,n=this._hintChildren?this._hintChildren.find(s=>s.align==="end"):null;t?e.push(t.id):this._hintLabel&&e.push(this._hintLabelId),n&&e.push(n.id)}else this._errorChildren&&e.push(...this._errorChildren.map(t=>t.id));this._control.setDescribedByIds(e)}}_updateOutlineLabelOffset(){if(!this._hasOutline()||!this._floatingLabel)return;let e=this._floatingLabel.element;if(!(this._iconPrefixContainer||this._textPrefixContainer)){e.style.transform="";return}if(!this._isAttachedToDom()){this._needsOutlineLabelOffsetUpdate=!0;return}let t=this._iconPrefixContainer?.nativeElement,n=this._textPrefixContainer?.nativeElement,s=this._iconSuffixContainer?.nativeElement,m=this._textSuffixContainer?.nativeElement,b=t?.getBoundingClientRect().width??0,I=n?.getBoundingClientRect().width??0,le=s?.getBoundingClientRect().width??0,bt=m?.getBoundingClientRect().width??0,Xi=this._dir.value==="rtl"?"-1":"1",Ji=`${b+I}px`,er=`calc(${Xi} * (${Ji} + var(--mat-mdc-form-field-label-offset-x, 0px)))`;e.style.transform=`var(
        --mat-mdc-form-field-label-transform,
        ${Ir} translateX(${er})
    )`;let tr=b+I+le+bt;this._elementRef.nativeElement.style.setProperty("--mat-form-field-notch-max-width",`calc(100% - ${tr}px)`)}_isAttachedToDom(){let e=this._elementRef.nativeElement;if(e.getRootNode){let t=e.getRootNode();return t&&t!==e}return document.documentElement.contains(e)}static \u0275fac=function(t){return new(t||i)};static \u0275cmp=w({type:i,selectors:[["mat-form-field"]],contentQueries:function(t,n,s){if(t&1&&(ei(s,n._labelChild,N,5),ke(s,st,5),ke(s,zi,5),ke(s,Tt,5),ke(s,It,5),ke(s,We,5)),t&2){ti();let m;M(m=S())&&(n._formFieldControl=m.first),M(m=S())&&(n._prefixChildren=m),M(m=S())&&(n._suffixChildren=m),M(m=S())&&(n._errorChildren=m),M(m=S())&&(n._hintChildren=m)}},viewQuery:function(t,n){if(t&1&&(E(or,5),E(ar,5),E(sr,5),E(lr,5),E(dr,5),E(Ii,5),E(Ri,5),E(Ai,5)),t&2){let s;M(s=S())&&(n._textField=s.first),M(s=S())&&(n._iconPrefixContainer=s.first),M(s=S())&&(n._textPrefixContainer=s.first),M(s=S())&&(n._iconSuffixContainer=s.first),M(s=S())&&(n._textSuffixContainer=s.first),M(s=S())&&(n._floatingLabel=s.first),M(s=S())&&(n._notchedOutline=s.first),M(s=S())&&(n._lineRipple=s.first)}},hostAttrs:[1,"mat-mdc-form-field"],hostVars:40,hostBindings:function(t,n){t&2&&v("mat-mdc-form-field-label-always-float",n._shouldAlwaysFloat())("mat-mdc-form-field-has-icon-prefix",n._hasIconPrefix)("mat-mdc-form-field-has-icon-suffix",n._hasIconSuffix)("mat-form-field-invalid",n._control.errorState)("mat-form-field-disabled",n._control.disabled)("mat-form-field-autofilled",n._control.autofilled)("mat-form-field-appearance-fill",n.appearance=="fill")("mat-form-field-appearance-outline",n.appearance=="outline")("mat-form-field-hide-placeholder",n._hasFloatingLabel()&&!n._shouldLabelFloat())("mat-focused",n._control.focused)("mat-primary",n.color!=="accent"&&n.color!=="warn")("mat-accent",n.color==="accent")("mat-warn",n.color==="warn")("ng-untouched",n._shouldForward("untouched"))("ng-touched",n._shouldForward("touched"))("ng-pristine",n._shouldForward("pristine"))("ng-dirty",n._shouldForward("dirty"))("ng-valid",n._shouldForward("valid"))("ng-invalid",n._shouldForward("invalid"))("ng-pending",n._shouldForward("pending"))},inputs:{hideRequiredMarker:"hideRequiredMarker",color:"color",floatLabel:"floatLabel",appearance:"appearance",subscriptSizing:"subscriptSizing",hintLabel:"hintLabel"},exportAs:["matFormField"],features:[Oe([{provide:lt,useExisting:i},{provide:Bi,useExisting:i}])],ngContentSelectors:mr,decls:20,vars:25,consts:[["labelTemplate",""],["textField",""],["iconPrefixContainer",""],["textPrefixContainer",""],["textSuffixContainer",""],["iconSuffixContainer",""],[1,"mat-mdc-text-field-wrapper","mdc-text-field",3,"click"],[1,"mat-mdc-form-field-focus-overlay"],[1,"mat-mdc-form-field-flex"],["matFormFieldNotchedOutline","",3,"matFormFieldNotchedOutlineOpen"],[1,"mat-mdc-form-field-icon-prefix"],[1,"mat-mdc-form-field-text-prefix"],[1,"mat-mdc-form-field-infix"],[3,"ngTemplateOutlet"],[1,"mat-mdc-form-field-text-suffix"],[1,"mat-mdc-form-field-icon-suffix"],["matFormFieldLineRipple",""],[1,"mat-mdc-form-field-subscript-wrapper","mat-mdc-form-field-bottom-align"],["aria-atomic","true","aria-live","polite"],["matFormFieldFloatingLabel","",3,"floating","monitorResize","id"],["aria-hidden","true",1,"mat-mdc-form-field-required-marker","mdc-floating-label--required"],[3,"id"],[1,"mat-mdc-form-field-hint-spacer"]],template:function(t,n){if(t&1){let s=de();yt(cr),f(0,ur,1,1,"ng-template",null,0,et),o(2,"div",6,1),u("click",function(b){return ee(s),te(n._control.onContainerClick(b))}),f(4,hr,1,0,"div",7),o(5,"div",8),f(6,vr,2,2,"div",9)(7,br,3,0,"div",10)(8,xr,3,0,"div",11),o(9,"div",12),f(10,Cr,1,1,null,13),ce(11),a(),f(12,wr,3,0,"div",14)(13,Pr,3,0,"div",15),a(),f(14,Mr,1,0,"div",16),a(),o(15,"div",17),ii(16),o(17,"div",18),f(18,Sr,1,0)(19,Or,4,1),a()()}if(t&2){let s;l(2),v("mdc-text-field--filled",!n._hasOutline())("mdc-text-field--outlined",n._hasOutline())("mdc-text-field--no-label",!n._hasFloatingLabel())("mdc-text-field--disabled",n._control.disabled)("mdc-text-field--invalid",n._control.errorState),l(2),A(!n._hasOutline()&&!n._control.disabled?4:-1),l(2),A(n._hasOutline()?6:-1),l(),A(n._hasIconPrefix?7:-1),l(),A(n._hasTextPrefix?8:-1),l(2),A(!n._hasOutline()||n._forceDisplayInfixLabel()?10:-1),l(2),A(n._hasTextSuffix?12:-1),l(),A(n._hasIconSuffix?13:-1),l(),A(n._hasOutline()?-1:14),l(),v("mat-mdc-form-field-subscript-dynamic-size",n.subscriptSizing==="dynamic");let m=n._getSubscriptMessageType();l(2),v("mat-mdc-form-field-error-wrapper",m==="error")("mat-mdc-form-field-hint-wrapper",m==="hint"),l(),A((s=m)==="error"?18:s==="hint"?19:-1)}},dependencies:[Ii,Ri,rt,Ai,We],styles:[`.mdc-text-field{display:inline-flex;align-items:baseline;padding:0 16px;position:relative;box-sizing:border-box;overflow:hidden;will-change:opacity,transform,color;border-top-left-radius:4px;border-top-right-radius:4px;border-bottom-right-radius:0;border-bottom-left-radius:0}.mdc-text-field__input{width:100%;min-width:0;border:none;border-radius:0;background:none;padding:0;-moz-appearance:none;-webkit-appearance:none;height:28px}.mdc-text-field__input::-webkit-calendar-picker-indicator{display:none}.mdc-text-field__input::-ms-clear{display:none}.mdc-text-field__input:focus{outline:none}.mdc-text-field__input:invalid{box-shadow:none}.mdc-text-field__input::placeholder{opacity:0}.mdc-text-field__input::-moz-placeholder{opacity:0}.mdc-text-field__input::-webkit-input-placeholder{opacity:0}.mdc-text-field__input:-ms-input-placeholder{opacity:0}.mdc-text-field--no-label .mdc-text-field__input::placeholder,.mdc-text-field--focused .mdc-text-field__input::placeholder{opacity:1}.mdc-text-field--no-label .mdc-text-field__input::-moz-placeholder,.mdc-text-field--focused .mdc-text-field__input::-moz-placeholder{opacity:1}.mdc-text-field--no-label .mdc-text-field__input::-webkit-input-placeholder,.mdc-text-field--focused .mdc-text-field__input::-webkit-input-placeholder{opacity:1}.mdc-text-field--no-label .mdc-text-field__input:-ms-input-placeholder,.mdc-text-field--focused .mdc-text-field__input:-ms-input-placeholder{opacity:1}.mdc-text-field--disabled:not(.mdc-text-field--no-label) .mdc-text-field__input.mat-mdc-input-disabled-interactive::placeholder{opacity:0}.mdc-text-field--disabled:not(.mdc-text-field--no-label) .mdc-text-field__input.mat-mdc-input-disabled-interactive::-moz-placeholder{opacity:0}.mdc-text-field--disabled:not(.mdc-text-field--no-label) .mdc-text-field__input.mat-mdc-input-disabled-interactive::-webkit-input-placeholder{opacity:0}.mdc-text-field--disabled:not(.mdc-text-field--no-label) .mdc-text-field__input.mat-mdc-input-disabled-interactive:-ms-input-placeholder{opacity:0}.mdc-text-field--outlined .mdc-text-field__input,.mdc-text-field--filled.mdc-text-field--no-label .mdc-text-field__input{height:100%}.mdc-text-field--outlined .mdc-text-field__input{display:flex;border:none !important;background-color:rgba(0,0,0,0)}.mdc-text-field--disabled .mdc-text-field__input{pointer-events:auto}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input{color:var(--mdc-filled-text-field-input-text-color, var(--mat-sys-on-surface));caret-color:var(--mdc-filled-text-field-caret-color, var(--mat-sys-primary))}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input::placeholder{color:var(--mdc-filled-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input::-moz-placeholder{color:var(--mdc-filled-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input::-webkit-input-placeholder{color:var(--mdc-filled-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input:-ms-input-placeholder{color:var(--mdc-filled-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-text-field__input{caret-color:var(--mdc-filled-text-field-error-caret-color)}.mdc-text-field--filled.mdc-text-field--disabled .mdc-text-field__input{color:var(--mdc-filled-text-field-disabled-input-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input{color:var(--mdc-outlined-text-field-input-text-color, var(--mat-sys-on-surface));caret-color:var(--mdc-outlined-text-field-caret-color, var(--mat-sys-primary))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input::placeholder{color:var(--mdc-outlined-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input::-moz-placeholder{color:var(--mdc-outlined-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input::-webkit-input-placeholder{color:var(--mdc-outlined-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input:-ms-input-placeholder{color:var(--mdc-outlined-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-text-field__input{caret-color:var(--mdc-outlined-text-field-error-caret-color)}.mdc-text-field--outlined.mdc-text-field--disabled .mdc-text-field__input{color:var(--mdc-outlined-text-field-disabled-input-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}@media(forced-colors: active){.mdc-text-field--disabled .mdc-text-field__input{background-color:Window}}.mdc-text-field--filled{height:56px;border-bottom-right-radius:0;border-bottom-left-radius:0;border-top-left-radius:var(--mdc-filled-text-field-container-shape, var(--mat-sys-corner-extra-small));border-top-right-radius:var(--mdc-filled-text-field-container-shape, var(--mat-sys-corner-extra-small))}.mdc-text-field--filled:not(.mdc-text-field--disabled){background-color:var(--mdc-filled-text-field-container-color, var(--mat-sys-surface-variant))}.mdc-text-field--filled.mdc-text-field--disabled{background-color:var(--mdc-filled-text-field-disabled-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 4%, transparent))}.mdc-text-field--outlined{height:56px;overflow:visible;padding-right:max(16px,var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small)));padding-left:max(16px,var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small)) + 4px)}[dir=rtl] .mdc-text-field--outlined{padding-right:max(16px,var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small)) + 4px);padding-left:max(16px,var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small)))}.mdc-floating-label{position:absolute;left:0;transform-origin:left top;line-height:1.15rem;text-align:left;text-overflow:ellipsis;white-space:nowrap;cursor:text;overflow:hidden;will-change:transform}[dir=rtl] .mdc-floating-label{right:0;left:auto;transform-origin:right top;text-align:right}.mdc-text-field .mdc-floating-label{top:50%;transform:translateY(-50%);pointer-events:none}.mdc-notched-outline .mdc-floating-label{display:inline-block;position:relative;max-width:100%}.mdc-text-field--outlined .mdc-floating-label{left:4px;right:auto}[dir=rtl] .mdc-text-field--outlined .mdc-floating-label{left:auto;right:4px}.mdc-text-field--filled .mdc-floating-label{left:16px;right:auto}[dir=rtl] .mdc-text-field--filled .mdc-floating-label{left:auto;right:16px}.mdc-text-field--disabled .mdc-floating-label{cursor:default}@media(forced-colors: active){.mdc-text-field--disabled .mdc-floating-label{z-index:1}}.mdc-text-field--filled.mdc-text-field--no-label .mdc-floating-label{display:none}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-floating-label{color:var(--mdc-filled-text-field-label-text-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label{color:var(--mdc-filled-text-field-focus-label-text-color, var(--mat-sys-primary))}.mdc-text-field--filled:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label{color:var(--mdc-filled-text-field-hover-label-text-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled.mdc-text-field--disabled .mdc-floating-label{color:var(--mdc-filled-text-field-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid .mdc-floating-label{color:var(--mdc-filled-text-field-error-label-text-color, var(--mat-sys-error))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid.mdc-text-field--focused .mdc-floating-label{color:var(--mdc-filled-text-field-error-focus-label-text-color, var(--mat-sys-error))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--disabled):hover .mdc-floating-label{color:var(--mdc-filled-text-field-error-hover-label-text-color, var(--mat-sys-on-error-container))}.mdc-text-field--filled .mdc-floating-label{font-family:var(--mdc-filled-text-field-label-text-font, var(--mat-sys-body-large-font));font-size:var(--mdc-filled-text-field-label-text-size, var(--mat-sys-body-large-size));font-weight:var(--mdc-filled-text-field-label-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mdc-filled-text-field-label-text-tracking, var(--mat-sys-body-large-tracking))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-floating-label{color:var(--mdc-outlined-text-field-label-text-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label{color:var(--mdc-outlined-text-field-focus-label-text-color, var(--mat-sys-primary))}.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label{color:var(--mdc-outlined-text-field-hover-label-text-color, var(--mat-sys-on-surface))}.mdc-text-field--outlined.mdc-text-field--disabled .mdc-floating-label{color:var(--mdc-outlined-text-field-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid .mdc-floating-label{color:var(--mdc-outlined-text-field-error-label-text-color, var(--mat-sys-error))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid.mdc-text-field--focused .mdc-floating-label{color:var(--mdc-outlined-text-field-error-focus-label-text-color, var(--mat-sys-error))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--disabled):hover .mdc-floating-label{color:var(--mdc-outlined-text-field-error-hover-label-text-color, var(--mat-sys-on-error-container))}.mdc-text-field--outlined .mdc-floating-label{font-family:var(--mdc-outlined-text-field-label-text-font, var(--mat-sys-body-large-font));font-size:var(--mdc-outlined-text-field-label-text-size, var(--mat-sys-body-large-size));font-weight:var(--mdc-outlined-text-field-label-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mdc-outlined-text-field-label-text-tracking, var(--mat-sys-body-large-tracking))}.mdc-floating-label--float-above{cursor:auto;transform:translateY(-106%) scale(0.75)}.mdc-text-field--filled .mdc-floating-label--float-above{transform:translateY(-106%) scale(0.75)}.mdc-text-field--outlined .mdc-floating-label--float-above{transform:translateY(-37.25px) scale(1);font-size:.75rem}.mdc-notched-outline .mdc-floating-label--float-above{text-overflow:clip}.mdc-notched-outline--upgraded .mdc-floating-label--float-above{max-width:133.3333333333%}.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{transform:translateY(-34.75px) scale(0.75)}.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:1rem}.mdc-floating-label--required:not(.mdc-floating-label--hide-required-marker)::after{margin-left:1px;margin-right:0;content:"*"}[dir=rtl] .mdc-floating-label--required:not(.mdc-floating-label--hide-required-marker)::after{margin-left:0;margin-right:1px}.mdc-notched-outline{display:flex;position:absolute;top:0;right:0;left:0;box-sizing:border-box;width:100%;max-width:100%;height:100%;text-align:left;pointer-events:none}[dir=rtl] .mdc-notched-outline{text-align:right}.mdc-text-field--outlined .mdc-notched-outline{z-index:1}.mat-mdc-notch-piece{box-sizing:border-box;height:100%;pointer-events:none;border-top:1px solid;border-bottom:1px solid}.mdc-text-field--focused .mat-mdc-notch-piece{border-width:2px}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mat-mdc-notch-piece{border-color:var(--mdc-outlined-text-field-outline-color, var(--mat-sys-outline));border-width:var(--mdc-outlined-text-field-outline-width, 1px)}.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mat-mdc-notch-piece{border-color:var(--mdc-outlined-text-field-hover-outline-color, var(--mat-sys-on-surface))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mat-mdc-notch-piece{border-color:var(--mdc-outlined-text-field-focus-outline-color, var(--mat-sys-primary))}.mdc-text-field--outlined.mdc-text-field--disabled .mat-mdc-notch-piece{border-color:var(--mdc-outlined-text-field-disabled-outline-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid .mat-mdc-notch-piece{border-color:var(--mdc-outlined-text-field-error-outline-color, var(--mat-sys-error))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--focused):hover .mdc-notched-outline .mat-mdc-notch-piece{border-color:var(--mdc-outlined-text-field-error-hover-outline-color, var(--mat-sys-on-error-container))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid.mdc-text-field--focused .mat-mdc-notch-piece{border-color:var(--mdc-outlined-text-field-error-focus-outline-color, var(--mat-sys-error))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline .mat-mdc-notch-piece{border-width:var(--mdc-outlined-text-field-focus-outline-width, 2px)}.mdc-notched-outline__leading{border-left:1px solid;border-right:none;border-top-right-radius:0;border-bottom-right-radius:0;border-top-left-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small));border-bottom-left-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small))}.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading{width:max(12px,var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small)))}[dir=rtl] .mdc-notched-outline__leading{border-left:none;border-right:1px solid;border-bottom-left-radius:0;border-top-left-radius:0;border-top-right-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small));border-bottom-right-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small))}.mdc-notched-outline__trailing{flex-grow:1;border-left:none;border-right:1px solid;border-top-left-radius:0;border-bottom-left-radius:0;border-top-right-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small));border-bottom-right-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small))}[dir=rtl] .mdc-notched-outline__trailing{border-left:1px solid;border-right:none;border-top-right-radius:0;border-bottom-right-radius:0;border-top-left-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small));border-bottom-left-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small))}.mdc-notched-outline__notch{flex:0 0 auto;width:auto}.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__notch{max-width:min(var(--mat-form-field-notch-max-width, 100%),100% - max(12px,var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small)))*2)}.mdc-text-field--outlined .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:1px}.mdc-text-field--focused.mdc-text-field--outlined .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:2px}.mdc-notched-outline--notched .mdc-notched-outline__notch{padding-left:0;padding-right:8px;border-top:none;--mat-form-field-notch-max-width: 100%}[dir=rtl] .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-left:8px;padding-right:0}.mdc-notched-outline--no-label .mdc-notched-outline__notch{display:none}.mdc-line-ripple::before,.mdc-line-ripple::after{position:absolute;bottom:0;left:0;width:100%;border-bottom-style:solid;content:""}.mdc-line-ripple::before{z-index:1;border-bottom-width:var(--mdc-filled-text-field-active-indicator-height, 1px)}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-active-indicator-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-hover-active-indicator-color, var(--mat-sys-on-surface))}.mdc-text-field--filled.mdc-text-field--disabled .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-disabled-active-indicator-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-error-active-indicator-color, var(--mat-sys-error))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--focused):hover .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-error-hover-active-indicator-color, var(--mat-sys-on-error-container))}.mdc-line-ripple::after{transform:scaleX(0);opacity:0;z-index:2}.mdc-text-field--filled .mdc-line-ripple::after{border-bottom-width:var(--mdc-filled-text-field-focus-active-indicator-height, 2px)}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-line-ripple::after{border-bottom-color:var(--mdc-filled-text-field-focus-active-indicator-color, var(--mat-sys-primary))}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-line-ripple::after{border-bottom-color:var(--mdc-filled-text-field-error-focus-active-indicator-color, var(--mat-sys-error))}.mdc-line-ripple--active::after{transform:scaleX(1);opacity:1}.mdc-line-ripple--deactivating::after{opacity:0}.mdc-text-field--disabled{pointer-events:none}.mat-mdc-form-field-textarea-control{vertical-align:middle;resize:vertical;box-sizing:border-box;height:auto;margin:0;padding:0;border:none;overflow:auto}.mat-mdc-form-field-input-control.mat-mdc-form-field-input-control{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font:inherit;letter-spacing:inherit;text-decoration:inherit;text-transform:inherit;border:none}.mat-mdc-form-field .mat-mdc-floating-label.mdc-floating-label{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;line-height:normal;pointer-events:all;will-change:auto}.mat-mdc-form-field:not(.mat-form-field-disabled) .mat-mdc-floating-label.mdc-floating-label{cursor:inherit}.mdc-text-field--no-label:not(.mdc-text-field--textarea) .mat-mdc-form-field-input-control.mdc-text-field__input,.mat-mdc-text-field-wrapper .mat-mdc-form-field-input-control{height:auto}.mat-mdc-text-field-wrapper .mat-mdc-form-field-input-control.mdc-text-field__input[type=color]{height:23px}.mat-mdc-text-field-wrapper{height:auto;flex:auto;will-change:auto}.mat-mdc-form-field-has-icon-prefix .mat-mdc-text-field-wrapper{padding-left:0;--mat-mdc-form-field-label-offset-x: -16px}.mat-mdc-form-field-has-icon-suffix .mat-mdc-text-field-wrapper{padding-right:0}[dir=rtl] .mat-mdc-text-field-wrapper{padding-left:16px;padding-right:16px}[dir=rtl] .mat-mdc-form-field-has-icon-suffix .mat-mdc-text-field-wrapper{padding-left:0}[dir=rtl] .mat-mdc-form-field-has-icon-prefix .mat-mdc-text-field-wrapper{padding-right:0}.mat-form-field-disabled .mdc-text-field__input::placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-disabled .mdc-text-field__input::-moz-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-disabled .mdc-text-field__input::-webkit-input-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-disabled .mdc-text-field__input:-ms-input-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-form-field-label-always-float .mdc-text-field__input::placeholder{transition-delay:40ms;transition-duration:110ms;opacity:1}.mat-mdc-text-field-wrapper .mat-mdc-form-field-infix .mat-mdc-floating-label{left:auto;right:auto}.mat-mdc-text-field-wrapper.mdc-text-field--outlined .mdc-text-field__input{display:inline-block}.mat-mdc-form-field .mat-mdc-text-field-wrapper.mdc-text-field .mdc-notched-outline__notch{padding-top:0}.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field .mdc-notched-outline__notch{border-left:1px solid rgba(0,0,0,0)}[dir=rtl] .mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field .mdc-notched-outline__notch{border-left:none;border-right:1px solid rgba(0,0,0,0)}.mat-mdc-form-field-infix{min-height:var(--mat-form-field-container-height, 56px);padding-top:var(--mat-form-field-filled-with-label-container-padding-top, 24px);padding-bottom:var(--mat-form-field-filled-with-label-container-padding-bottom, 8px)}.mdc-text-field--outlined .mat-mdc-form-field-infix,.mdc-text-field--no-label .mat-mdc-form-field-infix{padding-top:var(--mat-form-field-container-vertical-padding, 16px);padding-bottom:var(--mat-form-field-container-vertical-padding, 16px)}.mat-mdc-text-field-wrapper .mat-mdc-form-field-flex .mat-mdc-floating-label{top:calc(var(--mat-form-field-container-height, 56px)/2)}.mdc-text-field--filled .mat-mdc-floating-label{display:var(--mat-form-field-filled-label-display, block)}.mat-mdc-text-field-wrapper.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{--mat-mdc-form-field-label-transform: translateY(calc(calc(6.75px + var(--mat-form-field-container-height, 56px) / 2) * -1)) scale(var(--mat-mdc-form-field-floating-label-scale, 0.75));transform:var(--mat-mdc-form-field-label-transform)}@keyframes _mat-form-field-subscript-animation{from{opacity:0;transform:translateY(-5px)}to{opacity:1;transform:translateY(0)}}.mat-mdc-form-field-subscript-wrapper{box-sizing:border-box;width:100%;position:relative}.mat-mdc-form-field-hint-wrapper,.mat-mdc-form-field-error-wrapper{position:absolute;top:0;left:0;right:0;padding:0 16px;opacity:1;transform:translateY(0);animation:_mat-form-field-subscript-animation 0ms cubic-bezier(0.55, 0, 0.55, 0.2)}.mat-mdc-form-field-subscript-dynamic-size .mat-mdc-form-field-hint-wrapper,.mat-mdc-form-field-subscript-dynamic-size .mat-mdc-form-field-error-wrapper{position:static}.mat-mdc-form-field-bottom-align::before{content:"";display:inline-block;height:16px}.mat-mdc-form-field-bottom-align.mat-mdc-form-field-subscript-dynamic-size::before{content:unset}.mat-mdc-form-field-hint-end{order:1}.mat-mdc-form-field-hint-wrapper{display:flex}.mat-mdc-form-field-hint-spacer{flex:1 0 1em}.mat-mdc-form-field-error{display:block;color:var(--mat-form-field-error-text-color, var(--mat-sys-error))}.mat-mdc-form-field-subscript-wrapper,.mat-mdc-form-field-bottom-align::before{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-form-field-subscript-text-font, var(--mat-sys-body-small-font));line-height:var(--mat-form-field-subscript-text-line-height, var(--mat-sys-body-small-line-height));font-size:var(--mat-form-field-subscript-text-size, var(--mat-sys-body-small-size));letter-spacing:var(--mat-form-field-subscript-text-tracking, var(--mat-sys-body-small-tracking));font-weight:var(--mat-form-field-subscript-text-weight, var(--mat-sys-body-small-weight))}.mat-mdc-form-field-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;opacity:0;pointer-events:none;background-color:var(--mat-form-field-state-layer-color, var(--mat-sys-on-surface))}.mat-mdc-text-field-wrapper:hover .mat-mdc-form-field-focus-overlay{opacity:var(--mat-form-field-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-form-field.mat-focused .mat-mdc-form-field-focus-overlay{opacity:var(--mat-form-field-focus-state-layer-opacity, 0)}select.mat-mdc-form-field-input-control{-moz-appearance:none;-webkit-appearance:none;background-color:rgba(0,0,0,0);display:inline-flex;box-sizing:border-box}select.mat-mdc-form-field-input-control:not(:disabled){cursor:pointer}select.mat-mdc-form-field-input-control:not(.mat-mdc-native-select-inline) option{color:var(--mat-form-field-select-option-text-color, var(--mat-sys-neutral10))}select.mat-mdc-form-field-input-control:not(.mat-mdc-native-select-inline) option:disabled{color:var(--mat-form-field-select-disabled-option-text-color, color-mix(in srgb, var(--mat-sys-neutral10) 38%, transparent))}.mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-infix::after{content:"";width:0;height:0;border-left:5px solid rgba(0,0,0,0);border-right:5px solid rgba(0,0,0,0);border-top:5px solid;position:absolute;right:0;top:50%;margin-top:-2.5px;pointer-events:none;color:var(--mat-form-field-enabled-select-arrow-color, var(--mat-sys-on-surface-variant))}[dir=rtl] .mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-infix::after{right:auto;left:0}.mat-mdc-form-field-type-mat-native-select.mat-focused .mat-mdc-form-field-infix::after{color:var(--mat-form-field-focus-select-arrow-color, var(--mat-sys-primary))}.mat-mdc-form-field-type-mat-native-select.mat-form-field-disabled .mat-mdc-form-field-infix::after{color:var(--mat-form-field-disabled-select-arrow-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-input-control{padding-right:15px}[dir=rtl] .mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-input-control{padding-right:0;padding-left:15px}@media(forced-colors: active){.mat-form-field-appearance-fill .mat-mdc-text-field-wrapper{outline:solid 1px}}@media(forced-colors: active){.mat-form-field-appearance-fill.mat-form-field-disabled .mat-mdc-text-field-wrapper{outline-color:GrayText}}@media(forced-colors: active){.mat-form-field-appearance-fill.mat-focused .mat-mdc-text-field-wrapper{outline:dashed 3px}}@media(forced-colors: active){.mat-mdc-form-field.mat-focused .mdc-notched-outline{border:dashed 3px}}.mat-mdc-form-field-input-control[type=date],.mat-mdc-form-field-input-control[type=datetime],.mat-mdc-form-field-input-control[type=datetime-local],.mat-mdc-form-field-input-control[type=month],.mat-mdc-form-field-input-control[type=week],.mat-mdc-form-field-input-control[type=time]{line-height:1}.mat-mdc-form-field-input-control::-webkit-datetime-edit{line-height:1;padding:0;margin-bottom:-2px}.mat-mdc-form-field{--mat-mdc-form-field-floating-label-scale: 0.75;display:inline-flex;flex-direction:column;min-width:0;text-align:left;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-form-field-container-text-font, var(--mat-sys-body-large-font));line-height:var(--mat-form-field-container-text-line-height, var(--mat-sys-body-large-line-height));font-size:var(--mat-form-field-container-text-size, var(--mat-sys-body-large-size));letter-spacing:var(--mat-form-field-container-text-tracking, var(--mat-sys-body-large-tracking));font-weight:var(--mat-form-field-container-text-weight, var(--mat-sys-body-large-weight))}.mat-mdc-form-field .mdc-text-field--outlined .mdc-floating-label--float-above{font-size:calc(var(--mat-form-field-outlined-label-text-populated-size)*var(--mat-mdc-form-field-floating-label-scale))}.mat-mdc-form-field .mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:var(--mat-form-field-outlined-label-text-populated-size)}[dir=rtl] .mat-mdc-form-field{text-align:right}.mat-mdc-form-field-flex{display:inline-flex;align-items:baseline;box-sizing:border-box;width:100%}.mat-mdc-text-field-wrapper{width:100%;z-index:0}.mat-mdc-form-field-icon-prefix,.mat-mdc-form-field-icon-suffix{align-self:center;line-height:0;pointer-events:auto;position:relative;z-index:1}.mat-mdc-form-field-icon-prefix>.mat-icon,.mat-mdc-form-field-icon-suffix>.mat-icon{padding:0 12px;box-sizing:content-box}.mat-mdc-form-field-icon-prefix{color:var(--mat-form-field-leading-icon-color, var(--mat-sys-on-surface-variant))}.mat-form-field-disabled .mat-mdc-form-field-icon-prefix{color:var(--mat-form-field-disabled-leading-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-trailing-icon-color, var(--mat-sys-on-surface-variant))}.mat-form-field-disabled .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-disabled-trailing-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-invalid .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-error-trailing-icon-color, var(--mat-sys-error))}.mat-form-field-invalid:not(.mat-focused):not(.mat-form-field-disabled) .mat-mdc-text-field-wrapper:hover .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-error-hover-trailing-icon-color, var(--mat-sys-on-error-container))}.mat-form-field-invalid.mat-focused .mat-mdc-text-field-wrapper .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-error-focus-trailing-icon-color, var(--mat-sys-error))}.mat-mdc-form-field-icon-prefix,[dir=rtl] .mat-mdc-form-field-icon-suffix{padding:0 4px 0 0}.mat-mdc-form-field-icon-suffix,[dir=rtl] .mat-mdc-form-field-icon-prefix{padding:0 0 0 4px}.mat-mdc-form-field-subscript-wrapper .mat-icon,.mat-mdc-form-field label .mat-icon{width:1em;height:1em;font-size:inherit}.mat-mdc-form-field-infix{flex:auto;min-width:0;width:180px;position:relative;box-sizing:border-box}.mat-mdc-form-field-infix:has(textarea[cols]){width:auto}.mat-mdc-form-field .mdc-notched-outline__notch{margin-left:-1px;-webkit-clip-path:inset(-9em -999em -9em 1px);clip-path:inset(-9em -999em -9em 1px)}[dir=rtl] .mat-mdc-form-field .mdc-notched-outline__notch{margin-left:0;margin-right:-1px;-webkit-clip-path:inset(-9em 1px -9em -999em);clip-path:inset(-9em 1px -9em -999em)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-floating-label{transition:transform 150ms cubic-bezier(0.4, 0, 0.2, 1),color 150ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input{transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input::placeholder{transition:opacity 67ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input::-moz-placeholder{transition:opacity 67ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input::-webkit-input-placeholder{transition:opacity 67ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input:-ms-input-placeholder{transition:opacity 67ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--no-label .mdc-text-field__input::placeholder,.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--focused .mdc-text-field__input::placeholder{transition-delay:40ms;transition-duration:110ms}.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--no-label .mdc-text-field__input::-moz-placeholder,.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--focused .mdc-text-field__input::-moz-placeholder{transition-delay:40ms;transition-duration:110ms}.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--no-label .mdc-text-field__input::-webkit-input-placeholder,.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--focused .mdc-text-field__input::-webkit-input-placeholder{transition-delay:40ms;transition-duration:110ms}.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--no-label .mdc-text-field__input:-ms-input-placeholder,.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--focused .mdc-text-field__input:-ms-input-placeholder{transition-delay:40ms;transition-duration:110ms}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field--filled:not(.mdc-ripple-upgraded):focus .mdc-text-field__ripple::before{transition-duration:75ms}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-line-ripple::after{transition:transform 180ms cubic-bezier(0.4, 0, 0.2, 1),opacity 180ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mat-mdc-form-field-hint-wrapper,.mat-mdc-form-field.mat-form-field-animations-enabled .mat-mdc-form-field-error-wrapper{animation-duration:300ms}.mdc-notched-outline .mdc-floating-label{max-width:calc(100% + 1px)}.mdc-notched-outline--upgraded .mdc-floating-label--float-above{max-width:calc(133.3333333333% + 1px)}
`],encapsulation:2,changeDetection:0})}return i})();var Ie=(()=>{class i{static \u0275fac=function(t){return new(t||i)};static \u0275mod=q({type:i});static \u0275inj=j({imports:[me,gi,me]})}return i})();var Tr=(()=>{class i{static \u0275fac=function(t){return new(t||i)};static \u0275cmp=w({type:i,selectors:[["ng-component"]],hostAttrs:["cdk-text-field-style-loader",""],decls:0,vars:0,template:function(t,n){},styles:[`textarea.cdk-textarea-autosize{resize:none}textarea.cdk-textarea-autosize-measuring{padding:2px 0 !important;box-sizing:content-box !important;height:auto !important;overflow:hidden !important}textarea.cdk-textarea-autosize-measuring-firefox{padding:2px 0 !important;box-sizing:content-box !important;height:0 !important}@keyframes cdk-text-field-autofill-start{/*!*/}@keyframes cdk-text-field-autofill-end{/*!*/}.cdk-text-field-autofill-monitored:-webkit-autofill{animation:cdk-text-field-autofill-start 0s 1ms}.cdk-text-field-autofill-monitored:not(:-webkit-autofill){animation:cdk-text-field-autofill-end 0s 1ms}
`],encapsulation:2,changeDetection:0})}return i})(),Ar={passive:!0},Vi=(()=>{class i{_platform=p(ge);_ngZone=p(K);_renderer=p(Yt).createRenderer(null,null);_styleLoader=p(hi);_monitoredElements=new Map;constructor(){}monitor(e){if(!this._platform.isBrowser)return zt;this._styleLoader.load(Tr);let t=Mt(e),n=this._monitoredElements.get(t);if(n)return n.subject;let s=new x,m="cdk-text-field-autofilled",b=le=>{le.animationName==="cdk-text-field-autofill-start"&&!t.classList.contains(m)?(t.classList.add(m),this._ngZone.run(()=>s.next({target:le.target,isAutofilled:!0}))):le.animationName==="cdk-text-field-autofill-end"&&t.classList.contains(m)&&(t.classList.remove(m),this._ngZone.run(()=>s.next({target:le.target,isAutofilled:!1})))},I=this._ngZone.runOutsideAngular(()=>(t.classList.add("cdk-text-field-autofill-monitored"),ui(this._renderer,t,"animationstart",b,Ar)));return this._monitoredElements.set(t,{subject:s,unlisten:I}),s}stopMonitoring(e){let t=Mt(e),n=this._monitoredElements.get(t);n&&(n.unlisten(),n.subject.complete(),t.classList.remove("cdk-text-field-autofill-monitored"),t.classList.remove("cdk-text-field-autofilled"),this._monitoredElements.delete(t))}ngOnDestroy(){this._monitoredElements.forEach((e,t)=>this.stopMonitoring(t))}static \u0275fac=function(t){return new(t||i)};static \u0275prov=J({token:i,factory:i.\u0275fac,providedIn:"root"})}return i})();var ji=(()=>{class i{static \u0275fac=function(t){return new(t||i)};static \u0275mod=q({type:i});static \u0275inj=j({})}return i})();var qi=new T("MAT_INPUT_VALUE_ACCESSOR");var Hi=(()=>{class i{isErrorState(e,t){return!!(e&&e.invalid&&(e.touched||t&&t.submitted))}static \u0275fac=function(t){return new(t||i)};static \u0275prov=J({token:i,factory:i.\u0275fac,providedIn:"root"})}return i})();var dt=class{_defaultMatcher;ngControl;_parentFormGroup;_parentForm;_stateChanges;errorState=!1;matcher;constructor(r,e,t,n,s){this._defaultMatcher=r,this.ngControl=e,this._parentFormGroup=t,this._parentForm=n,this._stateChanges=s}updateErrorState(){let r=this.errorState,e=this._parentFormGroup||this._parentForm,t=this.matcher||this._defaultMatcher,n=this.ngControl?this.ngControl.control:null,s=t?.isErrorState(n,e)??!1;s!==r&&(this.errorState=s,this._stateChanges.next())}};var Rr=["button","checkbox","file","hidden","image","radio","range","reset","submit"],Lr=new T("MAT_INPUT_CONFIG"),se=(()=>{class i{_elementRef=p(ie);_platform=p(ge);ngControl=p(di,{optional:!0,self:!0});_autofillMonitor=p(Vi);_ngZone=p(K);_formField=p(lt,{optional:!0});_renderer=p(Ue);_uid=p(_e).getId("mat-input-");_previousNativeValue;_inputValueAccessor;_signalBasedValueAccessor;_previousPlaceholder;_errorStateTracker;_config=p(Lr,{optional:!0});_cleanupIosKeyup;_cleanupWebkitWheel;_formFieldDescribedBy;_isServer;_isNativeSelect;_isTextarea;_isInFormField;focused=!1;stateChanges=new x;controlType="mat-input";autofilled=!1;get disabled(){return this._disabled}set disabled(e){this._disabled=Ee(e),this.focused&&(this.focused=!1,this.stateChanges.next())}_disabled=!1;get id(){return this._id}set id(e){this._id=e||this._uid}_id;placeholder;name;get required(){return this._required??this.ngControl?.control?.hasValidator(_.required)??!1}set required(e){this._required=Ee(e)}_required;get type(){return this._type}set type(e){let t=this._type;this._type=e||"text",this._validateType(),!this._isTextarea&&kt().has(this._type)&&(this._elementRef.nativeElement.type=this._type),this._type!==t&&this._ensureWheelDefaultBehavior()}_type="text";get errorStateMatcher(){return this._errorStateTracker.matcher}set errorStateMatcher(e){this._errorStateTracker.matcher=e}userAriaDescribedBy;get value(){return this._signalBasedValueAccessor?this._signalBasedValueAccessor.value():this._inputValueAccessor.value}set value(e){e!==this.value&&(this._signalBasedValueAccessor?this._signalBasedValueAccessor.value.set(e):this._inputValueAccessor.value=e,this.stateChanges.next())}get readonly(){return this._readonly}set readonly(e){this._readonly=Ee(e)}_readonly=!1;disabledInteractive;get errorState(){return this._errorStateTracker.errorState}set errorState(e){this._errorStateTracker.errorState=e}_neverEmptyInputTypes=["date","datetime","datetime-local","month","time","week"].filter(e=>kt().has(e));constructor(){let e=p(ci,{optional:!0}),t=p(L,{optional:!0}),n=p(Hi),s=p(qi,{optional:!0,self:!0}),m=this._elementRef.nativeElement,b=m.nodeName.toLowerCase();s?Wt(s.value)?this._signalBasedValueAccessor=s:this._inputValueAccessor=s:this._inputValueAccessor=m,this._previousNativeValue=this.value,this.id=this.id,this._platform.IOS&&this._ngZone.runOutsideAngular(()=>{this._cleanupIosKeyup=this._renderer.listen(m,"keyup",this._iOSKeyupListener)}),this._errorStateTracker=new dt(n,this.ngControl,t,e,this.stateChanges),this._isServer=!this._platform.isBrowser,this._isNativeSelect=b==="select",this._isTextarea=b==="textarea",this._isInFormField=!!this._formField,this.disabledInteractive=this._config?.disabledInteractive||!1,this._isNativeSelect&&(this.controlType=m.multiple?"mat-native-select-multiple":"mat-native-select"),this._signalBasedValueAccessor&&ri(()=>{this._signalBasedValueAccessor.value(),this.stateChanges.next()})}ngAfterViewInit(){this._platform.isBrowser&&this._autofillMonitor.monitor(this._elementRef.nativeElement).subscribe(e=>{this.autofilled=e.isAutofilled,this.stateChanges.next()})}ngOnChanges(){this.stateChanges.next()}ngOnDestroy(){this.stateChanges.complete(),this._platform.isBrowser&&this._autofillMonitor.stopMonitoring(this._elementRef.nativeElement),this._cleanupIosKeyup?.(),this._cleanupWebkitWheel?.()}ngDoCheck(){this.ngControl&&(this.updateErrorState(),this.ngControl.disabled!==null&&this.ngControl.disabled!==this.disabled&&(this.disabled=this.ngControl.disabled,this.stateChanges.next())),this._dirtyCheckNativeValue(),this._dirtyCheckPlaceholder()}focus(e){this._elementRef.nativeElement.focus(e)}updateErrorState(){this._errorStateTracker.updateErrorState()}_focusChanged(e){if(e!==this.focused){if(!this._isNativeSelect&&e&&this.disabled&&this.disabledInteractive){let t=this._elementRef.nativeElement;t.type==="number"?(t.type="text",t.setSelectionRange(0,0),t.type="number"):t.setSelectionRange(0,0)}this.focused=e,this.stateChanges.next()}}_onInput(){}_dirtyCheckNativeValue(){let e=this._elementRef.nativeElement.value;this._previousNativeValue!==e&&(this._previousNativeValue=e,this.stateChanges.next())}_dirtyCheckPlaceholder(){let e=this._getPlaceholder();if(e!==this._previousPlaceholder){let t=this._elementRef.nativeElement;this._previousPlaceholder=e,e?t.setAttribute("placeholder",e):t.removeAttribute("placeholder")}}_getPlaceholder(){return this.placeholder||null}_validateType(){Rr.indexOf(this._type)>-1}_isNeverEmpty(){return this._neverEmptyInputTypes.indexOf(this._type)>-1}_isBadInput(){let e=this._elementRef.nativeElement.validity;return e&&e.badInput}get empty(){return!this._isNeverEmpty()&&!this._elementRef.nativeElement.value&&!this._isBadInput()&&!this.autofilled}get shouldLabelFloat(){if(this._isNativeSelect){let e=this._elementRef.nativeElement,t=e.options[0];return this.focused||e.multiple||!this.empty||!!(e.selectedIndex>-1&&t&&t.label)}else return this.focused&&!this.disabled||!this.empty}setDescribedByIds(e){let t=this._elementRef.nativeElement,n=t.getAttribute("aria-describedby"),s;if(n){let m=this._formFieldDescribedBy||e;s=e.concat(n.split(" ").filter(b=>b&&!m.includes(b)))}else s=e;this._formFieldDescribedBy=e,s.length?t.setAttribute("aria-describedby",s.join(" ")):t.removeAttribute("aria-describedby")}onContainerClick(){this.focused||this.focus()}_isInlineSelect(){let e=this._elementRef.nativeElement;return this._isNativeSelect&&(e.multiple||e.size>1)}_iOSKeyupListener=e=>{let t=e.target;!t.value&&t.selectionStart===0&&t.selectionEnd===0&&(t.setSelectionRange(1,1),t.setSelectionRange(0,0))};_webkitBlinkWheelListener=()=>{};_ensureWheelDefaultBehavior(){this._cleanupWebkitWheel?.(),this._type==="number"&&(this._platform.BLINK||this._platform.WEBKIT)&&(this._cleanupWebkitWheel=this._renderer.listen(this._elementRef.nativeElement,"wheel",this._webkitBlinkWheelListener))}_getReadonlyAttribute(){return this._isNativeSelect?null:this.readonly||this.disabled&&this.disabledInteractive?"true":null}static \u0275fac=function(t){return new(t||i)};static \u0275dir=B({type:i,selectors:[["input","matInput",""],["textarea","matInput",""],["select","matNativeControl",""],["input","matNativeControl",""],["textarea","matNativeControl",""]],hostAttrs:[1,"mat-mdc-input-element"],hostVars:21,hostBindings:function(t,n){t&1&&u("focus",function(){return n._focusChanged(!0)})("blur",function(){return n._focusChanged(!1)})("input",function(){return n._onInput()}),t&2&&(Ge("id",n.id)("disabled",n.disabled&&!n.disabledInteractive)("required",n.required),k("name",n.name||null)("readonly",n._getReadonlyAttribute())("aria-disabled",n.disabled&&n.disabledInteractive?"true":null)("aria-invalid",n.empty&&n.required?null:n.errorState)("aria-required",n.required)("id",n.id),v("mat-input-server",n._isServer)("mat-mdc-form-field-textarea-control",n._isInFormField&&n._isTextarea)("mat-mdc-form-field-input-control",n._isInFormField)("mat-mdc-input-disabled-interactive",n.disabledInteractive)("mdc-text-field__input",n._isInFormField)("mat-mdc-native-select-inline",n._isInlineSelect()))},inputs:{disabled:"disabled",id:"id",placeholder:"placeholder",name:"name",required:"required",type:"type",errorStateMatcher:"errorStateMatcher",userAriaDescribedBy:[0,"aria-describedby","userAriaDescribedBy"],value:"value",readonly:"readonly",disabledInteractive:[2,"disabledInteractive","disabledInteractive",Ct]},exportAs:["matInput"],features:[Oe([{provide:st,useExisting:i}]),Gt]})}return i})(),Ui=(()=>{class i{static \u0275fac=function(t){return new(t||i)};static \u0275mod=q({type:i});static \u0275inj=j({imports:[me,Ie,Ie,ji,me]})}return i})();var Dr=["determinateSpinner"];function zr(i,r){if(i&1&&(xt(),o(0,"svg",11),h(1,"circle",12),a()),i&2){let e=y();k("viewBox",e._viewBox()),l(),P("stroke-dasharray",e._strokeCircumference(),"px")("stroke-dashoffset",e._strokeCircumference()/2,"px")("stroke-width",e._circleStrokeWidth(),"%"),k("r",e._circleRadius())}}var Br=new T("mat-progress-spinner-default-options",{providedIn:"root",factory:Nr});function Nr(){return{diameter:Gi}}var Gi=100,Vr=10,Y=(()=>{class i{_elementRef=p(ie);_noopAnimations;get color(){return this._color||this._defaultColor}set color(e){this._color=e}_color;_defaultColor="primary";_determinateCircle;constructor(){let e=p(Se,{optional:!0}),t=p(Br);this._noopAnimations=e==="NoopAnimations"&&!!t&&!t._forceAnimations,this.mode=this._elementRef.nativeElement.nodeName.toLowerCase()==="mat-spinner"?"indeterminate":"determinate",t&&(t.color&&(this.color=this._defaultColor=t.color),t.diameter&&(this.diameter=t.diameter),t.strokeWidth&&(this.strokeWidth=t.strokeWidth))}mode;get value(){return this.mode==="determinate"?this._value:0}set value(e){this._value=Math.max(0,Math.min(100,e||0))}_value=0;get diameter(){return this._diameter}set diameter(e){this._diameter=e||0}_diameter=Gi;get strokeWidth(){return this._strokeWidth??this.diameter/10}set strokeWidth(e){this._strokeWidth=e||0}_strokeWidth;_circleRadius(){return(this.diameter-Vr)/2}_viewBox(){let e=this._circleRadius()*2+this.strokeWidth;return`0 0 ${e} ${e}`}_strokeCircumference(){return 2*Math.PI*this._circleRadius()}_strokeDashOffset(){return this.mode==="determinate"?this._strokeCircumference()*(100-this._value)/100:null}_circleStrokeWidth(){return this.strokeWidth/this.diameter*100}static \u0275fac=function(t){return new(t||i)};static \u0275cmp=w({type:i,selectors:[["mat-progress-spinner"],["mat-spinner"]],viewQuery:function(t,n){if(t&1&&E(Dr,5),t&2){let s;M(s=S())&&(n._determinateCircle=s.first)}},hostAttrs:["role","progressbar","tabindex","-1",1,"mat-mdc-progress-spinner","mdc-circular-progress"],hostVars:18,hostBindings:function(t,n){t&2&&(k("aria-valuemin",0)("aria-valuemax",100)("aria-valuenow",n.mode==="determinate"?n.value:null)("mode",n.mode),Jt("mat-"+n.color),P("width",n.diameter,"px")("height",n.diameter,"px")("--mdc-circular-progress-size",n.diameter+"px")("--mdc-circular-progress-active-indicator-width",n.diameter+"px"),v("_mat-animation-noopable",n._noopAnimations)("mdc-circular-progress--indeterminate",n.mode==="indeterminate"))},inputs:{color:"color",mode:"mode",value:[2,"value","value",it],diameter:[2,"diameter","diameter",it],strokeWidth:[2,"strokeWidth","strokeWidth",it]},exportAs:["matProgressSpinner"],decls:14,vars:11,consts:[["circle",""],["determinateSpinner",""],["aria-hidden","true",1,"mdc-circular-progress__determinate-container"],["xmlns","http://www.w3.org/2000/svg","focusable","false",1,"mdc-circular-progress__determinate-circle-graphic"],["cx","50%","cy","50%",1,"mdc-circular-progress__determinate-circle"],["aria-hidden","true",1,"mdc-circular-progress__indeterminate-container"],[1,"mdc-circular-progress__spinner-layer"],[1,"mdc-circular-progress__circle-clipper","mdc-circular-progress__circle-left"],[3,"ngTemplateOutlet"],[1,"mdc-circular-progress__gap-patch"],[1,"mdc-circular-progress__circle-clipper","mdc-circular-progress__circle-right"],["xmlns","http://www.w3.org/2000/svg","focusable","false",1,"mdc-circular-progress__indeterminate-circle-graphic"],["cx","50%","cy","50%"]],template:function(t,n){if(t&1&&(f(0,zr,2,8,"ng-template",null,0,et),o(2,"div",2,1),xt(),o(4,"svg",3),h(5,"circle",4),a()(),$t(),o(6,"div",5)(7,"div",6)(8,"div",7),Je(9,8),a(),o(10,"div",9),Je(11,8),a(),o(12,"div",10),Je(13,8),a()()()),t&2){let s=$e(1);l(4),k("viewBox",n._viewBox()),l(),P("stroke-dasharray",n._strokeCircumference(),"px")("stroke-dashoffset",n._strokeDashOffset(),"px")("stroke-width",n._circleStrokeWidth(),"%"),k("r",n._circleRadius()),l(4),c("ngTemplateOutlet",s),l(2),c("ngTemplateOutlet",s),l(2),c("ngTemplateOutlet",s)}},dependencies:[rt],styles:[`.mat-mdc-progress-spinner{display:block;overflow:hidden;line-height:0;position:relative;direction:ltr;transition:opacity 250ms cubic-bezier(0.4, 0, 0.6, 1)}.mat-mdc-progress-spinner circle{stroke-width:var(--mdc-circular-progress-active-indicator-width, 4px)}.mat-mdc-progress-spinner._mat-animation-noopable,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__determinate-circle{transition:none !important}.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-circle-graphic,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__spinner-layer,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-container{animation:none !important}.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-container circle{stroke-dasharray:0 !important}@media(forced-colors: active){.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic,.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle{stroke:currentColor;stroke:CanvasText}}.mdc-circular-progress__determinate-container,.mdc-circular-progress__indeterminate-circle-graphic,.mdc-circular-progress__indeterminate-container,.mdc-circular-progress__spinner-layer{position:absolute;width:100%;height:100%}.mdc-circular-progress__determinate-container{transform:rotate(-90deg)}.mdc-circular-progress--indeterminate .mdc-circular-progress__determinate-container{opacity:0}.mdc-circular-progress__indeterminate-container{font-size:0;letter-spacing:0;white-space:nowrap;opacity:0}.mdc-circular-progress--indeterminate .mdc-circular-progress__indeterminate-container{opacity:1;animation:mdc-circular-progress-container-rotate 1568.2352941176ms linear infinite}.mdc-circular-progress__determinate-circle-graphic,.mdc-circular-progress__indeterminate-circle-graphic{fill:rgba(0,0,0,0)}.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle,.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic{stroke:var(--mdc-circular-progress-active-indicator-color, var(--mat-sys-primary))}@media(forced-colors: active){.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle,.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic{stroke:CanvasText}}.mdc-circular-progress__determinate-circle{transition:stroke-dashoffset 500ms cubic-bezier(0, 0, 0.2, 1)}.mdc-circular-progress__gap-patch{position:absolute;top:0;left:47.5%;box-sizing:border-box;width:5%;height:100%;overflow:hidden}.mdc-circular-progress__gap-patch .mdc-circular-progress__indeterminate-circle-graphic{left:-900%;width:2000%;transform:rotate(180deg)}.mdc-circular-progress__circle-clipper .mdc-circular-progress__indeterminate-circle-graphic{width:200%}.mdc-circular-progress__circle-right .mdc-circular-progress__indeterminate-circle-graphic{left:-100%}.mdc-circular-progress--indeterminate .mdc-circular-progress__circle-left .mdc-circular-progress__indeterminate-circle-graphic{animation:mdc-circular-progress-left-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress--indeterminate .mdc-circular-progress__circle-right .mdc-circular-progress__indeterminate-circle-graphic{animation:mdc-circular-progress-right-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress__circle-clipper{display:inline-flex;position:relative;width:50%;height:100%;overflow:hidden}.mdc-circular-progress--indeterminate .mdc-circular-progress__spinner-layer{animation:mdc-circular-progress-spinner-layer-rotate 5332ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}@keyframes mdc-circular-progress-container-rotate{to{transform:rotate(360deg)}}@keyframes mdc-circular-progress-spinner-layer-rotate{12.5%{transform:rotate(135deg)}25%{transform:rotate(270deg)}37.5%{transform:rotate(405deg)}50%{transform:rotate(540deg)}62.5%{transform:rotate(675deg)}75%{transform:rotate(810deg)}87.5%{transform:rotate(945deg)}100%{transform:rotate(1080deg)}}@keyframes mdc-circular-progress-left-spin{from{transform:rotate(265deg)}50%{transform:rotate(130deg)}to{transform:rotate(265deg)}}@keyframes mdc-circular-progress-right-spin{from{transform:rotate(-265deg)}50%{transform:rotate(-130deg)}to{transform:rotate(-265deg)}}
`],encapsulation:2,changeDetection:0})}return i})();var $i=(()=>{class i{static \u0275fac=function(t){return new(t||i)};static \u0275mod=q({type:i});static \u0275inj=j({imports:[me]})}return i})();var Wi={XSmall:"(max-width: 599.98px)",Small:"(min-width: 600px) and (max-width: 959.98px)",Medium:"(min-width: 960px) and (max-width: 1279.98px)",Large:"(min-width: 1280px) and (max-width: 1919.98px)",XLarge:"(min-width: 1920px)",Handset:"(max-width: 599.98px) and (orientation: portrait), (max-width: 959.98px) and (orientation: landscape)",Tablet:"(min-width: 600px) and (max-width: 839.98px) and (orientation: portrait), (min-width: 960px) and (max-width: 1279.98px) and (orientation: landscape)",Web:"(min-width: 840px) and (orientation: portrait), (min-width: 1280px) and (orientation: landscape)",HandsetPortrait:"(max-width: 599.98px) and (orientation: portrait)",TabletPortrait:"(min-width: 600px) and (max-width: 839.98px) and (orientation: portrait)",WebPortrait:"(min-width: 840px) and (orientation: portrait)",HandsetLandscape:"(max-width: 959.98px) and (orientation: landscape)",TabletLandscape:"(min-width: 960px) and (max-width: 1279.98px) and (orientation: landscape)",WebLandscape:"(min-width: 1280px) and (orientation: landscape)"};function jr(i,r){if(i&1){let e=de();o(0,"div",1)(1,"button",2),u("click",function(){ee(e);let n=y();return te(n.action())}),d(2),a()()}if(i&2){let e=y();l(2),ue(" ",e.data.action," ")}}var qr=["label"];function Hr(i,r){}var Ur=Math.pow(2,31)-1,Qe=class{_overlayRef;instance;containerInstance;_afterDismissed=new x;_afterOpened=new x;_onAction=new x;_durationTimeoutId;_dismissedByAction=!1;constructor(r,e){this._overlayRef=e,this.containerInstance=r,r._onExit.subscribe(()=>this._finishDismiss())}dismiss(){this._afterDismissed.closed||this.containerInstance.exit(),clearTimeout(this._durationTimeoutId)}dismissWithAction(){this._onAction.closed||(this._dismissedByAction=!0,this._onAction.next(),this._onAction.complete(),this.dismiss()),clearTimeout(this._durationTimeoutId)}closeWithAction(){this.dismissWithAction()}_dismissAfter(r){this._durationTimeoutId=setTimeout(()=>this.dismiss(),Math.min(r,Ur))}_open(){this._afterOpened.closed||(this._afterOpened.next(),this._afterOpened.complete())}_finishDismiss(){this._overlayRef.dispose(),this._onAction.closed||this._onAction.complete(),this._afterDismissed.next({dismissedByAction:this._dismissedByAction}),this._afterDismissed.complete(),this._dismissedByAction=!1}afterDismissed(){return this._afterDismissed}afterOpened(){return this.containerInstance._onEnter}onAction(){return this._onAction}},Qi=new T("MatSnackBarData"),Re=class{politeness="assertive";announcementMessage="";viewContainerRef;duration=0;panelClass;direction;data=null;horizontalPosition="center";verticalPosition="bottom"},Gr=(()=>{class i{static \u0275fac=function(t){return new(t||i)};static \u0275dir=B({type:i,selectors:[["","matSnackBarLabel",""]],hostAttrs:[1,"mat-mdc-snack-bar-label","mdc-snackbar__label"]})}return i})(),$r=(()=>{class i{static \u0275fac=function(t){return new(t||i)};static \u0275dir=B({type:i,selectors:[["","matSnackBarActions",""]],hostAttrs:[1,"mat-mdc-snack-bar-actions","mdc-snackbar__actions"]})}return i})(),Wr=(()=>{class i{static \u0275fac=function(t){return new(t||i)};static \u0275dir=B({type:i,selectors:[["","matSnackBarAction",""]],hostAttrs:[1,"mat-mdc-snack-bar-action","mdc-snackbar__action"]})}return i})(),Zi=(()=>{class i{snackBarRef=p(Qe);data=p(Qi);constructor(){}action(){this.snackBarRef.dismissWithAction()}get hasAction(){return!!this.data.action}static \u0275fac=function(t){return new(t||i)};static \u0275cmp=w({type:i,selectors:[["simple-snack-bar"]],hostAttrs:[1,"mat-mdc-simple-snack-bar"],exportAs:["matSnackBar"],decls:3,vars:2,consts:[["matSnackBarLabel",""],["matSnackBarActions",""],["mat-button","","matSnackBarAction","",3,"click"]],template:function(t,n){t&1&&(o(0,"div",0),d(1),a(),f(2,jr,3,1,"div",1)),t&2&&(l(),ue(" ",n.data.message,`
`),l(),A(n.hasAction?2:-1))},dependencies:[D,Gr,$r,Wr],styles:[`.mat-mdc-simple-snack-bar{display:flex}
`],encapsulation:2,changeDetection:0})}return i})(),At="_mat-snack-bar-enter",Rt="_mat-snack-bar-exit",Qr=(()=>{class i extends Ci{_ngZone=p(K);_elementRef=p(ie);_changeDetectorRef=p(tt);_platform=p(ge);_rendersRef;_animationsDisabled=p(Se,{optional:!0})==="NoopAnimations";snackBarConfig=p(Re);_document=p(ni);_trackedModals=new Set;_enterFallback;_exitFallback;_renders=new x;_announceDelay=150;_announceTimeoutId;_destroyed=!1;_portalOutlet;_onAnnounce=new x;_onExit=new x;_onEnter=new x;_animationState="void";_live;_label;_role;_liveElementId=p(_e).getId("mat-snack-bar-container-live-");constructor(){super();let e=this.snackBarConfig;e.politeness==="assertive"&&!e.announcementMessage?this._live="assertive":e.politeness==="off"?this._live="off":this._live="polite",this._platform.FIREFOX&&(this._live==="polite"&&(this._role="status"),this._live==="assertive"&&(this._role="alert")),this._rendersRef=Xe(()=>this._renders.next(),{manualCleanup:!0})}attachComponentPortal(e){this._assertNotAttached();let t=this._portalOutlet.attachComponentPortal(e);return this._afterPortalAttached(),t}attachTemplatePortal(e){this._assertNotAttached();let t=this._portalOutlet.attachTemplatePortal(e);return this._afterPortalAttached(),t}attachDomPortal=e=>{this._assertNotAttached();let t=this._portalOutlet.attachDomPortal(e);return this._afterPortalAttached(),t};onAnimationEnd(e){e===Rt?this._completeExit():e===At&&(clearTimeout(this._enterFallback),this._ngZone.run(()=>{this._onEnter.next(),this._onEnter.complete()}))}enter(){this._destroyed||(this._animationState="visible",this._changeDetectorRef.markForCheck(),this._changeDetectorRef.detectChanges(),this._screenReaderAnnounce(),this._animationsDisabled?this._renders.pipe(He(1)).subscribe(()=>{this._ngZone.run(()=>queueMicrotask(()=>this.onAnimationEnd(At)))}):(clearTimeout(this._enterFallback),this._enterFallback=setTimeout(()=>{this._elementRef.nativeElement.classList.add("mat-snack-bar-fallback-visible"),this.onAnimationEnd(At)},200)))}exit(){return this._destroyed?Bt(void 0):(this._ngZone.run(()=>{this._animationState="hidden",this._changeDetectorRef.markForCheck(),this._elementRef.nativeElement.setAttribute("mat-exit",""),clearTimeout(this._announceTimeoutId),this._animationsDisabled?this._renders.pipe(He(1)).subscribe(()=>{this._ngZone.run(()=>queueMicrotask(()=>this.onAnimationEnd(Rt)))}):(clearTimeout(this._exitFallback),this._exitFallback=setTimeout(()=>this.onAnimationEnd(Rt),200))}),this._onExit)}ngOnDestroy(){this._destroyed=!0,this._clearFromModals(),this._completeExit(),this._renders.complete(),this._rendersRef.destroy()}_completeExit(){clearTimeout(this._exitFallback),queueMicrotask(()=>{this._onExit.next(),this._onExit.complete()})}_afterPortalAttached(){let e=this._elementRef.nativeElement,t=this.snackBarConfig.panelClass;t&&(Array.isArray(t)?t.forEach(m=>e.classList.add(m)):e.classList.add(t)),this._exposeToModals();let n=this._label.nativeElement,s="mdc-snackbar__label";n.classList.toggle(s,!n.querySelector(`.${s}`))}_exposeToModals(){let e=this._liveElementId,t=this._document.querySelectorAll('body > .cdk-overlay-container [aria-modal="true"]');for(let n=0;n<t.length;n++){let s=t[n],m=s.getAttribute("aria-owns");this._trackedModals.add(s),m?m.indexOf(e)===-1&&s.setAttribute("aria-owns",m+" "+e):s.setAttribute("aria-owns",e)}}_clearFromModals(){this._trackedModals.forEach(e=>{let t=e.getAttribute("aria-owns");if(t){let n=t.replace(this._liveElementId,"").trim();n.length>0?e.setAttribute("aria-owns",n):e.removeAttribute("aria-owns")}}),this._trackedModals.clear()}_assertNotAttached(){this._portalOutlet.hasAttached()}_screenReaderAnnounce(){this._announceTimeoutId||this._ngZone.runOutsideAngular(()=>{this._announceTimeoutId=setTimeout(()=>{if(this._destroyed)return;let e=this._elementRef.nativeElement,t=e.querySelector("[aria-hidden]"),n=e.querySelector("[aria-live]");if(t&&n){let s=null;this._platform.isBrowser&&document.activeElement instanceof HTMLElement&&t.contains(document.activeElement)&&(s=document.activeElement),t.removeAttribute("aria-hidden"),n.appendChild(t),s?.focus(),this._onAnnounce.next(),this._onAnnounce.complete()}},this._announceDelay)})}static \u0275fac=function(t){return new(t||i)};static \u0275cmp=w({type:i,selectors:[["mat-snack-bar-container"]],viewQuery:function(t,n){if(t&1&&(E(Ft,7),E(qr,7)),t&2){let s;M(s=S())&&(n._portalOutlet=s.first),M(s=S())&&(n._label=s.first)}},hostAttrs:[1,"mdc-snackbar","mat-mdc-snack-bar-container"],hostVars:6,hostBindings:function(t,n){t&1&&u("animationend",function(m){return n.onAnimationEnd(m.animationName)})("animationcancel",function(m){return n.onAnimationEnd(m.animationName)}),t&2&&v("mat-snack-bar-container-enter",n._animationState==="visible")("mat-snack-bar-container-exit",n._animationState==="hidden")("mat-snack-bar-container-animations-enabled",!n._animationsDisabled)},features:[Xt],decls:6,vars:3,consts:[["label",""],[1,"mdc-snackbar__surface","mat-mdc-snackbar-surface"],[1,"mat-mdc-snack-bar-label"],["aria-hidden","true"],["cdkPortalOutlet",""]],template:function(t,n){t&1&&(o(0,"div",1)(1,"div",2,0)(3,"div",3),f(4,Hr,0,0,"ng-template",4),a(),h(5,"div"),a()()),t&2&&(l(5),k("aria-live",n._live)("role",n._role)("id",n._liveElementId))},dependencies:[Ft],styles:[`@keyframes _mat-snack-bar-enter{from{transform:scale(0.8);opacity:0}to{transform:scale(1);opacity:1}}@keyframes _mat-snack-bar-exit{from{opacity:1}to{opacity:0}}.mat-mdc-snack-bar-container{display:flex;align-items:center;justify-content:center;box-sizing:border-box;-webkit-tap-highlight-color:rgba(0,0,0,0);margin:8px}.mat-mdc-snack-bar-handset .mat-mdc-snack-bar-container{width:100vw}.mat-snack-bar-container-animations-enabled{opacity:0}.mat-snack-bar-container-animations-enabled.mat-snack-bar-fallback-visible{opacity:1}.mat-snack-bar-container-animations-enabled.mat-snack-bar-container-enter{animation:_mat-snack-bar-enter 150ms cubic-bezier(0, 0, 0.2, 1) forwards}.mat-snack-bar-container-animations-enabled.mat-snack-bar-container-exit{animation:_mat-snack-bar-exit 75ms cubic-bezier(0.4, 0, 1, 1) forwards}.mat-mdc-snackbar-surface{box-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12);display:flex;align-items:center;justify-content:flex-start;box-sizing:border-box;padding-left:0;padding-right:8px}[dir=rtl] .mat-mdc-snackbar-surface{padding-right:0;padding-left:8px}.mat-mdc-snack-bar-container .mat-mdc-snackbar-surface{min-width:344px;max-width:672px}.mat-mdc-snack-bar-handset .mat-mdc-snackbar-surface{width:100%;min-width:0}@media(forced-colors: active){.mat-mdc-snackbar-surface{outline:solid 1px}}.mat-mdc-snack-bar-container .mat-mdc-snackbar-surface{color:var(--mdc-snackbar-supporting-text-color, var(--mat-sys-inverse-on-surface));border-radius:var(--mdc-snackbar-container-shape, var(--mat-sys-corner-extra-small));background-color:var(--mdc-snackbar-container-color, var(--mat-sys-inverse-surface))}.mdc-snackbar__label{width:100%;flex-grow:1;box-sizing:border-box;margin:0;padding:14px 8px 14px 16px}[dir=rtl] .mdc-snackbar__label{padding-left:8px;padding-right:16px}.mat-mdc-snack-bar-container .mdc-snackbar__label{font-family:var(--mdc-snackbar-supporting-text-font, var(--mat-sys-body-medium-font));font-size:var(--mdc-snackbar-supporting-text-size, var(--mat-sys-body-medium-size));font-weight:var(--mdc-snackbar-supporting-text-weight, var(--mat-sys-body-medium-weight));line-height:var(--mdc-snackbar-supporting-text-line-height, var(--mat-sys-body-medium-line-height))}.mat-mdc-snack-bar-actions{display:flex;flex-shrink:0;align-items:center;box-sizing:border-box}.mat-mdc-snack-bar-handset,.mat-mdc-snack-bar-container,.mat-mdc-snack-bar-label{flex:1 1 auto}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled).mat-unthemed{color:var(--mat-snack-bar-button-color, var(--mat-sys-inverse-primary))}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled){--mat-text-button-state-layer-color:currentColor;--mat-text-button-ripple-color:currentColor}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled) .mat-ripple-element{opacity:.1}
`],encapsulation:2})}return i})();function Zr(){return new Re}var Yr=new T("mat-snack-bar-default-options",{providedIn:"root",factory:Zr}),V=(()=>{class i{_overlay=p(Mi);_live=p(_i);_injector=p(Me);_breakpointObserver=p(St);_parentSnackBar=p(i,{optional:!0,skipSelf:!0});_defaultConfig=p(Yr);_snackBarRefAtThisLevel=null;simpleSnackBarComponent=Zi;snackBarContainerComponent=Qr;handsetCssClass="mat-mdc-snack-bar-handset";get _openedSnackBarRef(){let e=this._parentSnackBar;return e?e._openedSnackBarRef:this._snackBarRefAtThisLevel}set _openedSnackBarRef(e){this._parentSnackBar?this._parentSnackBar._openedSnackBarRef=e:this._snackBarRefAtThisLevel=e}constructor(){}openFromComponent(e,t){return this._attach(e,t)}openFromTemplate(e,t){return this._attach(e,t)}open(e,t="",n){let s=Pe(Pe({},this._defaultConfig),n);return s.data={message:e,action:t},s.announcementMessage===e&&(s.announcementMessage=void 0),this.openFromComponent(this.simpleSnackBarComponent,s)}dismiss(){this._openedSnackBarRef&&this._openedSnackBarRef.dismiss()}ngOnDestroy(){this._snackBarRefAtThisLevel&&this._snackBarRefAtThisLevel.dismiss()}_attachSnackBarContainer(e,t){let n=t&&t.viewContainerRef&&t.viewContainerRef.injector,s=Me.create({parent:n||this._injector,providers:[{provide:Re,useValue:t}]}),m=new Ot(this.snackBarContainerComponent,t.viewContainerRef,s),b=e.attach(m);return b.instance.snackBarConfig=t,b.instance}_attach(e,t){let n=Pe(Pe(Pe({},new Re),this._defaultConfig),t),s=this._createOverlay(n),m=this._attachSnackBarContainer(s,n),b=new Qe(m,s);if(e instanceof Zt){let I=new yi(e,null,{$implicit:n.data,snackBarRef:b});b.instance=m.attachTemplatePortal(I)}else{let I=this._createInjector(n,b),le=new Ot(e,void 0,I),bt=m.attachComponentPortal(le);b.instance=bt.instance}return this._breakpointObserver.observe(Wi.HandsetPortrait).pipe(C(s.detachments())).subscribe(I=>{s.overlayElement.classList.toggle(this.handsetCssClass,I.matches)}),n.announcementMessage&&m._onAnnounce.subscribe(()=>{this._live.announce(n.announcementMessage,n.politeness)}),this._animateSnackBar(b,n),this._openedSnackBarRef=b,this._openedSnackBarRef}_animateSnackBar(e,t){e.afterDismissed().subscribe(()=>{this._openedSnackBarRef==e&&(this._openedSnackBarRef=null),t.announcementMessage&&this._live.clear()}),t.duration&&t.duration>0&&e.afterOpened().subscribe(()=>e._dismissAfter(t.duration)),this._openedSnackBarRef?(this._openedSnackBarRef.afterDismissed().subscribe(()=>{e.containerInstance.enter()}),this._openedSnackBarRef.dismiss()):e.containerInstance.enter()}_createOverlay(e){let t=new Pi;t.direction=e.direction;let n=this._overlay.position().global(),s=e.direction==="rtl",m=e.horizontalPosition==="left"||e.horizontalPosition==="start"&&!s||e.horizontalPosition==="end"&&s,b=!m&&e.horizontalPosition!=="center";return m?n.left("0"):b?n.right("0"):n.centerHorizontally(),e.verticalPosition==="top"?n.top("0"):n.bottom("0"),t.positionStrategy=n,this._overlay.create(t)}_createInjector(e,t){let n=e&&e.viewContainerRef&&e.viewContainerRef.injector;return Me.create({parent:n||this._injector,providers:[{provide:Qe,useValue:t},{provide:Qi,useValue:e.data}]})}static \u0275fac=function(t){return new(t||i)};static \u0275prov=J({token:i,factory:i.\u0275fac,providedIn:"root"})}return i})();var Yi=(()=>{class i{static \u0275fac=function(t){return new(t||i)};static \u0275mod=q({type:i});static \u0275inj=j({providers:[V],imports:[Si,wi,ot,me,Zi,me]})}return i})();function Kr(i,r){i&1&&(o(0,"mat-error"),d(1," Email is required "),a())}function Xr(i,r){i&1&&(o(0,"mat-error"),d(1," Please enter a valid email address "),a())}function Jr(i,r){i&1&&(o(0,"mat-error"),d(1," Password is required "),a())}function en(i,r){i&1&&(o(0,"mat-error"),d(1," Password must be at least 6 characters long "),a())}function tn(i,r){i&1&&h(0,"mat-spinner",24)}var ct=class i{constructor(r,e,t,n,s){this.authService=r;this.authState=e;this.formBuilder=t;this.router=n;this.snackBar=s;this.loginForm=this.formBuilder.group({email:["",[_.required,_.email]],password:["",[_.required,_.minLength(6)]]})}destroy$=new x;loginForm;hidePassword=!0;logoLoaded=!0;isAuthenticated=!1;isLoading=!1;user=null;error=null;ngOnInit(){this.authState.authState$.pipe(C(this.destroy$)).subscribe(r=>{this.isAuthenticated=r.isAuthenticated,this.isLoading=r.isLoading,this.user=r.user,this.error=r.error,r.error&&this.showError(r.error),r.isAuthenticated&&r.user&&this.router.navigate(["/dashboard"])})}ngOnDestroy(){this.destroy$.next(),this.destroy$.complete()}onLogin(){if(this.loginForm.valid){let r=this.loginForm.value;this.authService.login(r).subscribe({next:e=>{e.data?.requiresOtp||e.data?.RequiresOtp?this.router.navigate(["/auth/otp-verification"],{queryParams:{userId:e.data.userId||e.data.UserId}}):e.isSuccess&&(this.showSuccess("Login successful!"),this.router.navigate(["/dashboard"]))},error:e=>{console.error("Login failed:",e)}})}else this.markFormGroupTouched()}onForgotPassword(){this.router.navigate(["/auth/forgot-password"])}onSignUp(){this.router.navigate(["/auth/signup"])}togglePasswordVisibility(){this.hidePassword=!this.hidePassword}onImageError(r){this.logoLoaded=!1,console.log("Logo failed to load, showing fallback")}markFormGroupTouched(){Object.keys(this.loginForm.controls).forEach(r=>{this.loginForm.get(r)?.markAsTouched()})}showError(r){this.snackBar.open(r,"Close",{duration:5e3,panelClass:["error-snackbar"]})}showSuccess(r){this.snackBar.open(r,"Close",{duration:3e3,panelClass:["success-snackbar"]})}get emailControl(){return this.loginForm.get("email")}get passwordControl(){return this.loginForm.get("password")}static \u0275fac=function(e){return new(e||i)(g(z),g(O),g($),g(R),g(V))};static \u0275cmp=w({type:i,selectors:[["app-login"]],standalone:!1,decls:44,vars:17,consts:[[1,"auth-container"],[1,"auth-card"],[1,"auth-header"],[1,"auth-logo"],["src","logo/logo2.png","alt","Chattrix Logo",3,"error"],[1,"logo-fallback"],[1,"logo-text"],[1,"auth-title"],[1,"auth-subtitle"],[1,"auth-form",3,"ngSubmit","formGroup"],["appearance","fill",1,"full-width"],["matInput","","type","email","formControlName","email","placeholder","Enter your email","autocomplete","email"],["matSuffix",""],[4,"ngIf"],["matInput","","formControlName","password","placeholder","Enter your password","autocomplete","current-password",3,"type"],["mat-icon-button","","matSuffix","","type","button",3,"click"],[1,"forgot-password-container"],["type","button","mat-button","","color","primary",1,"forgot-password-link",3,"click"],["mat-raised-button","","color","primary","type","submit",1,"login-button","full-width",3,"disabled"],[1,"button-content"],["diameter","30","class","button-spinner",4,"ngIf"],[1,"auth-footer"],[1,"signup-prompt"],["mat-button","","color","primary",1,"signup-link",3,"click"],["diameter","30",1,"button-spinner"]],template:function(e,t){e&1&&(o(0,"div",0)(1,"mat-card",1)(2,"div",2)(3,"div",3)(4,"img",4),u("error",function(s){return t.onImageError(s)}),a(),o(5,"div",5)(6,"span",6),d(7,"C"),a()()(),o(8,"h1",7),d(9,"Welcome Back"),a(),o(10,"p",8),d(11,"Sign in to your Chattrix account"),a()(),o(12,"form",9),u("ngSubmit",function(){return t.onLogin()}),o(13,"mat-form-field",10)(14,"mat-label"),d(15,"Email Address"),a(),h(16,"input",11),o(17,"mat-icon",12),d(18,"email"),a(),f(19,Kr,2,0,"mat-error",13)(20,Xr,2,0,"mat-error",13),a(),o(21,"mat-form-field",10)(22,"mat-label"),d(23,"Password"),a(),h(24,"input",14),o(25,"button",15),u("click",function(){return t.togglePasswordVisibility()}),o(26,"mat-icon"),d(27),a()(),f(28,Jr,2,0,"mat-error",13)(29,en,2,0,"mat-error",13),a(),o(30,"div",16)(31,"button",17),u("click",function(){return t.onForgotPassword()}),d(32," Forgot your password? "),a()(),o(33,"button",18)(34,"div",19),f(35,tn,1,0,"mat-spinner",20),o(36,"span"),d(37,"Sign In"),a()()()(),o(38,"div",21),h(39,"mat-divider"),o(40,"p",22),d(41," Don't have an account? "),o(42,"button",23),u("click",function(){return t.onSignUp()}),d(43," Sign Up "),a()()()()()),e&2&&(l(4),P("display",t.logoLoaded?"block":"none"),l(),P("display",t.logoLoaded?"none":"flex"),l(7),c("formGroup",t.loginForm),l(7),c("ngIf",t.emailControl==null?null:t.emailControl.hasError("required")),l(),c("ngIf",t.emailControl==null?null:t.emailControl.hasError("email")),l(4),c("type",t.hidePassword?"password":"text"),l(),k("aria-label","Hide password")("aria-pressed",t.hidePassword),l(2),re(t.hidePassword?"visibility_off":"visibility"),l(),c("ngIf",t.passwordControl==null?null:t.passwordControl.hasError("required")),l(),c("ngIf",t.passwordControl==null?null:t.passwordControl.hasError("minlength")),l(4),c("disabled",t.loginForm.invalid||t.isLoading),l(2),c("ngIf",t.isLoading),l(),v("hidden",t.isLoading))},dependencies:[H,G,ne,oe,U,L,ae,W,X,N,pe,fe,se,D,ve,Q,Y,Z],styles:[".full-width[_ngcontent-%COMP%]{width:100%;margin-bottom:var(--spacing-md)}.forgot-password-container[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;margin-bottom:var(--spacing-md)}.forgot-password-link[_ngcontent-%COMP%]{font-size:var(--font-size-sm);color:var(--accent-green)!important;text-transform:none;padding:0;min-width:auto}.forgot-password-link[_ngcontent-%COMP%]:hover{color:var(--accent-green-hover)!important;background:transparent}.login-button[_ngcontent-%COMP%]{height:44px;font-size:var(--font-size-base);font-weight:500;text-transform:none;border-radius:var(--radius-md);margin-bottom:var(--spacing-md);position:relative}.login-button[_ngcontent-%COMP%]:disabled{opacity:.6}.login-button[_ngcontent-%COMP%]   .button-content[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:var(--spacing-sm);position:relative}.login-button[_ngcontent-%COMP%]   .hidden[_ngcontent-%COMP%]{visibility:hidden;opacity:0}.button-spinner[_ngcontent-%COMP%]{position:absolute;transform:translate(-50%,-50%)}.signup-prompt[_ngcontent-%COMP%]{margin:var(--spacing-lg) 0 0;text-align:center;color:var(--text-secondary);font-size:var(--font-size-sm)}.signup-link[_ngcontent-%COMP%]{color:var(--accent-green)!important;text-transform:none;font-weight:500;padding:0;min-width:auto;margin-left:var(--spacing-xs)}.signup-link[_ngcontent-%COMP%]:hover{color:var(--accent-green-hover)!important;background:transparent}  .mat-mdc-form-field .mat-mdc-text-field-wrapper{background-color:var(--bg-input);border-radius:var(--radius-md)}  .mat-mdc-form-field .mat-mdc-form-field-label{color:var(--text-secondary)}  .mat-mdc-form-field .mat-mdc-form-field-input{color:var(--text-primary)}  .mat-mdc-form-field .mat-mdc-form-field-icon-suffix{color:var(--text-secondary)}  .mat-mdc-form-field.mat-focused .mat-mdc-form-field-label{color:var(--accent-green)}  .error-snackbar{background-color:var(--error)!important;color:var(--text-primary)!important}  .success-snackbar{background-color:var(--success)!important;color:var(--text-primary)!important}@media (max-width: 480px){.auth-card[_ngcontent-%COMP%]{margin:var(--spacing-sm);max-width:calc(100vw - 2rem)}.auth-header[_ngcontent-%COMP%], .auth-form[_ngcontent-%COMP%]{padding:var(--spacing-lg) var(--spacing-md)}.auth-footer[_ngcontent-%COMP%]{padding:var(--spacing-md)}.auth-logo[_ngcontent-%COMP%]{width:60px;height:60px}.auth-title[_ngcontent-%COMP%]{font-size:var(--font-size-xl)}}.mat-mdc-button[_ngcontent-%COMP%]:focus{outline:2px solid var(--accent-green);outline-offset:2px}.mat-mdc-form-field[_ngcontent-%COMP%]:focus-within   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%]{border:2px solid var(--accent-green)}.button-spinner[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_spin 1s linear infinite}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.logo-fallback[_ngcontent-%COMP%]{width:100%;height:100%;display:flex;align-items:center;justify-content:center;background:linear-gradient(135deg,var(--accent-green) 0%,var(--accent-green-light) 100%);border-radius:50%}.logo-text[_ngcontent-%COMP%]{font-size:var(--font-size-2xl);font-weight:700;color:var(--text-primary);text-shadow:0 2px 4px rgba(0,0,0,.3)}"]})};function rn(i,r){i&1&&(o(0,"mat-error"),d(1," Full name is required "),a())}function nn(i,r){i&1&&(o(0,"mat-error"),d(1," Full name must be at least 2 characters long "),a())}function on(i,r){i&1&&(o(0,"mat-error"),d(1," Email is required "),a())}function an(i,r){i&1&&(o(0,"mat-error"),d(1," Please enter a valid email address "),a())}function sn(i,r){i&1&&(o(0,"mat-error"),d(1," Please enter a valid phone number "),a())}function ln(i,r){i&1&&(o(0,"mat-error"),d(1," Phone number must be at least 10 digits long "),a())}function dn(i,r){i&1&&(o(0,"mat-error"),d(1," Password is required "),a())}function cn(i,r){i&1&&(o(0,"mat-error"),d(1," Password must be at least 8 characters long "),a())}function mn(i,r){i&1&&(o(0,"mat-error"),d(1," Password must contain uppercase, lowercase, number, and special character "),a())}function pn(i,r){i&1&&(o(0,"mat-error"),d(1," Please confirm your password "),a())}function fn(i,r){i&1&&(o(0,"mat-error"),d(1," Passwords do not match "),a())}function un(i,r){i&1&&h(0,"mat-spinner",21)}var mt=class i{constructor(r,e,t,n,s){this.authService=r;this.authState=e;this.formBuilder=t;this.router=n;this.snackBar=s;this.signupForm=this.formBuilder.group({fullName:["",[_.required,_.minLength(2)]],email:["",[_.required,_.email]],password:["",[_.required,_.minLength(8),this.passwordValidator]],confirmPassword:["",[_.required]],phoneNumber:["",[_.pattern(/^\+?[\d\s\-\(\)]+$/),_.maxLength(15),_.minLength(10),_.required]]},{validators:this.passwordMatchValidator})}destroy$=new x;signupForm;hidePassword=!0;hideConfirmPassword=!0;logoLoaded=!0;isAuthenticated=!1;isLoading=!1;user=null;error=null;ngOnInit(){this.authState.authState$.pipe(C(this.destroy$)).subscribe(r=>{this.isAuthenticated=r.isAuthenticated,this.isLoading=r.isLoading,this.user=r.user,this.error=r.error,r.error&&this.showError(r.error),r.isAuthenticated&&r.user&&this.router.navigate(["/dashboard"])})}ngOnDestroy(){this.destroy$.next(),this.destroy$.complete()}onSignUp(){if(this.signupForm.valid){let r={fullName:this.signupForm.value.fullName,email:this.signupForm.value.email,password:this.signupForm.value.password,phoneNumber:this.signupForm.value.phoneNumber||void 0};sessionStorage.setItem("pendingSignupData",JSON.stringify(r)),this.showSuccess("Basic information saved! Please complete your profile."),this.router.navigate(["/auth/profile-picture"])}else this.markFormGroupTouched()}onLogin(){this.router.navigate(["/auth/login"])}togglePasswordVisibility(){this.hidePassword=!this.hidePassword}toggleConfirmPasswordVisibility(){this.hideConfirmPassword=!this.hideConfirmPassword}passwordValidator(r){let e=r.value;if(!e)return null;let t=/[A-Z]/.test(e),n=/[a-z]/.test(e),s=/[0-9]/.test(e),m=/[!@#$%^&*(),.?":{}|<>]/.test(e);return t&&n&&s&&m?null:{passwordStrength:{hasUpperCase:t,hasLowerCase:n,hasNumeric:s,hasSpecialChar:m}}}passwordMatchValidator(r){let e=r.get("password"),t=r.get("confirmPassword");return!e||!t||e.value===t.value?null:{passwordMismatch:!0}}markFormGroupTouched(){Object.keys(this.signupForm.controls).forEach(r=>{this.signupForm.get(r)?.markAsTouched()})}showError(r){this.snackBar.open(r,"Close",{duration:5e3,panelClass:["error-snackbar"]})}showSuccess(r){this.snackBar.open(r,"Close",{duration:5e3,panelClass:["success-snackbar"]})}get fullNameControl(){return this.signupForm.get("fullName")}get emailControl(){return this.signupForm.get("email")}get passwordControl(){return this.signupForm.get("password")}get confirmPasswordControl(){return this.signupForm.get("confirmPassword")}get phoneNumberControl(){return this.signupForm.get("phoneNumber")}static \u0275fac=function(e){return new(e||i)(g(z),g(O),g($),g(R),g(V))};static \u0275cmp=w({type:i,selectors:[["app-sign-up"]],standalone:!1,decls:62,vars:24,consts:[[1,"auth-container"],[1,"auth-card","signup-card"],[1,"auth-header","mt-4"],[1,"auth-title"],[1,"auth-subtitle"],[1,"auth-form",3,"ngSubmit","formGroup"],["appearance","fill",1,"full-width"],["matInput","","type","text","formControlName","fullName","placeholder","Enter your full name","autocomplete","name"],["matSuffix",""],[4,"ngIf"],["matInput","","type","email","formControlName","email","placeholder","Enter your email","autocomplete","email"],["matInput","","type","tel","formControlName","phoneNumber","placeholder","Enter your phone number","autocomplete","tel"],["matInput","","formControlName","password","placeholder","Create a strong password","autocomplete","new-password",3,"type"],["mat-icon-button","","matSuffix","","type","button",3,"click"],["matInput","","formControlName","confirmPassword","placeholder","Confirm your password","autocomplete","new-password",3,"type"],["mat-raised-button","","color","primary","type","submit",1,"signup-button","full-width",3,"disabled"],[1,"button-content"],["diameter","20","class","button-spinner",4,"ngIf"],[1,"auth-footer"],[1,"login-prompt"],["mat-button","","color","primary",1,"login-link",3,"click"],["diameter","20",1,"button-spinner"]],template:function(e,t){e&1&&(o(0,"div",0)(1,"mat-card",1)(2,"div",2)(3,"h1",3),d(4,"Join Chattrix"),a(),o(5,"p",4),d(6,"Create your account to get started"),a()(),o(7,"form",5),u("ngSubmit",function(){return t.onSignUp()}),o(8,"mat-form-field",6)(9,"mat-label"),d(10,"Full Name"),a(),h(11,"input",7),o(12,"mat-icon",8),d(13,"person"),a(),f(14,rn,2,0,"mat-error",9)(15,nn,2,0,"mat-error",9),a(),o(16,"mat-form-field",6)(17,"mat-label"),d(18,"Email Address"),a(),h(19,"input",10),o(20,"mat-icon",8),d(21,"email"),a(),f(22,on,2,0,"mat-error",9)(23,an,2,0,"mat-error",9),a(),o(24,"mat-form-field",6)(25,"mat-label"),d(26,"Phone Number"),a(),h(27,"input",11),o(28,"mat-icon",8),d(29,"phone"),a(),f(30,sn,2,0,"mat-error",9)(31,ln,2,0,"mat-error",9),a(),o(32,"mat-form-field",6)(33,"mat-label"),d(34,"Password"),a(),h(35,"input",12),o(36,"button",13),u("click",function(){return t.togglePasswordVisibility()}),o(37,"mat-icon"),d(38),a()(),f(39,dn,2,0,"mat-error",9)(40,cn,2,0,"mat-error",9)(41,mn,2,0,"mat-error",9),a(),o(42,"mat-form-field",6)(43,"mat-label"),d(44,"Confirm Password"),a(),h(45,"input",14),o(46,"button",13),u("click",function(){return t.toggleConfirmPasswordVisibility()}),o(47,"mat-icon"),d(48),a()(),f(49,pn,2,0,"mat-error",9)(50,fn,2,0,"mat-error",9),a(),o(51,"button",15)(52,"div",16),f(53,un,1,0,"mat-spinner",17),o(54,"span"),d(55,"Continue"),a()()()(),o(56,"div",18),h(57,"mat-divider"),o(58,"p",19),d(59," Already have an account? "),o(60,"button",20),u("click",function(){return t.onLogin()}),d(61," Sign In "),a()()()()()),e&2&&(l(7),c("formGroup",t.signupForm),l(7),c("ngIf",t.fullNameControl==null?null:t.fullNameControl.hasError("required")),l(),c("ngIf",t.fullNameControl==null?null:t.fullNameControl.hasError("minlength")),l(7),c("ngIf",t.emailControl==null?null:t.emailControl.hasError("required")),l(),c("ngIf",t.emailControl==null?null:t.emailControl.hasError("email")),l(7),c("ngIf",t.phoneNumberControl==null?null:t.phoneNumberControl.hasError("pattern")),l(),c("ngIf",t.phoneNumberControl==null?null:t.phoneNumberControl.hasError("minlength")),l(4),c("type",t.hidePassword?"password":"text"),l(),k("aria-label","Hide password")("aria-pressed",t.hidePassword),l(2),re(t.hidePassword?"visibility_off":"visibility"),l(),c("ngIf",t.passwordControl==null?null:t.passwordControl.hasError("required")),l(),c("ngIf",t.passwordControl==null?null:t.passwordControl.hasError("minlength")),l(),c("ngIf",t.passwordControl==null?null:t.passwordControl.hasError("passwordStrength")),l(4),c("type",t.hideConfirmPassword?"password":"text"),l(),k("aria-label","Hide password")("aria-pressed",t.hideConfirmPassword),l(2),re(t.hideConfirmPassword?"visibility_off":"visibility"),l(),c("ngIf",t.confirmPasswordControl==null?null:t.confirmPasswordControl.hasError("required")),l(),c("ngIf",t.signupForm.hasError("passwordMismatch")&&(t.confirmPasswordControl==null?null:t.confirmPasswordControl.touched)),l(),c("disabled",t.signupForm.invalid||t.isLoading),l(2),c("ngIf",t.isLoading),l(),v("hidden",t.isLoading))},dependencies:[H,G,ne,oe,U,L,ae,W,X,N,pe,fe,se,D,ve,Q,Y,Z],styles:[".signup-card[_ngcontent-%COMP%]{max-width:480px}.signup-card[_ngcontent-%COMP%]   .auth-header[_ngcontent-%COMP%]{padding:var(--spacing-sm) var(--spacing-lg) var(--spacing-xs)}.signup-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%], .signup-card[_ngcontent-%COMP%]   .auth-footer[_ngcontent-%COMP%]{padding:var(--spacing-sm) var(--spacing-lg)}.signup-card[_ngcontent-%COMP%]   .auth-logo[_ngcontent-%COMP%]{width:50px;height:50px;margin-bottom:var(--spacing-sm)}.signup-card[_ngcontent-%COMP%]   .auth-title[_ngcontent-%COMP%]{font-size:var(--font-size-lg);margin-bottom:var(--spacing-xs)}.signup-card[_ngcontent-%COMP%]   .auth-subtitle[_ngcontent-%COMP%]{font-size:var(--font-size-xs);margin-bottom:0}.full-width[_ngcontent-%COMP%]{width:100%;margin-bottom:var(--spacing-sm)}.signup-button[_ngcontent-%COMP%]{height:44px;font-size:var(--font-size-base);font-weight:500;text-transform:none;border-radius:var(--radius-md);margin-bottom:var(--spacing-sm);position:relative}.signup-button[_ngcontent-%COMP%]:disabled{opacity:.6}.signup-button[_ngcontent-%COMP%]   .button-content[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:var(--spacing-sm);position:relative}.signup-button[_ngcontent-%COMP%]   .hidden[_ngcontent-%COMP%]{visibility:hidden;opacity:0}.button-spinner[_ngcontent-%COMP%]{position:absolute;transform:translate(-50%,-50%)}.login-prompt[_ngcontent-%COMP%]{margin:var(--spacing-sm) 0 0;text-align:center;color:var(--text-secondary);font-size:var(--font-size-sm)}.login-link[_ngcontent-%COMP%]{color:var(--accent-green)!important;text-transform:none;font-weight:500;padding:0;min-width:auto;margin-left:var(--spacing-xs)}.login-link[_ngcontent-%COMP%]:hover{color:var(--accent-green-hover)!important;background:transparent}.password-strength[_ngcontent-%COMP%]{margin-top:var(--spacing-sm);padding:var(--spacing-sm);background:var(--bg-tertiary);border-radius:var(--radius-sm);font-size:var(--font-size-xs)}.strength-requirement[_ngcontent-%COMP%]{display:flex;align-items:center;margin-bottom:var(--spacing-xs)}.strength-requirement[_ngcontent-%COMP%]:last-child{margin-bottom:0}.strength-requirement[_ngcontent-%COMP%]   .requirement-icon[_ngcontent-%COMP%]{width:16px;height:16px;margin-right:var(--spacing-xs);border-radius:50%;display:flex;align-items:center;justify-content:center;font-size:10px}.strength-requirement[_ngcontent-%COMP%]   .requirement-icon.met[_ngcontent-%COMP%]{background-color:var(--success);color:#fff}.strength-requirement[_ngcontent-%COMP%]   .requirement-icon.unmet[_ngcontent-%COMP%]{background-color:var(--text-disabled);color:#fff}.strength-requirement[_ngcontent-%COMP%]   .requirement-text.met[_ngcontent-%COMP%]{color:var(--success)}.strength-requirement[_ngcontent-%COMP%]   .requirement-text.unmet[_ngcontent-%COMP%]{color:var(--text-disabled)}  .signup-card .mat-mdc-form-field .mat-mdc-text-field-wrapper{background-color:var(--bg-input);border-radius:var(--radius-md)}  .signup-card .mat-mdc-form-field .mat-mdc-form-field-label{color:var(--text-secondary)}  .signup-card .mat-mdc-form-field .mat-mdc-form-field-input{color:var(--text-primary)}  .signup-card .mat-mdc-form-field .mat-mdc-form-field-icon-suffix{color:var(--text-secondary)}  .signup-card .mat-mdc-form-field.mat-focused .mat-mdc-form-field-label{color:var(--accent-green)}  .signup-card .mat-mdc-form-field .mat-mdc-form-field-wrapper{padding-bottom:0}  .signup-card .mat-mdc-form-field .mat-mdc-form-field-subscript-wrapper{margin-top:2px;min-height:14px;font-size:var(--font-size-xs)}  .signup-card .mat-mdc-form-field textarea.mat-mdc-input-element{resize:vertical;min-height:50px}  .mat-mdc-form-field-hint{color:var(--text-muted);font-size:var(--font-size-xs)}  .error-snackbar{background-color:var(--error)!important;color:var(--text-primary)!important}  .success-snackbar{background-color:var(--success)!important;color:var(--text-primary)!important}.signup-card[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]{min-height:100vh;min-height:100dvh;display:flex;align-items:center;justify-content:center;padding:var(--spacing-xs);box-sizing:border-box}@media (max-width: 768px){.signup-card[_ngcontent-%COMP%]   .auth-header[_ngcontent-%COMP%]{padding:var(--spacing-xs) var(--spacing-md) var(--spacing-xs)}.signup-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%], .signup-card[_ngcontent-%COMP%]   .auth-footer[_ngcontent-%COMP%]{padding:var(--spacing-xs) var(--spacing-md)}.signup-card[_ngcontent-%COMP%]   .full-width[_ngcontent-%COMP%]{margin-bottom:var(--spacing-xs)}}@media (max-width: 480px){.signup-card[_ngcontent-%COMP%]{margin:var(--spacing-xs);max-width:calc(100vw - 1rem)}.signup-card[_ngcontent-%COMP%]   .auth-header[_ngcontent-%COMP%]{padding:var(--spacing-xs) var(--spacing-sm) var(--spacing-xs)}.signup-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%], .signup-card[_ngcontent-%COMP%]   .auth-footer[_ngcontent-%COMP%]{padding:var(--spacing-xs) var(--spacing-sm)}.signup-card[_ngcontent-%COMP%]   .auth-logo[_ngcontent-%COMP%]{width:40px;height:40px;margin-bottom:var(--spacing-xs)}.signup-card[_ngcontent-%COMP%]   .auth-title[_ngcontent-%COMP%]{font-size:var(--font-size-base);margin-bottom:2px}.signup-card[_ngcontent-%COMP%]   .auth-subtitle[_ngcontent-%COMP%]{font-size:11px;margin-bottom:0}.signup-card[_ngcontent-%COMP%]   .full-width[_ngcontent-%COMP%]{margin-bottom:6px}.signup-card[_ngcontent-%COMP%]   .signup-button[_ngcontent-%COMP%]{height:40px;margin-bottom:6px}.signup-card[_ngcontent-%COMP%]   .login-prompt[_ngcontent-%COMP%]{margin:6px 0 0;font-size:11px}}.mat-mdc-button[_ngcontent-%COMP%]:focus{outline:2px solid var(--accent-green);outline-offset:2px}.mat-mdc-form-field[_ngcontent-%COMP%]:focus-within   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%]{border:2px solid var(--accent-green)}.button-spinner[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_spin 1s linear infinite}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.mat-mdc-form-field.ng-invalid.ng-touched[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%]{border:1px solid var(--error)}.mat-mdc-form-field.ng-valid.ng-touched[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%]{border:1px solid var(--success)}.logo-fallback[_ngcontent-%COMP%]{width:100%;height:100%;display:flex;align-items:center;justify-content:center;background:linear-gradient(135deg,var(--accent-green) 0%,var(--accent-green-light) 100%);border-radius:50%}.logo-text[_ngcontent-%COMP%]{font-size:var(--font-size-2xl);font-weight:700;color:var(--text-primary);text-shadow:0 2px 4px rgba(0,0,0,.3)}"]})};function hn(i,r){i&1&&(o(0,"mat-error"),d(1," Verification code is required "),a())}function gn(i,r){i&1&&(o(0,"mat-error"),d(1," Please enter a valid 5-digit numeric code "),a())}function _n(i,r){i&1&&(o(0,"mat-error"),d(1," Verification code must be exactly 5 digits "),a())}function vn(i,r){i&1&&h(0,"mat-spinner",24)}function bn(i,r){if(i&1&&(o(0,"button",25)(1,"mat-icon"),d(2,"schedule"),a(),d(3),a()),i&2){let e=y();l(3),ue(" Resend in ",e.timerDisplay," ")}}function xn(i,r){i&1&&h(0,"mat-spinner",29)}function yn(i,r){if(i&1&&(o(0,"mat-icon"),d(1,"refresh"),a()),i&2){let e=y(2);v("hidden",e.isResending)}}function Cn(i,r){if(i&1){let e=de();o(0,"button",26),u("click",function(){ee(e);let n=y();return te(n.onResendOtp())}),o(1,"div",15),f(2,xn,1,0,"mat-spinner",27)(3,yn,2,2,"mat-icon",28),o(4,"span"),d(5,"Resend Code"),a()()()}if(i&2){let e=y();c("disabled",e.isResending),l(2),c("ngIf",e.isResending),l(),c("ngIf",!e.isResending),l(),v("hidden",e.isResending)}}var pt=class i{constructor(r,e,t,n,s,m){this.authService=r;this.authState=e;this.formBuilder=t;this.router=n;this.route=s;this.snackBar=m;this.otpForm=this.formBuilder.group({otpCode:["",[_.required,_.pattern(/^\d{5}$/),_.minLength(5),_.maxLength(5)]]})}destroy$=new x;timerSubscription;otpForm;userId="";logoLoaded=!0;resendTimer=0;canResend=!1;isAuthenticated=!1;isLoading=!1;isResending=!1;user=null;error=null;ngOnInit(){this.route.queryParams.pipe(C(this.destroy$)).subscribe(r=>{if(this.userId=r.userId,!this.userId){this.router.navigate(["/auth/login"]);return}}),this.authState.authState$.pipe(C(this.destroy$)).subscribe(r=>{this.isAuthenticated=r.isAuthenticated,this.isLoading=r.isLoading,this.user=r.user,this.error=r.error,r.error&&this.showError(r.error),r.isAuthenticated&&r.user&&this.router.navigate(["/dashboard"])}),this.startResendTimer()}ngOnDestroy(){this.destroy$.next(),this.destroy$.complete(),this.timerSubscription?.unsubscribe()}onOtpInput(r){let n=r.target.value.replace(/\D/g,"").slice(0,5);this.otpForm.get("otpCode")?.setValue(n),n.length===5&&setTimeout(()=>this.onVerifyOtp(),100)}onOtpPaste(r){r.preventDefault();let t=(r.clipboardData?.getData("text")||"").replace(/\D/g,"").slice(0,5);this.otpForm.get("otpCode")?.setValue(t),t.length===5&&setTimeout(()=>this.onVerifyOtp(),100)}onVerifyOtp(){let r=this.otpForm.get("otpCode")?.value||"";this.otpForm.valid&&r.length===5&&/^\d{5}$/.test(r)?this.authService.loginWith2FA(this.userId,r).subscribe({next:e=>{e.isSuccess&&(this.showSuccess("OTP verified successfully!"),this.router.navigate(["/dashboard"]))},error:e=>{console.error("OTP verification failed:",e),this.clearOtp()}}):(this.showError("Please enter a valid 5-digit OTP"),this.markFormGroupTouched())}onResendOtp(){this.canResend&&(this.isResending=!0,this.authService.resendOtp(this.userId).subscribe({next:r=>{r.isSuccess&&(this.showSuccess("OTP sent successfully!"),this.clearOtp(),this.startResendTimer()),this.isResending=!1},error:r=>{console.error("Resend OTP failed:",r),this.isResending=!1}}))}onBackToLogin(){this.router.navigate(["/auth/login"])}onImageError(r){this.logoLoaded=!1,console.log("Logo failed to load, showing fallback")}clearOtp(){this.otpForm.get("otpCode")?.setValue(""),this.otpForm.get("otpCode")?.markAsUntouched()}startResendTimer(){this.resendTimer=60,this.canResend=!1,this.timerSubscription?.unsubscribe(),this.timerSubscription=Ye(1e3).pipe(C(this.destroy$)).subscribe(()=>{this.resendTimer--,this.resendTimer<=0&&(this.canResend=!0,this.timerSubscription?.unsubscribe())})}markFormGroupTouched(){Object.keys(this.otpForm.controls).forEach(r=>{this.otpForm.get(r)?.markAsTouched()})}showError(r){this.snackBar.open(r,"Close",{duration:5e3,panelClass:["error-snackbar"]})}showSuccess(r){this.snackBar.open(r,"Close",{duration:3e3,panelClass:["success-snackbar"]})}get timerDisplay(){let r=Math.floor(this.resendTimer/60),e=this.resendTimer%60;return`${r}:${e.toString().padStart(2,"0")}`}static \u0275fac=function(e){return new(e||i)(g(z),g(O),g($),g(R),g(Fe),g(V))};static \u0275cmp=w({type:i,selectors:[["app-otp-verification"]],standalone:!1,decls:38,vars:14,consts:[[1,"auth-container"],[1,"auth-card","otp-card"],[1,"auth-header"],[1,"auth-logo"],["src","/logo/logo2.png","alt","Chattrix Logo",3,"error"],[1,"logo-fallback"],[1,"logo-text"],[1,"auth-title"],[1,"auth-subtitle"],[1,"auth-form",3,"ngSubmit","formGroup"],[1,"otp-container"],["appearance","fill",1,"otp-input-field"],["matInput","","formControlName","otpCode","type","text","maxlength","5","placeholder","12345","autocomplete","one-time-code","inputmode","numeric","pattern","[0-9]*",1,"otp-input-single",3,"input","paste"],[4,"ngIf"],["mat-raised-button","","color","primary","type","submit",1,"verify-button","full-width",3,"disabled"],[1,"button-content"],["diameter","20","class","button-spinner",4,"ngIf"],[1,"resend-section"],[1,"resend-text"],["mat-button","","type","button","class","resend-button disabled","disabled","",4,"ngIf"],["mat-button","","color","primary","type","button","class","resend-button",3,"disabled","click",4,"ngIf"],[1,"auth-footer"],[1,"back-to-login"],["mat-button","","color","primary",1,"back-button",3,"click"],["diameter","20",1,"button-spinner"],["mat-button","","type","button","disabled","",1,"resend-button","disabled"],["mat-button","","color","primary","type","button",1,"resend-button",3,"click","disabled"],["diameter","16","class","resend-spinner",4,"ngIf"],[3,"hidden",4,"ngIf"],["diameter","16",1,"resend-spinner"]],template:function(e,t){if(e&1&&(o(0,"div",0)(1,"mat-card",1)(2,"div",2)(3,"div",3)(4,"img",4),u("error",function(s){return t.onImageError(s)}),a(),o(5,"div",5)(6,"span",6),d(7,"C"),a()()(),o(8,"h1",7),d(9,"Verify Your Account"),a(),o(10,"p",8),d(11," We've sent a 5-digit verification code to your email address. Please enter it below to complete your verification. "),a()(),o(12,"form",9),u("ngSubmit",function(){return t.onVerifyOtp()}),o(13,"div",10)(14,"mat-form-field",11)(15,"mat-label"),d(16,"Enter 5-digit verification code"),a(),o(17,"input",12),u("input",function(s){return t.onOtpInput(s)})("paste",function(s){return t.onOtpPaste(s)}),a(),f(18,hn,2,0,"mat-error",13)(19,gn,2,0,"mat-error",13)(20,_n,2,0,"mat-error",13),a()(),o(21,"button",14)(22,"div",15),f(23,vn,1,0,"mat-spinner",16),o(24,"span"),d(25,"Verify Code"),a()()(),o(26,"div",17)(27,"p",18),d(28,"Didn't receive the code?"),a(),f(29,bn,4,1,"button",19)(30,Cn,6,5,"button",20),a()(),o(31,"div",21),h(32,"mat-divider"),o(33,"div",22)(34,"button",23),u("click",function(){return t.onBackToLogin()}),o(35,"mat-icon"),d(36,"arrow_back"),a(),d(37," Back to Login "),a()()()()()),e&2){let n,s,m;l(4),P("display",t.logoLoaded?"block":"none"),l(),P("display",t.logoLoaded?"none":"flex"),l(7),c("formGroup",t.otpForm),l(6),c("ngIf",(n=t.otpForm.get("otpCode"))==null?null:n.hasError("required")),l(),c("ngIf",(s=t.otpForm.get("otpCode"))==null?null:s.hasError("pattern")),l(),c("ngIf",((m=t.otpForm.get("otpCode"))==null?null:m.hasError("minlength"))||((m=t.otpForm.get("otpCode"))==null?null:m.hasError("maxlength"))),l(),c("disabled",t.otpForm.invalid||t.isLoading),l(2),c("ngIf",t.isLoading),l(),v("hidden",t.isLoading),l(5),c("ngIf",!t.canResend),l(),c("ngIf",t.canResend)}},dependencies:[H,G,ne,oe,U,nt,mi,L,ae,W,X,N,pe,se,D,Q,Y,Z],styles:[".otp-card[_ngcontent-%COMP%]{max-width:420px}.full-width[_ngcontent-%COMP%]{width:100%}.otp-container[_ngcontent-%COMP%]{margin-bottom:var(--spacing-xl)}.otp-input-field[_ngcontent-%COMP%]{width:100%}.otp-input-field[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%]{background-color:var(--bg-input);border-radius:var(--radius-md)}.otp-input-field[_ngcontent-%COMP%]   .mat-mdc-form-field-input[_ngcontent-%COMP%]{color:var(--text-primary);font-size:var(--font-size-xl);font-weight:600;text-align:center;letter-spacing:.5em;padding:var(--spacing-md)}.otp-input-field[_ngcontent-%COMP%]   .mat-mdc-form-field-label[_ngcontent-%COMP%]{color:var(--text-secondary)}.otp-input-field.mat-focused[_ngcontent-%COMP%]   .mat-mdc-form-field-label[_ngcontent-%COMP%]{color:var(--accent-green)}.otp-input-field.mat-focused[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%]{border-color:var(--accent-green);box-shadow:0 0 0 3px #10b9811a}.otp-input-field.mat-form-field-invalid[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%]{border-color:var(--error);box-shadow:0 0 0 3px #ef44441a}.otp-input-single[_ngcontent-%COMP%]{text-align:center;letter-spacing:.3em;font-size:var(--font-size-xl);font-weight:600}.verify-button[_ngcontent-%COMP%]{height:48px;font-size:var(--font-size-base);font-weight:500;text-transform:none;border-radius:var(--radius-md);margin-bottom:var(--spacing-xl);position:relative}.verify-button[_ngcontent-%COMP%]:disabled{opacity:.6}.verify-button[_ngcontent-%COMP%]   .button-content[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:var(--spacing-sm);position:relative}.verify-button[_ngcontent-%COMP%]   .hidden[_ngcontent-%COMP%]{visibility:hidden;opacity:0}.button-spinner[_ngcontent-%COMP%]{position:absolute;transform:translate(-50%,-50%)}.resend-section[_ngcontent-%COMP%]{text-align:center;margin-bottom:var(--spacing-lg)}.resend-text[_ngcontent-%COMP%]{color:var(--text-secondary);font-size:var(--font-size-sm);margin-bottom:var(--spacing-md)}.resend-button[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spacing-xs);font-size:var(--font-size-sm);text-transform:none;margin:0 auto;position:relative}.resend-button[_ngcontent-%COMP%]:not(.disabled){color:var(--accent-green)!important}.resend-button[_ngcontent-%COMP%]:not(.disabled):hover{color:var(--accent-green-hover)!important;background:transparent}.resend-button.disabled[_ngcontent-%COMP%]{color:var(--text-disabled)!important;cursor:not-allowed}.resend-button[_ngcontent-%COMP%]   .button-content[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:var(--spacing-xs);position:relative}.resend-button[_ngcontent-%COMP%]   .hidden[_ngcontent-%COMP%]{visibility:hidden;opacity:0}.resend-spinner[_ngcontent-%COMP%]{position:absolute;left:50%;top:50%;transform:translate(-50%,-50%)}.back-to-login[_ngcontent-%COMP%]{text-align:center;padding-top:var(--spacing-lg)}.back-button[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spacing-xs);color:var(--accent-green)!important;text-transform:none;font-size:var(--font-size-sm);margin:0 auto}.back-button[_ngcontent-%COMP%]:hover{color:var(--accent-green-hover)!important;background:transparent}  .error-snackbar{background-color:var(--error)!important;color:var(--text-primary)!important}  .success-snackbar{background-color:var(--success)!important;color:var(--text-primary)!important}@media (max-width: 480px){.otp-card[_ngcontent-%COMP%]{margin:var(--spacing-sm);max-width:calc(100vw - 2rem)}.auth-header[_ngcontent-%COMP%], .auth-form[_ngcontent-%COMP%]{padding:var(--spacing-lg) var(--spacing-md)}.auth-footer[_ngcontent-%COMP%]{padding:var(--spacing-md)}.auth-logo[_ngcontent-%COMP%]{width:60px;height:60px}.auth-title[_ngcontent-%COMP%]{font-size:var(--font-size-xl)}.otp-input-field[_ngcontent-%COMP%]   .mat-mdc-form-field-input[_ngcontent-%COMP%]{font-size:var(--font-size-lg);letter-spacing:.2em}}@media (max-width: 360px){.otp-input-field[_ngcontent-%COMP%]   .mat-mdc-form-field-input[_ngcontent-%COMP%]{font-size:var(--font-size-base);letter-spacing:.1em}}.mat-mdc-button[_ngcontent-%COMP%]:focus{outline:2px solid var(--accent-green);outline-offset:2px}.button-spinner[_ngcontent-%COMP%], .resend-spinner[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_spin 1s linear infinite}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.otp-input-field[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeIn .3s ease-in-out}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0;transform:translateY(-10px)}to{opacity:1;transform:translateY(0)}}.otp-input-field.mat-form-field-valid[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_successPulse .6s ease-in-out;border-color:var(--success);background-color:#10b9811a}@keyframes _ngcontent-%COMP%_successPulse{0%{transform:scale(1)}50%{transform:scale(1.02)}to{transform:scale(1)}}.resend-button[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse 2s infinite}@keyframes _ngcontent-%COMP%_pulse{0%{opacity:1}50%{opacity:.5}to{opacity:1}}.logo-fallback[_ngcontent-%COMP%]{width:100%;height:100%;display:flex;align-items:center;justify-content:center;background:linear-gradient(135deg,var(--accent-green) 0%,var(--accent-green-light) 100%);border-radius:50%}.logo-text[_ngcontent-%COMP%]{font-size:var(--font-size-2xl);font-weight:700;color:var(--text-primary);text-shadow:0 2px 4px rgba(0,0,0,.3)}"]})};function wn(i,r){i&1&&(o(0,"mat-error"),d(1," Email is required "),a())}function Pn(i,r){i&1&&(o(0,"mat-error"),d(1," Please enter a valid email address "),a())}function Mn(i,r){i&1&&h(0,"mat-spinner",20)}var ft=class i{constructor(r,e,t,n,s){this.authService=r;this.authState=e;this.formBuilder=t;this.router=n;this.snackBar=s;this.forgotPasswordForm=this.formBuilder.group({email:["",[_.required,_.email]]})}destroy$=new x;forgotPasswordForm;logoLoaded=!0;isLoading=!1;error=null;ngOnInit(){this.authState.authState$.pipe(C(this.destroy$)).subscribe(r=>{this.isLoading=r.isLoading,this.error=r.error,r.error&&this.showError(r.error)})}ngOnDestroy(){this.destroy$.next(),this.destroy$.complete()}onSendResetLink(){if(this.forgotPasswordForm.valid){let r=this.forgotPasswordForm.value;this.authService.forgotPassword(r).subscribe({next:e=>{e.isSuccess&&(this.showSuccess("Password reset instructions have been sent to your email address."),this.router.navigate(["/auth/verify-reset-token"],{queryParams:{email:r.email}}))},error:e=>{console.error("Forgot password failed:",e)}})}else this.markFormGroupTouched()}onBackToLogin(){this.router.navigate(["/auth/login"])}onImageError(r){this.logoLoaded=!1,console.log("Logo failed to load, showing fallback")}markFormGroupTouched(){Object.keys(this.forgotPasswordForm.controls).forEach(r=>{this.forgotPasswordForm.get(r)?.markAsTouched()})}showError(r){this.snackBar.open(r,"Close",{duration:5e3,panelClass:["error-snackbar"]})}showSuccess(r){this.snackBar.open(r,"Close",{duration:5e3,panelClass:["success-snackbar"]})}get emailControl(){return this.forgotPasswordForm.get("email")}static \u0275fac=function(e){return new(e||i)(g(z),g(O),g($),g(R),g(V))};static \u0275cmp=w({type:i,selectors:[["app-forget-password"]],standalone:!1,decls:33,vars:11,consts:[[1,"auth-container"],[1,"auth-card"],[1,"auth-header"],[1,"auth-logo"],["src","logo/logo2.png","alt","Chattrix Logo",3,"error"],[1,"logo-fallback"],[1,"logo-text"],[1,"auth-title"],[1,"auth-subtitle"],[1,"auth-form",3,"ngSubmit","formGroup"],["appearance","fill",1,"full-width"],["matInput","","type","email","formControlName","email","placeholder","Enter your email address","autocomplete","email"],["matSuffix",""],[4,"ngIf"],["mat-raised-button","","color","primary","type","submit",1,"reset-button","full-width",3,"disabled"],[1,"button-content"],["diameter","20","class","button-spinner",4,"ngIf"],[1,"auth-footer"],[1,"back-to-login"],["mat-button","","color","primary",1,"back-button",3,"click"],["diameter","20",1,"button-spinner"]],template:function(e,t){e&1&&(o(0,"div",0)(1,"mat-card",1)(2,"div",2)(3,"div",3)(4,"img",4),u("error",function(s){return t.onImageError(s)}),a(),o(5,"div",5)(6,"span",6),d(7,"C"),a()()(),o(8,"h1",7),d(9,"Forgot Password?"),a(),o(10,"p",8),d(11," Enter your email address and we'll send you a verification code to reset your password. "),a()(),o(12,"form",9),u("ngSubmit",function(){return t.onSendResetLink()}),o(13,"mat-form-field",10)(14,"mat-label"),d(15,"Email Address"),a(),h(16,"input",11),o(17,"mat-icon",12),d(18,"email"),a(),f(19,wn,2,0,"mat-error",13)(20,Pn,2,0,"mat-error",13),a(),o(21,"button",14)(22,"div",15),f(23,Mn,1,0,"mat-spinner",16),o(24,"span"),d(25,"Send Reset Code"),a()()()(),o(26,"div",17),h(27,"mat-divider"),o(28,"div",18)(29,"button",19),u("click",function(){return t.onBackToLogin()}),o(30,"mat-icon"),d(31,"arrow_back"),a(),d(32," Back to Login "),a()()()()()),e&2&&(l(4),P("display",t.logoLoaded?"block":"none"),l(),P("display",t.logoLoaded?"none":"flex"),l(7),c("formGroup",t.forgotPasswordForm),l(7),c("ngIf",t.emailControl==null?null:t.emailControl.hasError("required")),l(),c("ngIf",t.emailControl==null?null:t.emailControl.hasError("email")),l(),c("disabled",t.forgotPasswordForm.invalid||t.isLoading),l(2),c("ngIf",t.isLoading),l(),v("hidden",t.isLoading))},dependencies:[H,G,ne,oe,U,L,ae,W,X,N,pe,fe,se,D,Q,Y,Z],styles:[".full-width[_ngcontent-%COMP%]{width:100%;margin-bottom:var(--spacing-md)}.reset-button[_ngcontent-%COMP%]{height:44px;font-size:var(--font-size-base);font-weight:500;text-transform:none;border-radius:var(--radius-md);margin-bottom:var(--spacing-md);position:relative}.reset-button[_ngcontent-%COMP%]:disabled{opacity:.6}.reset-button[_ngcontent-%COMP%]   .button-content[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:var(--spacing-sm);position:relative}.reset-button[_ngcontent-%COMP%]   .hidden[_ngcontent-%COMP%]{visibility:hidden;opacity:0}.button-spinner[_ngcontent-%COMP%]{position:absolute;left:50%;top:50%;transform:translate(-50%,-50%)}.back-to-login[_ngcontent-%COMP%]{text-align:center;padding-top:var(--spacing-lg)}.back-button[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spacing-xs);color:var(--accent-green)!important;text-transform:none;font-size:var(--font-size-sm);margin:0 auto}.back-button[_ngcontent-%COMP%]:hover{color:var(--accent-green-hover)!important;background:transparent}.logo-fallback[_ngcontent-%COMP%]{width:100%;height:100%;display:flex;align-items:center;justify-content:center;background:linear-gradient(135deg,var(--accent-green) 0%,var(--accent-green-light) 100%);border-radius:50%}.logo-text[_ngcontent-%COMP%]{font-size:var(--font-size-2xl);font-weight:700;color:var(--text-primary);text-shadow:0 2px 4px rgba(0,0,0,.3)}  .mat-mdc-form-field .mat-mdc-text-field-wrapper{background-color:var(--bg-input);border-radius:var(--radius-md)}  .mat-mdc-form-field .mat-mdc-form-field-label{color:var(--text-secondary)}  .mat-mdc-form-field .mat-mdc-form-field-input{color:var(--text-primary)}  .mat-mdc-form-field .mat-mdc-form-field-icon-suffix{color:var(--text-secondary)}  .mat-mdc-form-field.mat-focused .mat-mdc-form-field-label{color:var(--accent-green)}  .error-snackbar{background-color:var(--error)!important;color:var(--text-primary)!important}  .success-snackbar{background-color:var(--success)!important;color:var(--text-primary)!important}@media (max-width: 480px){.auth-card[_ngcontent-%COMP%]{margin:var(--spacing-sm);max-width:calc(100vw - 2rem)}.auth-header[_ngcontent-%COMP%], .auth-form[_ngcontent-%COMP%]{padding:var(--spacing-lg) var(--spacing-md)}.auth-footer[_ngcontent-%COMP%]{padding:var(--spacing-md)}.auth-logo[_ngcontent-%COMP%]{width:50px;height:50px}.auth-title[_ngcontent-%COMP%]{font-size:var(--font-size-lg)}.auth-subtitle[_ngcontent-%COMP%]{font-size:var(--font-size-xs)}}.mat-mdc-button[_ngcontent-%COMP%]:focus{outline:2px solid var(--accent-green);outline-offset:2px}.mat-mdc-form-field[_ngcontent-%COMP%]:focus-within   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%]{border:2px solid var(--accent-green)}.button-spinner[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_spin 1s linear infinite}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}"]})};var Sn=["tokenInput"];function kn(i,r){if(i&1){let e=de();o(0,"input",25,0),u("input",function(n){let s=ee(e).index,m=y();return te(m.onTokenDigitInput(n,s))})("keydown",function(n){let s=ee(e).index,m=y();return te(m.onTokenDigitKeydown(n,s))}),a()}if(i&2){let e,t=r.$implicit,n=r.index,s=y();v("error",((e=s.tokenForm.get("digit"+(n+1)))==null?null:e.invalid)&&((e=s.tokenForm.get("digit"+(n+1)))==null?null:e.touched)),c("value",t)}}function On(i,r){i&1&&(o(0,"div",26)(1,"mat-icon",27),d(2,"error"),a(),o(3,"span"),d(4,"Please enter a valid 6-digit verification code"),a()())}function Fn(i,r){i&1&&h(0,"mat-spinner",28)}function En(i,r){if(i&1&&(o(0,"button",29)(1,"mat-icon"),d(2,"schedule"),a(),d(3),a()),i&2){let e=y();l(3),ue(" Resend in ",e.timerDisplay," ")}}function In(i,r){i&1&&h(0,"mat-spinner",33)}function Tn(i,r){if(i&1&&(o(0,"mat-icon"),d(1,"refresh"),a()),i&2){let e=y(2);v("hidden",e.isResending)}}function An(i,r){if(i&1){let e=de();o(0,"button",30),u("click",function(){ee(e);let n=y();return te(n.onResendCode())}),o(1,"div",16),f(2,In,1,0,"mat-spinner",31)(3,Tn,2,2,"mat-icon",32),o(4,"span"),d(5,"Resend Code"),a()()()}if(i&2){let e=y();c("disabled",e.isResending),l(2),c("ngIf",e.isResending),l(),c("ngIf",!e.isResending),l(),v("hidden",e.isResending)}}var ut=class i{constructor(r,e,t,n,s,m){this.authService=r;this.authState=e;this.formBuilder=t;this.router=n;this.route=s;this.snackBar=m;this.tokenForm=this.formBuilder.group({digit1:["",[_.required,_.pattern(/^\d$/)]],digit2:["",[_.required,_.pattern(/^\d$/)]],digit3:["",[_.required,_.pattern(/^\d$/)]],digit4:["",[_.required,_.pattern(/^\d$/)]],digit5:["",[_.required,_.pattern(/^\d$/)]],digit6:["",[_.required,_.pattern(/^\d$/)]]})}tokenInputs;destroy$=new x;timerSubscription;tokenForm;email="";logoLoaded=!0;tokenDigits=["","","","","",""];resendTimer=0;canResend=!1;isLoading=!1;isResending=!1;error=null;ngOnInit(){this.route.queryParams.pipe(C(this.destroy$)).subscribe(r=>{if(this.email=r.email,!this.email){this.router.navigate(["/auth/forgot-password"]);return}}),this.authState.authState$.pipe(C(this.destroy$)).subscribe(r=>{this.isLoading=r.isLoading,this.error=r.error,r.error&&this.showError(r.error)}),this.startResendTimer()}ngOnDestroy(){this.destroy$.next(),this.destroy$.complete(),this.timerSubscription?.unsubscribe()}onTokenDigitInput(r,e){let t=r.target.value;if(t&&/^\d$/.test(t)){if(this.tokenDigits[e]=t,this.tokenForm.get(`digit${e+1}`)?.setValue(t),e<5){let n=this.tokenInputs.toArray()[e+1];n&&n.nativeElement.focus()}this.tokenDigits.every(n=>n!=="")&&this.onVerifyToken()}else t===""&&(this.tokenDigits[e]="",this.tokenForm.get(`digit${e+1}`)?.setValue(""))}onTokenDigitKeydown(r,e){if(r.key==="Backspace"&&this.tokenDigits[e]===""&&e>0){let t=this.tokenInputs.toArray()[e-1];t&&t.nativeElement.focus()}r.key==="v"&&(r.ctrlKey||r.metaKey)&&(r.preventDefault(),navigator.clipboard.readText().then(t=>{this.handlePaste(t,e)}))}onVerifyToken(){let r=this.tokenDigits.join("");if(r.length===6&&/^\d{6}$/.test(r)){let e={email:this.email,resetToken:r};this.authService.verifyResetToken(e).subscribe({next:t=>{t.isSuccess&&(this.showSuccess("Reset token verified successfully!"),this.router.navigate(["/auth/reset-password"],{queryParams:{email:this.email,token:r}}))},error:t=>{console.error("Token verification failed:",t),this.clearToken()}})}else this.showError("Please enter a valid 6-digit verification code"),this.markFormGroupTouched()}onResendCode(){if(!this.canResend)return;this.isResending=!0;let r={email:this.email};this.authService.forgotPassword(r).subscribe({next:e=>{e.isSuccess&&(this.showSuccess("Verification code sent successfully!"),this.clearToken(),this.startResendTimer()),this.isResending=!1},error:e=>{console.error("Resend code failed:",e),this.isResending=!1}})}onBackToLogin(){this.router.navigate(["/auth/login"])}onImageError(r){this.logoLoaded=!1,console.log("Logo failed to load, showing fallback")}handlePaste(r,e){let t=r.replace(/\D/g,"").slice(0,6-e);for(let b=0;b<t.length&&e+b<6;b++){let I=e+b;this.tokenDigits[I]=t[b],this.tokenForm.get(`digit${I+1}`)?.setValue(t[b]);let le=this.tokenInputs.toArray()[I];le&&(le.nativeElement.value=t[b])}let n=this.tokenDigits.findIndex(b=>b===""),s=n!==-1?n:5,m=this.tokenInputs.toArray()[s];m&&m.nativeElement.focus()}clearToken(){this.tokenDigits=["","","","","",""],this.tokenForm.reset(),setTimeout(()=>{this.tokenInputs.toArray().forEach((e,t)=>{e.nativeElement.value=""});let r=this.tokenInputs.toArray()[0];r&&r.nativeElement.focus()})}startResendTimer(){this.resendTimer=60,this.canResend=!1,this.timerSubscription?.unsubscribe(),this.timerSubscription=Ye(1e3).pipe(C(this.destroy$)).subscribe(()=>{this.resendTimer--,this.resendTimer<=0&&(this.canResend=!0,this.timerSubscription?.unsubscribe())})}markFormGroupTouched(){Object.keys(this.tokenForm.controls).forEach(r=>{this.tokenForm.get(r)?.markAsTouched()})}showError(r){this.snackBar.open(r,"Close",{duration:5e3,panelClass:["error-snackbar"]})}showSuccess(r){this.snackBar.open(r,"Close",{duration:3e3,panelClass:["success-snackbar"]})}get timerDisplay(){let r=Math.floor(this.resendTimer/60),e=this.resendTimer%60;return`${r}:${e.toString().padStart(2,"0")}`}static \u0275fac=function(e){return new(e||i)(g(z),g(O),g($),g(R),g(Fe),g(V))};static \u0275cmp=w({type:i,selectors:[["app-verify-reset-token"]],viewQuery:function(e,t){if(e&1&&E(Sn,5),e&2){let n;M(n=S())&&(t.tokenInputs=n)}},standalone:!1,decls:37,vars:14,consts:[["tokenInput",""],[1,"auth-container"],[1,"auth-card","token-card"],[1,"auth-header"],[1,"auth-logo"],["src","logo/logo2.png","alt","Chattrix Logo",3,"error"],[1,"logo-fallback"],[1,"logo-text"],[1,"auth-title"],[1,"auth-subtitle"],[1,"auth-form",3,"ngSubmit","formGroup"],[1,"token-container"],[1,"token-inputs"],["type","text","maxlength","1","class","token-input","autocomplete","one-time-code","inputmode","numeric","pattern","[0-9]*",3,"value","error","input","keydown",4,"ngFor","ngForOf"],["class","token-error",4,"ngIf"],["mat-raised-button","","color","primary","type","submit",1,"verify-button","full-width",3,"disabled"],[1,"button-content"],["diameter","20","class","button-spinner",4,"ngIf"],[1,"resend-section"],[1,"resend-text"],["mat-button","","type","button","class","resend-button disabled","disabled","",4,"ngIf"],["mat-button","","color","primary","type","button","class","resend-button",3,"disabled","click",4,"ngIf"],[1,"auth-footer"],[1,"back-to-login"],["mat-button","","color","primary",1,"back-button",3,"click"],["type","text","maxlength","1","autocomplete","one-time-code","inputmode","numeric","pattern","[0-9]*",1,"token-input",3,"input","keydown","value"],[1,"token-error"],[1,"error-icon"],["diameter","20",1,"button-spinner"],["mat-button","","type","button","disabled","",1,"resend-button","disabled"],["mat-button","","color","primary","type","button",1,"resend-button",3,"click","disabled"],["diameter","16","class","resend-spinner",4,"ngIf"],[3,"hidden",4,"ngIf"],["diameter","16",1,"resend-spinner"]],template:function(e,t){e&1&&(o(0,"div",1)(1,"mat-card",2)(2,"div",3)(3,"div",4)(4,"img",5),u("error",function(s){return t.onImageError(s)}),a(),o(5,"div",6)(6,"span",7),d(7,"C"),a()()(),o(8,"h1",8),d(9,"Verify Reset Code"),a(),o(10,"p",9),d(11," We've sent a 6-digit verification code to "),o(12,"strong"),d(13),a(),d(14,". Please enter it below to verify your identity. "),a()(),o(15,"form",10),u("ngSubmit",function(){return t.onVerifyToken()}),o(16,"div",11)(17,"div",12),f(18,kn,2,3,"input",13),a(),f(19,On,5,0,"div",14),a(),o(20,"button",15)(21,"div",16),f(22,Fn,1,0,"mat-spinner",17),o(23,"span"),d(24,"Verify Code"),a()()(),o(25,"div",18)(26,"p",19),d(27,"Didn't receive the code?"),a(),f(28,En,4,1,"button",20)(29,An,6,5,"button",21),a()(),o(30,"div",22),h(31,"mat-divider"),o(32,"div",23)(33,"button",24),u("click",function(){return t.onBackToLogin()}),o(34,"mat-icon"),d(35,"arrow_back"),a(),d(36," Back to Login "),a()()()()()),e&2&&(l(4),P("display",t.logoLoaded?"block":"none"),l(),P("display",t.logoLoaded?"none":"flex"),l(8),re(t.email),l(2),c("formGroup",t.tokenForm),l(3),c("ngForOf",t.tokenDigits),l(),c("ngIf",t.tokenForm.invalid&&t.tokenForm.touched),l(),c("disabled",t.tokenForm.invalid||t.isLoading),l(2),c("ngIf",t.isLoading),l(),v("hidden",t.isLoading),l(5),c("ngIf",!t.canResend),l(),c("ngIf",t.canResend))},dependencies:[oi,H,G,U,L,W,D,Q,Y,Z],styles:[".token-card[_ngcontent-%COMP%]{max-width:420px}.full-width[_ngcontent-%COMP%]{width:100%}.token-container[_ngcontent-%COMP%]{margin-bottom:var(--spacing-xl)}.token-inputs[_ngcontent-%COMP%]{display:flex;justify-content:space-between;gap:var(--spacing-sm);margin-bottom:var(--spacing-md)}.token-input[_ngcontent-%COMP%]{width:50px;height:60px;border:2px solid var(--border-primary);border-radius:var(--radius-md);background-color:var(--bg-input);color:var(--text-primary);font-size:var(--font-size-xl);font-weight:600;text-align:center;outline:none;transition:all var(--transition-normal)}.token-input[_ngcontent-%COMP%]:focus{border-color:var(--accent-green);box-shadow:0 0 0 3px #10b9811a;background-color:var(--bg-secondary)}.token-input[_ngcontent-%COMP%]:hover{border-color:var(--border-secondary)}.token-input.error[_ngcontent-%COMP%]{border-color:var(--error);box-shadow:0 0 0 3px #ef44441a}.token-input[_ngcontent-%COMP%]:disabled{opacity:.6;cursor:not-allowed}.token-error[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spacing-xs);color:var(--error);font-size:var(--font-size-sm);margin-top:var(--spacing-sm)}.token-error[_ngcontent-%COMP%]   .error-icon[_ngcontent-%COMP%]{font-size:16px;width:16px;height:16px}.verify-button[_ngcontent-%COMP%]{height:44px;font-size:var(--font-size-base);font-weight:500;text-transform:none;border-radius:var(--radius-md);margin-bottom:var(--spacing-xl);position:relative}.verify-button[_ngcontent-%COMP%]:disabled{opacity:.6}.verify-button[_ngcontent-%COMP%]   .button-content[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:var(--spacing-sm);position:relative}.verify-button[_ngcontent-%COMP%]   .hidden[_ngcontent-%COMP%]{visibility:hidden;opacity:0}.button-spinner[_ngcontent-%COMP%]{position:absolute;transform:translate(-50%,-50%)}.resend-section[_ngcontent-%COMP%]{text-align:center;margin-bottom:var(--spacing-lg)}.resend-text[_ngcontent-%COMP%]{color:var(--text-secondary);font-size:var(--font-size-sm);margin-bottom:var(--spacing-md)}.resend-button[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spacing-xs);font-size:var(--font-size-sm);text-transform:none;margin:0 auto;position:relative}.resend-button[_ngcontent-%COMP%]:not(.disabled){color:var(--accent-green)!important}.resend-button[_ngcontent-%COMP%]:not(.disabled):hover{color:var(--accent-green-hover)!important;background:transparent}.resend-button.disabled[_ngcontent-%COMP%]{color:var(--text-disabled)!important;cursor:not-allowed}.resend-button[_ngcontent-%COMP%]   .button-content[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:var(--spacing-xs);position:relative}.resend-button[_ngcontent-%COMP%]   .hidden[_ngcontent-%COMP%]{visibility:hidden;opacity:0}.resend-spinner[_ngcontent-%COMP%]{position:absolute;left:50%;top:50%;transform:translate(-50%,-50%)}.back-to-login[_ngcontent-%COMP%]{text-align:center;padding-top:var(--spacing-lg)}.back-button[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spacing-xs);color:var(--accent-green)!important;text-transform:none;font-size:var(--font-size-sm);margin:0 auto}.back-button[_ngcontent-%COMP%]:hover{color:var(--accent-green-hover)!important;background:transparent}.logo-fallback[_ngcontent-%COMP%]{width:100%;height:100%;display:flex;align-items:center;justify-content:center;background:linear-gradient(135deg,var(--accent-green) 0%,var(--accent-green-light) 100%);border-radius:50%}.logo-text[_ngcontent-%COMP%]{font-size:var(--font-size-2xl);font-weight:700;color:var(--text-primary);text-shadow:0 2px 4px rgba(0,0,0,.3)}  .error-snackbar{background-color:var(--error)!important;color:var(--text-primary)!important}  .success-snackbar{background-color:var(--success)!important;color:var(--text-primary)!important}@media (max-width: 480px){.token-card[_ngcontent-%COMP%]{margin:var(--spacing-sm);max-width:calc(100vw - 2rem)}.auth-header[_ngcontent-%COMP%], .auth-form[_ngcontent-%COMP%]{padding:var(--spacing-lg) var(--spacing-md)}.auth-footer[_ngcontent-%COMP%]{padding:var(--spacing-md)}.auth-logo[_ngcontent-%COMP%]{width:50px;height:50px}.auth-title[_ngcontent-%COMP%]{font-size:var(--font-size-lg)}.auth-subtitle[_ngcontent-%COMP%]{font-size:var(--font-size-xs)}.token-input[_ngcontent-%COMP%]{width:45px;height:55px;font-size:var(--font-size-lg)}.token-inputs[_ngcontent-%COMP%]{gap:var(--spacing-xs)}}@media (max-width: 360px){.token-input[_ngcontent-%COMP%]{width:40px;height:50px;font-size:var(--font-size-base)}}.mat-mdc-button[_ngcontent-%COMP%]:focus{outline:2px solid var(--accent-green);outline-offset:2px}.button-spinner[_ngcontent-%COMP%], .resend-spinner[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_spin 1s linear infinite}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.token-input[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeIn .3s ease-in-out}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0;transform:translateY(-10px)}to{opacity:1;transform:translateY(0)}}.token-input.success[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_successPulse .6s ease-in-out;border-color:var(--success);background-color:#10b9811a}@keyframes _ngcontent-%COMP%_successPulse{0%{transform:scale(1)}50%{transform:scale(1.05)}to{transform:scale(1)}}.resend-button[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse 2s infinite}@keyframes _ngcontent-%COMP%_pulse{0%{opacity:1}50%{opacity:.5}to{opacity:1}}"]})};function Rn(i,r){i&1&&(o(0,"mat-error"),d(1," Password is required "),a())}function Ln(i,r){i&1&&(o(0,"mat-error"),d(1," Password must be at least 8 characters long "),a())}function Dn(i,r){i&1&&(o(0,"mat-error"),d(1," Password must contain uppercase, lowercase, number, and special character "),a())}function zn(i,r){i&1&&(o(0,"mat-error"),d(1," Please confirm your password "),a())}function Bn(i,r){i&1&&(o(0,"mat-error"),d(1," Passwords do not match "),a())}function Nn(i,r){if(i&1&&(o(0,"div",22)(1,"h4"),d(2,"Password must contain:"),a(),o(3,"div",23)(4,"div",24),d(5," \u2713 "),a(),o(6,"span",25),d(7," At least one uppercase letter "),a()(),o(8,"div",23)(9,"div",24),d(10," \u2713 "),a(),o(11,"span",25),d(12," At least one lowercase letter "),a()(),o(13,"div",23)(14,"div",24),d(15," \u2713 "),a(),o(16,"span",25),d(17," At least one number "),a()(),o(18,"div",23)(19,"div",24),d(20," \u2713 "),a(),o(21,"span",25),d(22," At least one special character "),a()()()),i&2){let e=y();l(4),v("met",e.newPasswordControl==null||e.newPasswordControl.errors==null||e.newPasswordControl.errors.passwordStrength==null?null:e.newPasswordControl.errors.passwordStrength.hasUpperCase)("unmet",!(!(e.newPasswordControl==null||e.newPasswordControl.errors==null||e.newPasswordControl.errors.passwordStrength==null)&&e.newPasswordControl.errors.passwordStrength.hasUpperCase)),l(2),v("met",e.newPasswordControl==null||e.newPasswordControl.errors==null||e.newPasswordControl.errors.passwordStrength==null?null:e.newPasswordControl.errors.passwordStrength.hasUpperCase)("unmet",!(!(e.newPasswordControl==null||e.newPasswordControl.errors==null||e.newPasswordControl.errors.passwordStrength==null)&&e.newPasswordControl.errors.passwordStrength.hasUpperCase)),l(3),v("met",e.newPasswordControl==null||e.newPasswordControl.errors==null||e.newPasswordControl.errors.passwordStrength==null?null:e.newPasswordControl.errors.passwordStrength.hasLowerCase)("unmet",!(!(e.newPasswordControl==null||e.newPasswordControl.errors==null||e.newPasswordControl.errors.passwordStrength==null)&&e.newPasswordControl.errors.passwordStrength.hasLowerCase)),l(2),v("met",e.newPasswordControl==null||e.newPasswordControl.errors==null||e.newPasswordControl.errors.passwordStrength==null?null:e.newPasswordControl.errors.passwordStrength.hasLowerCase)("unmet",!(!(e.newPasswordControl==null||e.newPasswordControl.errors==null||e.newPasswordControl.errors.passwordStrength==null)&&e.newPasswordControl.errors.passwordStrength.hasLowerCase)),l(3),v("met",e.newPasswordControl==null||e.newPasswordControl.errors==null||e.newPasswordControl.errors.passwordStrength==null?null:e.newPasswordControl.errors.passwordStrength.hasNumeric)("unmet",!(!(e.newPasswordControl==null||e.newPasswordControl.errors==null||e.newPasswordControl.errors.passwordStrength==null)&&e.newPasswordControl.errors.passwordStrength.hasNumeric)),l(2),v("met",e.newPasswordControl==null||e.newPasswordControl.errors==null||e.newPasswordControl.errors.passwordStrength==null?null:e.newPasswordControl.errors.passwordStrength.hasNumeric)("unmet",!(!(e.newPasswordControl==null||e.newPasswordControl.errors==null||e.newPasswordControl.errors.passwordStrength==null)&&e.newPasswordControl.errors.passwordStrength.hasNumeric)),l(3),v("met",e.newPasswordControl==null||e.newPasswordControl.errors==null||e.newPasswordControl.errors.passwordStrength==null?null:e.newPasswordControl.errors.passwordStrength.hasSpecialChar)("unmet",!(!(e.newPasswordControl==null||e.newPasswordControl.errors==null||e.newPasswordControl.errors.passwordStrength==null)&&e.newPasswordControl.errors.passwordStrength.hasSpecialChar)),l(2),v("met",e.newPasswordControl==null||e.newPasswordControl.errors==null||e.newPasswordControl.errors.passwordStrength==null?null:e.newPasswordControl.errors.passwordStrength.hasSpecialChar)("unmet",!(!(e.newPasswordControl==null||e.newPasswordControl.errors==null||e.newPasswordControl.errors.passwordStrength==null)&&e.newPasswordControl.errors.passwordStrength.hasSpecialChar))}}function Vn(i,r){i&1&&h(0,"mat-spinner",26)}var ht=class i{constructor(r,e,t,n,s,m){this.authService=r;this.authState=e;this.formBuilder=t;this.router=n;this.route=s;this.snackBar=m;this.resetPasswordForm=this.formBuilder.group({newPassword:["",[_.required,_.minLength(8),this.passwordValidator]],confirmPassword:["",[_.required]]},{validators:this.passwordMatchValidator})}destroy$=new x;resetPasswordForm;hidePassword=!0;hideConfirmPassword=!0;logoLoaded=!0;email="";resetToken="";isLoading=!1;error=null;ngOnInit(){this.route.queryParams.pipe(C(this.destroy$)).subscribe(r=>{if(this.email=r.email,this.resetToken=r.token,!this.email||!this.resetToken){this.router.navigate(["/auth/forgot-password"]);return}}),this.authState.authState$.pipe(C(this.destroy$)).subscribe(r=>{this.isLoading=r.isLoading,this.error=r.error,r.error&&this.showError(r.error)})}ngOnDestroy(){this.destroy$.next(),this.destroy$.complete()}onResetPassword(){if(this.resetPasswordForm.valid){let r={email:this.email,resetToken:this.resetToken,newPassword:this.resetPasswordForm.value.newPassword,confirmPassword:this.resetPasswordForm.value.confirmPassword};this.authService.resetPassword(r).subscribe({next:e=>{e.isSuccess&&(this.showSuccess("Password reset successfully! You can now login with your new password."),this.router.navigate(["/auth/login"]))},error:e=>{console.error("Password reset failed:",e)}})}else this.markFormGroupTouched()}onBackToLogin(){this.router.navigate(["/auth/login"])}togglePasswordVisibility(){this.hidePassword=!this.hidePassword}toggleConfirmPasswordVisibility(){this.hideConfirmPassword=!this.hideConfirmPassword}onImageError(r){this.logoLoaded=!1,console.log("Logo failed to load, showing fallback")}passwordValidator(r){let e=r.value;if(!e)return null;let t=/[A-Z]/.test(e),n=/[a-z]/.test(e),s=/[0-9]/.test(e),m=/[!@#$%^&*(),.?":{}|<>]/.test(e);return t&&n&&s&&m?null:{passwordStrength:{hasUpperCase:t,hasLowerCase:n,hasNumeric:s,hasSpecialChar:m}}}passwordMatchValidator(r){let e=r.get("newPassword"),t=r.get("confirmPassword");return!e||!t||e.value===t.value?null:{passwordMismatch:!0}}markFormGroupTouched(){Object.keys(this.resetPasswordForm.controls).forEach(r=>{this.resetPasswordForm.get(r)?.markAsTouched()})}showError(r){this.snackBar.open(r,"Close",{duration:5e3,panelClass:["error-snackbar"]})}showSuccess(r){this.snackBar.open(r,"Close",{duration:5e3,panelClass:["success-snackbar"]})}get newPasswordControl(){return this.resetPasswordForm.get("newPassword")}get confirmPasswordControl(){return this.resetPasswordForm.get("confirmPassword")}static \u0275fac=function(e){return new(e||i)(g(z),g(O),g($),g(R),g(Fe),g(V))};static \u0275cmp=w({type:i,selectors:[["app-reset-password"]],standalone:!1,decls:45,vars:23,consts:[[1,"auth-container"],[1,"auth-card","reset-card"],[1,"auth-header"],[1,"auth-logo"],["src","logo/logo2.png","alt","Chattrix Logo",3,"error"],[1,"logo-fallback"],[1,"logo-text"],[1,"auth-title"],[1,"auth-subtitle"],[1,"auth-form",3,"ngSubmit","formGroup"],["appearance","fill",1,"full-width"],["matInput","","formControlName","newPassword","placeholder","Create a strong password","autocomplete","new-password",3,"type"],["mat-icon-button","","matSuffix","","type","button",3,"click"],[4,"ngIf"],["matInput","","formControlName","confirmPassword","placeholder","Confirm your new password","autocomplete","new-password",3,"type"],["class","password-strength",4,"ngIf"],["mat-raised-button","","color","primary","type","submit",1,"reset-button","full-width",3,"disabled"],[1,"button-content"],["diameter","20","class","button-spinner",4,"ngIf"],[1,"auth-footer"],[1,"back-to-login"],["mat-button","","color","primary",1,"back-button",3,"click"],[1,"password-strength"],[1,"strength-requirement"],[1,"requirement-icon"],[1,"requirement-text"],["diameter","20",1,"button-spinner"]],template:function(e,t){e&1&&(o(0,"div",0)(1,"mat-card",1)(2,"div",2)(3,"div",3)(4,"img",4),u("error",function(s){return t.onImageError(s)}),a(),o(5,"div",5)(6,"span",6),d(7,"C"),a()()(),o(8,"h1",7),d(9,"Reset Password"),a(),o(10,"p",8),d(11," Create a new secure password for your account. "),a()(),o(12,"form",9),u("ngSubmit",function(){return t.onResetPassword()}),o(13,"mat-form-field",10)(14,"mat-label"),d(15,"New Password"),a(),h(16,"input",11),o(17,"button",12),u("click",function(){return t.togglePasswordVisibility()}),o(18,"mat-icon"),d(19),a()(),f(20,Rn,2,0,"mat-error",13)(21,Ln,2,0,"mat-error",13)(22,Dn,2,0,"mat-error",13),a(),o(23,"mat-form-field",10)(24,"mat-label"),d(25,"Confirm New Password"),a(),h(26,"input",14),o(27,"button",12),u("click",function(){return t.toggleConfirmPasswordVisibility()}),o(28,"mat-icon"),d(29),a()(),f(30,zn,2,0,"mat-error",13)(31,Bn,2,0,"mat-error",13),a(),f(32,Nn,23,32,"div",15),o(33,"button",16)(34,"div",17),f(35,Vn,1,0,"mat-spinner",18),o(36,"span"),d(37,"Reset Password"),a()()()(),o(38,"div",19),h(39,"mat-divider"),o(40,"div",20)(41,"button",21),u("click",function(){return t.onBackToLogin()}),o(42,"mat-icon"),d(43,"arrow_back"),a(),d(44," Back to Login "),a()()()()()),e&2&&(l(4),P("display",t.logoLoaded?"block":"none"),l(),P("display",t.logoLoaded?"none":"flex"),l(7),c("formGroup",t.resetPasswordForm),l(4),c("type",t.hidePassword?"password":"text"),l(),k("aria-label","Hide password")("aria-pressed",t.hidePassword),l(2),re(t.hidePassword?"visibility_off":"visibility"),l(),c("ngIf",t.newPasswordControl==null?null:t.newPasswordControl.hasError("required")),l(),c("ngIf",t.newPasswordControl==null?null:t.newPasswordControl.hasError("minlength")),l(),c("ngIf",t.newPasswordControl==null?null:t.newPasswordControl.hasError("passwordStrength")),l(4),c("type",t.hideConfirmPassword?"password":"text"),l(),k("aria-label","Hide password")("aria-pressed",t.hideConfirmPassword),l(2),re(t.hideConfirmPassword?"visibility_off":"visibility"),l(),c("ngIf",t.confirmPasswordControl==null?null:t.confirmPasswordControl.hasError("required")),l(),c("ngIf",t.resetPasswordForm.hasError("passwordMismatch")&&(t.confirmPasswordControl==null?null:t.confirmPasswordControl.touched)),l(),c("ngIf",(t.newPasswordControl==null?null:t.newPasswordControl.hasError("passwordStrength"))&&(t.newPasswordControl==null?null:t.newPasswordControl.touched)),l(),c("disabled",t.resetPasswordForm.invalid||t.isLoading),l(2),c("ngIf",t.isLoading),l(),v("hidden",t.isLoading))},dependencies:[H,G,ne,oe,U,L,ae,W,X,N,pe,fe,se,D,ve,Q,Y,Z],styles:[".reset-card[_ngcontent-%COMP%]{max-width:450px}.full-width[_ngcontent-%COMP%]{width:100%;margin-bottom:var(--spacing-md)}.reset-button[_ngcontent-%COMP%]{height:44px;font-size:var(--font-size-base);font-weight:500;text-transform:none;border-radius:var(--radius-md);margin-bottom:var(--spacing-md);position:relative}.reset-button[_ngcontent-%COMP%]:disabled{opacity:.6}.reset-button[_ngcontent-%COMP%]   .button-content[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:var(--spacing-sm);position:relative}.reset-button[_ngcontent-%COMP%]   .hidden[_ngcontent-%COMP%]{visibility:hidden;opacity:0}.button-spinner[_ngcontent-%COMP%]{position:absolute;transform:translate(-50%,-50%)}.password-strength[_ngcontent-%COMP%]{margin-top:var(--spacing-sm);margin-bottom:var(--spacing-lg);padding:var(--spacing-md);background:var(--bg-tertiary);border-radius:var(--radius-md);border:1px solid var(--border-primary)}.password-strength[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:var(--font-size-sm);color:var(--text-secondary);margin-bottom:var(--spacing-sm);font-weight:500}.strength-requirement[_ngcontent-%COMP%]{display:flex;align-items:center;margin-bottom:var(--spacing-xs)}.strength-requirement[_ngcontent-%COMP%]:last-child{margin-bottom:0}.strength-requirement[_ngcontent-%COMP%]   .requirement-icon[_ngcontent-%COMP%]{width:18px;height:18px;margin-right:var(--spacing-sm);border-radius:50%;display:flex;align-items:center;justify-content:center;font-size:12px;font-weight:700;transition:all var(--transition-normal)}.strength-requirement[_ngcontent-%COMP%]   .requirement-icon.met[_ngcontent-%COMP%]{background-color:var(--success);color:#fff;transform:scale(1)}.strength-requirement[_ngcontent-%COMP%]   .requirement-icon.unmet[_ngcontent-%COMP%]{background-color:var(--text-disabled);color:#fff;transform:scale(.9);opacity:.7}.strength-requirement[_ngcontent-%COMP%]   .requirement-text[_ngcontent-%COMP%]{font-size:var(--font-size-xs);transition:color var(--transition-normal)}.strength-requirement[_ngcontent-%COMP%]   .requirement-text.met[_ngcontent-%COMP%]{color:var(--success);font-weight:500}.strength-requirement[_ngcontent-%COMP%]   .requirement-text.unmet[_ngcontent-%COMP%]{color:var(--text-disabled)}.back-to-login[_ngcontent-%COMP%]{text-align:center;padding-top:var(--spacing-lg)}.back-button[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spacing-xs);color:var(--accent-green)!important;text-transform:none;font-size:var(--font-size-sm);margin:0 auto}.back-button[_ngcontent-%COMP%]:hover{color:var(--accent-green-hover)!important;background:transparent}.logo-fallback[_ngcontent-%COMP%]{width:100%;height:100%;display:flex;align-items:center;justify-content:center;background:linear-gradient(135deg,var(--accent-green) 0%,var(--accent-green-light) 100%);border-radius:50%}.logo-text[_ngcontent-%COMP%]{font-size:var(--font-size-2xl);font-weight:700;color:var(--text-primary);text-shadow:0 2px 4px rgba(0,0,0,.3)}  .reset-card .mat-mdc-form-field .mat-mdc-text-field-wrapper{background-color:var(--bg-input);border-radius:var(--radius-md)}  .reset-card .mat-mdc-form-field .mat-mdc-form-field-label{color:var(--text-secondary)}  .reset-card .mat-mdc-form-field .mat-mdc-form-field-input{color:var(--text-primary)}  .reset-card .mat-mdc-form-field .mat-mdc-form-field-icon-suffix{color:var(--text-secondary)}  .reset-card .mat-mdc-form-field.mat-focused .mat-mdc-form-field-label{color:var(--accent-green)}  .reset-card .mat-mdc-form-field .mat-mdc-form-field-wrapper{padding-bottom:0}  .reset-card .mat-mdc-form-field .mat-mdc-form-field-subscript-wrapper{margin-top:4px;min-height:16px;font-size:var(--font-size-xs)}  .error-snackbar{background-color:var(--error)!important;color:var(--text-primary)!important}  .success-snackbar{background-color:var(--success)!important;color:var(--text-primary)!important}@media (max-width: 768px){.reset-card[_ngcontent-%COMP%]   .auth-header[_ngcontent-%COMP%]{padding:var(--spacing-sm) var(--spacing-md) var(--spacing-xs)}.reset-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%], .reset-card[_ngcontent-%COMP%]   .auth-footer[_ngcontent-%COMP%]{padding:var(--spacing-sm) var(--spacing-md)}.reset-card[_ngcontent-%COMP%]   .full-width[_ngcontent-%COMP%]{margin-bottom:var(--spacing-sm)}.reset-card[_ngcontent-%COMP%]   .password-strength[_ngcontent-%COMP%]{margin-bottom:var(--spacing-md);padding:var(--spacing-sm)}}@media (max-width: 480px){.reset-card[_ngcontent-%COMP%]{margin:var(--spacing-xs);max-width:calc(100vw - 1rem)}.reset-card[_ngcontent-%COMP%]   .auth-header[_ngcontent-%COMP%]{padding:var(--spacing-xs) var(--spacing-sm) var(--spacing-xs)}.reset-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%], .reset-card[_ngcontent-%COMP%]   .auth-footer[_ngcontent-%COMP%]{padding:var(--spacing-xs) var(--spacing-sm)}.reset-card[_ngcontent-%COMP%]   .auth-logo[_ngcontent-%COMP%]{width:45px;height:45px;margin-bottom:var(--spacing-xs)}.reset-card[_ngcontent-%COMP%]   .auth-title[_ngcontent-%COMP%]{font-size:var(--font-size-lg);margin-bottom:4px}.reset-card[_ngcontent-%COMP%]   .auth-subtitle[_ngcontent-%COMP%]{font-size:var(--font-size-xs);margin-bottom:0}.reset-card[_ngcontent-%COMP%]   .full-width[_ngcontent-%COMP%]{margin-bottom:8px}.reset-card[_ngcontent-%COMP%]   .reset-button[_ngcontent-%COMP%]{height:40px;margin-bottom:8px}.reset-card[_ngcontent-%COMP%]   .password-strength[_ngcontent-%COMP%]{margin-top:8px;margin-bottom:var(--spacing-sm);padding:var(--spacing-xs)}.reset-card[_ngcontent-%COMP%]   .password-strength[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:11px;margin-bottom:6px}.reset-card[_ngcontent-%COMP%]   .strength-requirement[_ngcontent-%COMP%]{margin-bottom:4px}.reset-card[_ngcontent-%COMP%]   .strength-requirement[_ngcontent-%COMP%]   .requirement-icon[_ngcontent-%COMP%]{width:14px;height:14px;font-size:10px;margin-right:6px}.reset-card[_ngcontent-%COMP%]   .strength-requirement[_ngcontent-%COMP%]   .requirement-text[_ngcontent-%COMP%]{font-size:10px}}.mat-mdc-button[_ngcontent-%COMP%]:focus{outline:2px solid var(--accent-green);outline-offset:2px}.mat-mdc-form-field[_ngcontent-%COMP%]:focus-within   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%]{border:2px solid var(--accent-green)}.button-spinner[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_spin 1s linear infinite}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.mat-mdc-form-field.ng-invalid.ng-touched[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%]{border:1px solid var(--error)}.mat-mdc-form-field.ng-valid.ng-touched[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%]{border:1px solid var(--success)}.strength-requirement[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInUp .3s ease-in-out}@keyframes _ngcontent-%COMP%_fadeInUp{0%{opacity:0;transform:translateY(10px)}to{opacity:1;transform:translateY(0)}}.requirement-icon.met[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_checkmark .5s ease-in-out}@keyframes _ngcontent-%COMP%_checkmark{0%{transform:scale(.8);opacity:.5}50%{transform:scale(1.1)}to{transform:scale(1);opacity:1}}"]})};var gt=class i{constructor(r,e,t){this.httpClient=r;this.errorHandler=e;this.authState=t}API_URL=`${Oi.apiUrl}/api/Account`;registerWithProfile(r){this.authState.setLoading(!0),this.authState.clearError();let e=new si;return this.httpClient.post(`${this.API_URL}/Register`,r,{headers:e}).pipe(Vt(t=>{let n=this.errorHandler.handleError(t);return this.authState.setError(n.message),this.errorHandler.createErrorObservable(t)}),jt(()=>{this.authState.setLoading(!1)}))}validateFile(r){return["image/jpeg","image/jpg","image/png"].includes(r.type)?r.size>5242880?{isValid:!1,error:"File size must be less than 5MB."}:{isValid:!0}:{isValid:!1,error:"Please select a valid image file (JPEG, JPG, or PNG)."}}createImagePreview(r){return new Promise((e,t)=>{let n=new FileReader;n.onload=s=>{e(s.target?.result)},n.onerror=()=>{t(new Error("Failed to read file"))},n.readAsDataURL(r)})}prepareRegistrationFormData(r,e,t){let n=new FormData;return n.append("fullName",r.fullName),n.append("email",r.email),n.append("password",r.password),r.phoneNumber&&n.append("phoneNumber",r.phoneNumber),e&&n.append("description",e),t&&n.append("profileImage",t),n}static \u0275fac=function(e){return new(e||i)(he(li),he(Fi),he(O))};static \u0275prov=J({token:i,factory:i.\u0275fac,providedIn:"root"})};function qn(i,r){if(i&1&&h(0,"img",29),i&2){let e=y();c("src",e.imagePreview,Qt)}}function Hn(i,r){i&1&&(o(0,"div",30)(1,"mat-icon",31),d(2,"person"),a(),o(3,"span",32),d(4,"Add Photo"),a()())}function Un(i,r){if(i&1){let e=de();o(0,"button",33),u("click",function(){ee(e);let n=y();return te(n.removeImage())}),o(1,"mat-icon"),d(2,"close"),a()()}}function Gn(i,r){i&1&&h(0,"mat-spinner",34)}var _t=class i{constructor(r,e,t,n,s,m){this.authService=r;this.authState=e;this.fileUploadService=t;this.formBuilder=n;this.router=s;this.snackBar=m;this.profileForm=this.formBuilder.group({description:["",[_.maxLength(500)]]})}destroy$=new x;profileForm;logoLoaded=!0;selectedFile=null;imagePreview=null;isLoading=!1;maxFileSize=5*1024*1024;allowedTypes=["image/jpeg","image/jpg","image/png"];ngOnInit(){if(!sessionStorage.getItem("pendingSignupData")){this.router.navigate(["/auth/signup"]);return}this.authState.authState$.pipe(C(this.destroy$)).subscribe(e=>{this.isLoading=e.isLoading})}ngOnDestroy(){this.destroy$.next(),this.destroy$.complete()}onFileSelected(r){let e=r.target.files[0];if(e){let t=this.fileUploadService.validateFile(e);if(!t.isValid){this.showError(t.error);return}this.selectedFile=e,this.fileUploadService.createImagePreview(e).then(n=>{this.imagePreview=n}).catch(n=>{console.error("Error creating preview:",n),this.showError("Failed to create image preview.")})}}removeImage(){this.selectedFile=null,this.imagePreview=null;let r=document.getElementById("fileInput");r&&(r.value="")}triggerFileInput(){let r=document.getElementById("fileInput");r&&r.click()}onCompleteProfile(){if(this.profileForm.valid){let r=sessionStorage.getItem("pendingSignupData");if(!r){this.showError("Session expired. Please start over."),this.router.navigate(["/auth/signup"]);return}let e=JSON.parse(r),t=this.fileUploadService.prepareRegistrationFormData(e,this.profileForm.value.description||"",this.selectedFile||void 0);this.isLoading=!0,this.registerWithProfile(t)}else this.markFormGroupTouched()}registerWithProfile(r){this.fileUploadService.registerWithProfile(r).subscribe({next:e=>{e.isSuccess&&(sessionStorage.removeItem("pendingSignupData"),this.showSuccess("Account created successfully! Please check your email for verification."),e.data?.userId?this.router.navigate(["/auth/otp-verification"],{queryParams:{userId:e.data.userId}}):this.router.navigate(["/auth/login"]))},error:e=>{console.error("Registration failed:",e),this.isLoading=!1}})}onBack(){this.router.navigate(["/auth/signup"])}onImageError(r){this.logoLoaded=!1,console.log("Logo failed to load, showing fallback")}markFormGroupTouched(){Object.keys(this.profileForm.controls).forEach(r=>{this.profileForm.get(r)?.markAsTouched()})}showError(r){this.snackBar.open(r,"Close",{duration:5e3,panelClass:["error-snackbar"]})}showSuccess(r){this.snackBar.open(r,"Close",{duration:5e3,panelClass:["success-snackbar"]})}get descriptionControl(){return this.profileForm.get("description")}static \u0275fac=function(e){return new(e||i)(g(z),g(O),g(gt),g($),g(R),g(V))};static \u0275cmp=w({type:i,selectors:[["app-profile-picture"]],standalone:!1,decls:48,vars:17,consts:[[1,"auth-container"],[1,"auth-card","profile-card"],[1,"auth-header"],[1,"auth-logo"],["src","logo/logo2.png","alt","Chattrix Logo",3,"error"],[1,"logo-fallback"],[1,"logo-text"],[1,"auth-title"],[1,"auth-subtitle"],[1,"auth-form",3,"ngSubmit","formGroup"],[1,"profile-picture-section"],[1,"profile-picture-container"],[1,"profile-picture-preview"],["alt","Profile Preview","class","preview-image",3,"src",4,"ngIf"],["class","placeholder-content",4,"ngIf"],["type","button","mat-icon-button","","class","remove-image-btn","aria-label","Remove image",3,"click",4,"ngIf"],["type","file","id","fileInput","accept","image/jpeg,image/jpg,image/png",2,"display","none",3,"change"],["type","button","mat-stroked-button","","color","primary",1,"upload-button",3,"click"],[1,"upload-hint"],["appearance","fill",1,"full-width"],["matInput","","formControlName","description","placeholder","Tell us a bit about yourself","rows","4","maxlength","500"],["matSuffix",""],[1,"button-group"],["type","button","mat-stroked-button","",1,"back-button",3,"click","disabled"],["mat-raised-button","","color","primary","type","submit",1,"complete-button",3,"disabled"],[1,"button-content"],["diameter","20","class","button-spinner",4,"ngIf"],[1,"auth-footer"],[1,"footer-text"],["alt","Profile Preview",1,"preview-image",3,"src"],[1,"placeholder-content"],[1,"upload-icon"],[1,"upload-text"],["type","button","mat-icon-button","","aria-label","Remove image",1,"remove-image-btn",3,"click"],["diameter","20",1,"button-spinner"]],template:function(e,t){e&1&&(o(0,"div",0)(1,"mat-card",1)(2,"div",2)(3,"div",3)(4,"img",4),u("error",function(s){return t.onImageError(s)}),a(),o(5,"div",5)(6,"span",6),d(7,"C"),a()()(),o(8,"h1",7),d(9,"Complete Your Profile"),a(),o(10,"p",8),d(11," Add a profile picture and tell us about yourself "),a()(),o(12,"form",9),u("ngSubmit",function(){return t.onCompleteProfile()}),o(13,"div",10)(14,"div",11)(15,"div",12),f(16,qn,1,1,"img",13)(17,Hn,5,0,"div",14)(18,Un,3,0,"button",15),a(),o(19,"input",16),u("change",function(s){return t.onFileSelected(s)}),a(),o(20,"button",17),u("click",function(){return t.triggerFileInput()}),o(21,"mat-icon"),d(22,"cloud_upload"),a(),d(23),a(),o(24,"p",18),d(25,"Supported formats: JPEG, JPG, PNG (Max 5MB)"),a()()(),o(26,"mat-form-field",19)(27,"mat-label"),d(28,"About You (Optional)"),a(),h(29,"textarea",20),o(30,"mat-icon",21),d(31,"description"),a(),o(32,"mat-hint"),d(33),a()(),o(34,"div",22)(35,"button",23),u("click",function(){return t.onBack()}),o(36,"mat-icon"),d(37,"arrow_back"),a(),d(38," Back "),a(),o(39,"button",24)(40,"div",25),f(41,Gn,1,0,"mat-spinner",26),o(42,"span"),d(43,"Complete Registration"),a()()()()(),o(44,"div",27),h(45,"mat-divider"),o(46,"p",28),d(47," By completing registration, you agree to our Terms of Service and Privacy Policy. "),a()()()()),e&2&&(l(4),P("display",t.logoLoaded?"block":"none"),l(),P("display",t.logoLoaded?"none":"flex"),l(7),c("formGroup",t.profileForm),l(3),v("has-image",t.imagePreview),l(),c("ngIf",t.imagePreview),l(),c("ngIf",!t.imagePreview),l(),c("ngIf",t.imagePreview),l(5),ue(" ",t.imagePreview?"Change Photo":"Upload Photo"," "),l(10),ue("",(t.descriptionControl==null||t.descriptionControl.value==null?null:t.descriptionControl.value.length)||0,"/500"),l(2),c("disabled",t.isLoading),l(4),c("disabled",t.isLoading),l(2),c("ngIf",t.isLoading),l(),v("hidden",t.isLoading))},dependencies:[H,G,ne,oe,U,nt,L,ae,W,X,N,We,fe,se,D,ve,Q,Y,Z],styles:[".profile-card[_ngcontent-%COMP%]{max-width:520px}.profile-card[_ngcontent-%COMP%]   .auth-header[_ngcontent-%COMP%]{padding:var(--spacing-sm) var(--spacing-lg) var(--spacing-xs)}.profile-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%], .profile-card[_ngcontent-%COMP%]   .auth-footer[_ngcontent-%COMP%]{padding:var(--spacing-sm) var(--spacing-lg)}.profile-card[_ngcontent-%COMP%]   .auth-logo[_ngcontent-%COMP%]{width:50px;height:50px;margin-bottom:var(--spacing-sm)}.profile-card[_ngcontent-%COMP%]   .auth-title[_ngcontent-%COMP%]{font-size:var(--font-size-lg);margin-bottom:var(--spacing-xs)}.profile-card[_ngcontent-%COMP%]   .auth-subtitle[_ngcontent-%COMP%]{font-size:var(--font-size-xs);margin-bottom:0}.profile-picture-section[_ngcontent-%COMP%]{margin-bottom:var(--spacing-lg);text-align:center}.profile-picture-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:var(--spacing-md)}.profile-picture-preview[_ngcontent-%COMP%]{position:relative;width:120px;height:120px;border-radius:50%;border:3px dashed var(--border-color);display:flex;align-items:center;justify-content:center;overflow:hidden;transition:all .3s ease;background-color:var(--bg-secondary)}.profile-picture-preview.has-image[_ngcontent-%COMP%]{border:3px solid var(--accent-green);border-style:solid}.profile-picture-preview[_ngcontent-%COMP%]:hover{border-color:var(--accent-green);background-color:var(--bg-tertiary)}.preview-image[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover;border-radius:50%}.placeholder-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:var(--spacing-xs);color:var(--text-secondary)}.upload-icon[_ngcontent-%COMP%]{font-size:2.5rem;width:2.5rem;height:2.5rem;color:var(--text-disabled)}.upload-text[_ngcontent-%COMP%]{font-size:var(--font-size-sm);font-weight:500}.remove-image-btn[_ngcontent-%COMP%]{position:absolute;top:-8px;right:-8px;background-color:var(--error);color:#fff;width:28px;height:28px;min-width:28px}.remove-image-btn[_ngcontent-%COMP%]:hover{background-color:var(--error-dark)}.remove-image-btn[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%]{font-size:16px;width:16px;height:16px}.upload-button[_ngcontent-%COMP%]{min-width:140px;height:40px;border-radius:var(--radius-md);font-weight:500;text-transform:none}.upload-button[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%]{margin-right:var(--spacing-xs)}.upload-hint[_ngcontent-%COMP%]{font-size:var(--font-size-xs);color:var(--text-muted);margin:0;text-align:center}.full-width[_ngcontent-%COMP%]{width:100%;margin-bottom:var(--spacing-md)}.button-group[_ngcontent-%COMP%]{display:flex;gap:var(--spacing-md);margin-top:var(--spacing-lg);justify-content:space-between}.back-button[_ngcontent-%COMP%]{flex:0 0 auto;min-width:100px;height:44px;border-radius:var(--radius-md);font-weight:500;text-transform:none}.back-button[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%]{margin-right:var(--spacing-xs)}.complete-button[_ngcontent-%COMP%]{flex:1;height:44px;font-size:var(--font-size-base);font-weight:500;text-transform:none;border-radius:var(--radius-md);position:relative}.complete-button[_ngcontent-%COMP%]:disabled{opacity:.6}.complete-button[_ngcontent-%COMP%]   .button-content[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:var(--spacing-sm);position:relative}.complete-button[_ngcontent-%COMP%]   .hidden[_ngcontent-%COMP%]{visibility:hidden;opacity:0}.button-spinner[_ngcontent-%COMP%]{position:absolute;transform:translate(-50%,-50%)}.footer-text[_ngcontent-%COMP%]{margin:var(--spacing-sm) 0 0;text-align:center;color:var(--text-muted);font-size:var(--font-size-xs);line-height:1.4}"]})};var qe=class i{constructor(r,e,t){this.authService=r;this.authState=e;this.router=t}canActivate(r,e){return this.authState.authState$.pipe(He(1),Ze(t=>t.isAuthenticated?(this.router.navigate(["/dashboard"]),!1):!0))}static \u0275fac=function(e){return new(e||i)(he(z),he(O),he(R))};static \u0275prov=J({token:i,factory:i.\u0275fac,providedIn:"root"})};var $n=[{path:"",redirectTo:"login",pathMatch:"full"},{path:"login",component:ct,title:"Login - Chattrix",canActivate:[qe]},{path:"signup",component:mt,title:"Sign Up - Chattrix",canActivate:[qe]},{path:"otp-verification",component:pt,title:"Verify Account - Chattrix"},{path:"profile-picture",component:_t,title:"Complete Profile - Chattrix"},{path:"forgot-password",component:ft,title:"Forgot Password - Chattrix",canActivate:[qe]},{path:"verify-reset-token",component:ut,title:"Verify Reset Code - Chattrix"},{path:"reset-password",component:ht,title:"Reset Password - Chattrix"},{path:"**",redirectTo:"login"}],vt=class i{static \u0275fac=function(e){return new(e||i)};static \u0275mod=q({type:i});static \u0275inj=j({imports:[Pt.forChild($n),Pt]})};var Ki=class i{static \u0275fac=function(e){return new(e||i)};static \u0275mod=q({type:i});static \u0275inj=j({imports:[ai,fi,pi,vt,bi,Ie,Ui,ot,xi,$i,Yi,ki]})};export{Ki as AuthenticationModule};
