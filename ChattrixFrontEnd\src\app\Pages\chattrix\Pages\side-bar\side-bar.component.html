<div class="sidebar-container" *ngIf="userProfile$ | async as userProfile">
  <!-- User Profile Section -->
  <div class="user-profile-section">
    <div class="profile-header">
      <!-- Logo -->
      <div class="app-logo">
        <img
          src="logo/logo2.png"
          alt="Chattrix Logo"
          class="logo-image"
          #logoImg
          (error)="logoImg.style.display = 'none'"
        />
        <span
          class="logo-fallback"
          [style.display]="logoImg.style.display === 'none' ? 'flex' : 'none'"
          >C</span
        >
      </div>
      <h2 class="app-name">Chattrix</h2>
    </div>

    <!-- User Info Card -->
    <mat-card class="user-info-card">
      <div class="user-avatar-section">
        <!-- Profile Picture or Initials -->
        <div
          class="user-avatar"
          [class.has-image]="userProfile.profilePictureUrl"
        >
          <img
            *ngIf="userProfile.profilePictureUrl"
            [src]="userProfile.profilePictureUrl"
            [alt]="getUserDisplayName(userProfile)"
            class="avatar-image"
            #avatarImg
            (error)="avatarImg.style.display = 'none'"
          />
          <span *ngIf="!userProfile.profilePictureUrl" class="avatar-initials">
            {{ getUserInitials(userProfile) }}
          </span>
        </div>

        <!-- User Details -->
        <div class="user-details">
          <h3 class="user-name">{{ getUserDisplayName(userProfile) }}</h3>
          <p class="user-phone" *ngIf="userProfile.phoneNumber">
            {{ userProfile.phoneNumber }}
          </p>
          <p class="user-email">{{ userProfile.email }}</p>
          <p class="user-role">{{ getUserRole(userProfile) }}</p>
          <p class="user-description" *ngIf="userProfile.description">
            {{ userProfile.description }}
          </p>
        </div>
      </div>
    </mat-card>
  </div>

  <!-- Navigation Menu -->
  <div class="navigation-section">
    <mat-nav-list class="nav-list">
      <ng-container *ngFor="let item of navigationItems">
        <mat-list-item
          *ngIf="shouldShowNavItem(item, (hasAdminAccess$ | async) || false)"
          class="nav-item"
          [class.active]="router.url === item.route"
          (click)="onNavItemClick(item)"
          matRipple
        >
          <mat-icon matListItemIcon class="nav-icon">{{ item.icon }}</mat-icon>
          <span matListItemTitle class="nav-label">{{ item.label }}</span>
          <span
            *ngIf="item.badge && item.badge > 0"
            class="nav-badge"
            matListItemMeta
          >
            {{ item.badge }}
          </span>
        </mat-list-item>
      </ng-container>
    </mat-nav-list>
  </div>

  <!-- Settings Section -->
  <div class="settings-section">
    <mat-divider></mat-divider>

    <!-- Dark Mode Toggle -->
    <div class="setting-item">
      <mat-icon class="setting-icon">{{
        isDarkMode ? "light_mode" : "dark_mode"
      }}</mat-icon>
      <span class="setting-label">Dark Mode</span>
      <mat-slide-toggle
        [checked]="isDarkMode"
        (change)="toggleTheme()"
        class="theme-toggle"
        color="primary"
      ></mat-slide-toggle>
    </div>

    <!-- Logout Button -->
    <button
      mat-stroked-button
      class="logout-button"
      (click)="onLogout()"
      color="warn"
    >
      <mat-icon>logout</mat-icon>
      <span>Logout</span>
    </button>
  </div>
</div>
