import{$ as ue,A as ae,B as re,D as p,E as se,F as ce,G as X,I as le,J as de,K as me,L as pt,N as _t,O as he,P as pe,Q as ut,R as _e,X as H,Z as W,_ as gt,a as Jt,aa as ft,c as te,r as ee,s as ie,u as Et,v as ht,x as ne,y as oe}from"./chunk-MSJACPQM.js";import{$a as Dt,Aa as f,Ba as dt,Ca as et,Da as a,E as Ht,Ea as r,Eb as q,Fa as O,Fb as At,Ga as Yt,H as Pt,Ha as $t,I as Q,Ib as Z,J as _,Ja as I,Jb as K,K as A,Ka as qt,L as G,La as M,M as d,Ma as h,Na as S,O as Ot,Oa as v,P as x,Pa as R,Q as k,Qa as z,R as st,Ra as C,Sa as T,T as j,U as ct,V as Qt,Va as U,W as tt,Wa as c,X as lt,Xa as B,Ya as it,Z as F,_a as $,a as at,ab as St,b as Tt,ca as Y,d as jt,db as nt,eb as N,f as J,fb as Xt,g as rt,ha as Gt,ia as s,l as L,ma as E,nb as Wt,ob as mt,q as Vt,qa as g,qb as Zt,ra as u,rb as Kt,sa as P,ta as V,ua as b,v as Ut,xa as D,ya as m,za as It}from"./chunk-UM6LOK3U.js";var ge=(()=>{class e{static \u0275fac=function(i){return new(i||e)};static \u0275mod=u({type:e});static \u0275inj=_({imports:[p,H,H,p]})}return e})();var fe=(()=>{class e{static \u0275fac=function(i){return new(i||e)};static \u0275mod=u({type:e});static \u0275inj=_({imports:[p]})}return e})();var Be=["*"],Ne=`.mdc-list{margin:0;padding:8px 0;list-style-type:none}.mdc-list:focus{outline:none}.mdc-list-item{display:flex;position:relative;justify-content:flex-start;overflow:hidden;padding:0;align-items:stretch;cursor:pointer;padding-left:16px;padding-right:16px;background-color:var(--mdc-list-list-item-container-color, transparent);border-radius:var(--mdc-list-list-item-container-shape, var(--mat-sys-corner-none))}.mdc-list-item.mdc-list-item--selected{background-color:var(--mdc-list-list-item-selected-container-color)}.mdc-list-item:focus{outline:0}.mdc-list-item.mdc-list-item--disabled{cursor:auto}.mdc-list-item.mdc-list-item--with-one-line{height:var(--mdc-list-list-item-one-line-container-height, 48px)}.mdc-list-item.mdc-list-item--with-one-line .mdc-list-item__start{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-one-line .mdc-list-item__end{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-two-lines{height:var(--mdc-list-list-item-two-line-container-height, 64px)}.mdc-list-item.mdc-list-item--with-two-lines .mdc-list-item__start{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--with-two-lines .mdc-list-item__end{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-three-lines{height:var(--mdc-list-list-item-three-line-container-height, 88px)}.mdc-list-item.mdc-list-item--with-three-lines .mdc-list-item__start{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--with-three-lines .mdc-list-item__end{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--selected::before,.mdc-list-item.mdc-list-item--selected:focus::before,.mdc-list-item:not(.mdc-list-item--selected):focus::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;content:"";pointer-events:none}a.mdc-list-item{color:inherit;text-decoration:none}.mdc-list-item__start{fill:currentColor;flex-shrink:0;pointer-events:none}.mdc-list-item--with-leading-icon .mdc-list-item__start{color:var(--mdc-list-list-item-leading-icon-color, var(--mat-sys-on-surface-variant));width:var(--mdc-list-list-item-leading-icon-size, 24px);height:var(--mdc-list-list-item-leading-icon-size, 24px);margin-left:16px;margin-right:32px}[dir=rtl] .mdc-list-item--with-leading-icon .mdc-list-item__start{margin-left:32px;margin-right:16px}.mdc-list-item--with-leading-icon:hover .mdc-list-item__start{color:var(--mdc-list-list-item-hover-leading-icon-color)}.mdc-list-item--with-leading-avatar .mdc-list-item__start{width:var(--mdc-list-list-item-leading-avatar-size, 40px);height:var(--mdc-list-list-item-leading-avatar-size, 40px);margin-left:16px;margin-right:16px;border-radius:50%}.mdc-list-item--with-leading-avatar .mdc-list-item__start,[dir=rtl] .mdc-list-item--with-leading-avatar .mdc-list-item__start{margin-left:16px;margin-right:16px;border-radius:50%}.mdc-list-item__end{flex-shrink:0;pointer-events:none}.mdc-list-item--with-trailing-meta .mdc-list-item__end{font-family:var(--mdc-list-list-item-trailing-supporting-text-font, var(--mat-sys-label-small-font));line-height:var(--mdc-list-list-item-trailing-supporting-text-line-height, var(--mat-sys-label-small-line-height));font-size:var(--mdc-list-list-item-trailing-supporting-text-size, var(--mat-sys-label-small-size));font-weight:var(--mdc-list-list-item-trailing-supporting-text-weight, var(--mat-sys-label-small-weight));letter-spacing:var(--mdc-list-list-item-trailing-supporting-text-tracking, var(--mat-sys-label-small-tracking))}.mdc-list-item--with-trailing-icon .mdc-list-item__end{color:var(--mdc-list-list-item-trailing-icon-color, var(--mat-sys-on-surface-variant));width:var(--mdc-list-list-item-trailing-icon-size, 24px);height:var(--mdc-list-list-item-trailing-icon-size, 24px)}.mdc-list-item--with-trailing-icon:hover .mdc-list-item__end{color:var(--mdc-list-list-item-hover-trailing-icon-color)}.mdc-list-item.mdc-list-item--with-trailing-meta .mdc-list-item__end{color:var(--mdc-list-list-item-trailing-supporting-text-color, var(--mat-sys-on-surface-variant))}.mdc-list-item--selected.mdc-list-item--with-trailing-icon .mdc-list-item__end{color:var(--mdc-list-list-item-selected-trailing-icon-color, var(--mat-sys-primary))}.mdc-list-item__content{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;align-self:center;flex:1;pointer-events:none}.mdc-list-item--with-two-lines .mdc-list-item__content,.mdc-list-item--with-three-lines .mdc-list-item__content{align-self:stretch}.mdc-list-item__primary-text{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;color:var(--mdc-list-list-item-label-text-color, var(--mat-sys-on-surface));font-family:var(--mdc-list-list-item-label-text-font, var(--mat-sys-body-large-font));line-height:var(--mdc-list-list-item-label-text-line-height, var(--mat-sys-body-large-line-height));font-size:var(--mdc-list-list-item-label-text-size, var(--mat-sys-body-large-size));font-weight:var(--mdc-list-list-item-label-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mdc-list-list-item-label-text-tracking, var(--mat-sys-body-large-tracking))}.mdc-list-item:hover .mdc-list-item__primary-text{color:var(--mdc-list-list-item-hover-label-text-color, var(--mat-sys-on-surface))}.mdc-list-item:focus .mdc-list-item__primary-text{color:var(--mdc-list-list-item-focus-label-text-color, var(--mat-sys-on-surface))}.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-three-lines .mdc-list-item__primary-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-three-lines .mdc-list-item__primary-text::before{display:inline-block;width:0;height:28px;content:"";vertical-align:0}.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-three-lines .mdc-list-item__primary-text::after{display:inline-block;width:0;height:20px;content:"";vertical-align:-20px}.mdc-list-item__secondary-text{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;display:block;margin-top:0;color:var(--mdc-list-list-item-supporting-text-color, var(--mat-sys-on-surface-variant));font-family:var(--mdc-list-list-item-supporting-text-font, var(--mat-sys-body-medium-font));line-height:var(--mdc-list-list-item-supporting-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mdc-list-list-item-supporting-text-size, var(--mat-sys-body-medium-size));font-weight:var(--mdc-list-list-item-supporting-text-weight, var(--mat-sys-body-medium-weight));letter-spacing:var(--mdc-list-list-item-supporting-text-tracking, var(--mat-sys-body-medium-tracking))}.mdc-list-item__secondary-text::before{display:inline-block;width:0;height:20px;content:"";vertical-align:0}.mdc-list-item--with-three-lines .mdc-list-item__secondary-text{white-space:normal;line-height:20px}.mdc-list-item--with-overline .mdc-list-item__secondary-text{white-space:nowrap;line-height:auto}.mdc-list-item--with-leading-radio.mdc-list-item,.mdc-list-item--with-leading-checkbox.mdc-list-item,.mdc-list-item--with-leading-icon.mdc-list-item,.mdc-list-item--with-leading-avatar.mdc-list-item{padding-left:0;padding-right:16px}[dir=rtl] .mdc-list-item--with-leading-radio.mdc-list-item,[dir=rtl] .mdc-list-item--with-leading-checkbox.mdc-list-item,[dir=rtl] .mdc-list-item--with-leading-icon.mdc-list-item,[dir=rtl] .mdc-list-item--with-leading-avatar.mdc-list-item{padding-left:16px;padding-right:0}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before{display:inline-block;width:0;height:32px;content:"";vertical-align:0}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after{display:inline-block;width:0;height:20px;content:"";vertical-align:-20px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end{display:block;margin-top:0;line-height:normal}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before{display:inline-block;width:0;height:32px;content:"";vertical-align:0}.mdc-list-item--with-trailing-icon.mdc-list-item,[dir=rtl] .mdc-list-item--with-trailing-icon.mdc-list-item{padding-left:0;padding-right:0}.mdc-list-item--with-trailing-icon .mdc-list-item__end{margin-left:16px;margin-right:16px}.mdc-list-item--with-trailing-meta.mdc-list-item{padding-left:16px;padding-right:0}[dir=rtl] .mdc-list-item--with-trailing-meta.mdc-list-item{padding-left:0;padding-right:16px}.mdc-list-item--with-trailing-meta .mdc-list-item__end{-webkit-user-select:none;user-select:none;margin-left:28px;margin-right:16px}[dir=rtl] .mdc-list-item--with-trailing-meta .mdc-list-item__end{margin-left:16px;margin-right:28px}.mdc-list-item--with-trailing-meta.mdc-list-item--with-three-lines .mdc-list-item__end,.mdc-list-item--with-trailing-meta.mdc-list-item--with-two-lines .mdc-list-item__end{display:block;line-height:normal;align-self:flex-start;margin-top:0}.mdc-list-item--with-trailing-meta.mdc-list-item--with-three-lines .mdc-list-item__end::before,.mdc-list-item--with-trailing-meta.mdc-list-item--with-two-lines .mdc-list-item__end::before{display:inline-block;width:0;height:28px;content:"";vertical-align:0}.mdc-list-item--with-leading-radio .mdc-list-item__start,.mdc-list-item--with-leading-checkbox .mdc-list-item__start{margin-left:8px;margin-right:24px}[dir=rtl] .mdc-list-item--with-leading-radio .mdc-list-item__start,[dir=rtl] .mdc-list-item--with-leading-checkbox .mdc-list-item__start{margin-left:24px;margin-right:8px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__start,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__start{align-self:flex-start;margin-top:8px}.mdc-list-item--with-trailing-radio.mdc-list-item,.mdc-list-item--with-trailing-checkbox.mdc-list-item{padding-left:16px;padding-right:0}[dir=rtl] .mdc-list-item--with-trailing-radio.mdc-list-item,[dir=rtl] .mdc-list-item--with-trailing-checkbox.mdc-list-item{padding-left:0;padding-right:16px}.mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-icon,.mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-avatar,.mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-icon,.mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-avatar{padding-left:0}[dir=rtl] .mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-icon,[dir=rtl] .mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-avatar,[dir=rtl] .mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-icon,[dir=rtl] .mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-avatar{padding-right:0}.mdc-list-item--with-trailing-radio .mdc-list-item__end,.mdc-list-item--with-trailing-checkbox .mdc-list-item__end{margin-left:24px;margin-right:8px}[dir=rtl] .mdc-list-item--with-trailing-radio .mdc-list-item__end,[dir=rtl] .mdc-list-item--with-trailing-checkbox .mdc-list-item__end{margin-left:8px;margin-right:24px}.mdc-list-item--with-trailing-radio.mdc-list-item--with-three-lines .mdc-list-item__end,.mdc-list-item--with-trailing-checkbox.mdc-list-item--with-three-lines .mdc-list-item__end{align-self:flex-start;margin-top:8px}.mdc-list-group__subheader{margin:.75rem 16px}.mdc-list-item--disabled .mdc-list-item__start,.mdc-list-item--disabled .mdc-list-item__content,.mdc-list-item--disabled .mdc-list-item__end{opacity:1}.mdc-list-item--disabled .mdc-list-item__primary-text,.mdc-list-item--disabled .mdc-list-item__secondary-text{opacity:var(--mdc-list-list-item-disabled-label-text-opacity, 0.3)}.mdc-list-item--disabled.mdc-list-item--with-leading-icon .mdc-list-item__start{color:var(--mdc-list-list-item-disabled-leading-icon-color, var(--mat-sys-on-surface));opacity:var(--mdc-list-list-item-disabled-leading-icon-opacity, 0.38)}.mdc-list-item--disabled.mdc-list-item--with-trailing-icon .mdc-list-item__end{color:var(--mdc-list-list-item-disabled-trailing-icon-color, var(--mat-sys-on-surface));opacity:var(--mdc-list-list-item-disabled-trailing-icon-opacity, 0.38)}.mat-mdc-list-item.mat-mdc-list-item-both-leading-and-trailing,[dir=rtl] .mat-mdc-list-item.mat-mdc-list-item-both-leading-and-trailing{padding-left:0;padding-right:0}.mdc-list-item.mdc-list-item--disabled .mdc-list-item__primary-text{color:var(--mdc-list-list-item-disabled-label-text-color, var(--mat-sys-on-surface))}.mdc-list-item:hover::before{background-color:var(--mdc-list-list-item-hover-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mdc-list-list-item-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mdc-list-item.mdc-list-item--disabled::before{background-color:var(--mdc-list-list-item-disabled-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mdc-list-list-item-disabled-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-list-item:focus::before{background-color:var(--mdc-list-list-item-focus-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mdc-list-list-item-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-list-item--disabled .mdc-radio,.mdc-list-item--disabled .mdc-checkbox{opacity:var(--mdc-list-list-item-disabled-label-text-opacity, 0.3)}.mdc-list-item--with-leading-avatar .mat-mdc-list-item-avatar{border-radius:var(--mdc-list-list-item-leading-avatar-shape, var(--mat-sys-corner-full));background-color:var(--mdc-list-list-item-leading-avatar-color, var(--mat-sys-primary-container))}.mat-mdc-list-item-icon{font-size:var(--mdc-list-list-item-leading-icon-size, 24px)}@media(forced-colors: active){a.mdc-list-item--activated::after{content:"";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}a.mdc-list-item--activated [dir=rtl]::after{right:auto;left:16px}}.mat-mdc-list-base{display:block}.mat-mdc-list-base .mdc-list-item__start,.mat-mdc-list-base .mdc-list-item__end,.mat-mdc-list-base .mdc-list-item__content{pointer-events:auto}.mat-mdc-list-item,.mat-mdc-list-option{width:100%;box-sizing:border-box;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-list-item:not(.mat-mdc-list-item-interactive),.mat-mdc-list-option:not(.mat-mdc-list-item-interactive){cursor:default}.mat-mdc-list-item .mat-divider-inset,.mat-mdc-list-option .mat-divider-inset{position:absolute;left:0;right:0;bottom:0}.mat-mdc-list-item .mat-mdc-list-item-avatar~.mat-divider-inset,.mat-mdc-list-option .mat-mdc-list-item-avatar~.mat-divider-inset{margin-left:72px}[dir=rtl] .mat-mdc-list-item .mat-mdc-list-item-avatar~.mat-divider-inset,[dir=rtl] .mat-mdc-list-option .mat-mdc-list-item-avatar~.mat-divider-inset{margin-right:72px}.mat-mdc-list-item-interactive::before{top:0;left:0;right:0;bottom:0;position:absolute;content:"";opacity:0;pointer-events:none;border-radius:inherit}.mat-mdc-list-item>.mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-mdc-list-item:focus>.mat-focus-indicator::before{content:""}.mat-mdc-list-item.mdc-list-item--with-three-lines .mat-mdc-list-item-line.mdc-list-item__secondary-text{white-space:nowrap;line-height:normal}.mat-mdc-list-item.mdc-list-item--with-three-lines .mat-mdc-list-item-unscoped-content.mdc-list-item__secondary-text{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2}mat-action-list button{background:none;color:inherit;border:none;font:inherit;outline:inherit;-webkit-tap-highlight-color:rgba(0,0,0,0);text-align:start}mat-action-list button::-moz-focus-inner{border:0}.mdc-list-item--with-leading-icon .mdc-list-item__start{margin-inline-start:var(--mat-list-list-item-leading-icon-start-space, 16px);margin-inline-end:var(--mat-list-list-item-leading-icon-end-space, 16px)}.mat-mdc-nav-list .mat-mdc-list-item{border-radius:var(--mat-list-active-indicator-shape, var(--mat-sys-corner-full));--mat-focus-indicator-border-radius:var(--mat-list-active-indicator-shape, var(--mat-sys-corner-full))}.mat-mdc-nav-list .mat-mdc-list-item.mdc-list-item--activated{background-color:var(--mat-list-active-indicator-color, var(--mat-sys-secondary-container))}
`,je=["unscopedContent"],Ve=["text"],Ue=[[["","matListItemAvatar",""],["","matListItemIcon",""]],[["","matListItemTitle",""]],[["","matListItemLine",""]],"*",[["","matListItemMeta",""]],[["mat-divider"]]],He=["[matListItemAvatar],[matListItemIcon]","[matListItemTitle]","[matListItemLine]","*","[matListItemMeta]","mat-divider"];var Qe=new A("ListOption"),Lt=(()=>{class e{_elementRef=d(F);constructor(){}static \u0275fac=function(i){return new(i||e)};static \u0275dir=P({type:e,selectors:[["","matListItemTitle",""]],hostAttrs:[1,"mat-mdc-list-item-title","mdc-list-item__primary-text"]})}return e})(),Ge=(()=>{class e{_elementRef=d(F);constructor(){}static \u0275fac=function(i){return new(i||e)};static \u0275dir=P({type:e,selectors:[["","matListItemLine",""]],hostAttrs:[1,"mat-mdc-list-item-line","mdc-list-item__secondary-text"]})}return e})(),Ft=(()=>{class e{static \u0275fac=function(i){return new(i||e)};static \u0275dir=P({type:e,selectors:[["","matListItemMeta",""]],hostAttrs:[1,"mat-mdc-list-item-meta","mdc-list-item__end"]})}return e})(),be=(()=>{class e{_listOption=d(Qe,{optional:!0});constructor(){}_isAlignedAtStart(){return!this._listOption||this._listOption?._getTogglePosition()==="after"}static \u0275fac=function(i){return new(i||e)};static \u0275dir=P({type:e,hostVars:4,hostBindings:function(i,o){i&2&&f("mdc-list-item__start",o._isAlignedAtStart())("mdc-list-item__end",!o._isAlignedAtStart())}})}return e})(),Ye=(()=>{class e extends be{static \u0275fac=(()=>{let t;return function(o){return(t||(t=j(e)))(o||e)}})();static \u0275dir=P({type:e,selectors:[["","matListItemAvatar",""]],hostAttrs:[1,"mat-mdc-list-item-avatar"],features:[V]})}return e})(),zt=(()=>{class e extends be{static \u0275fac=(()=>{let t;return function(o){return(t||(t=j(e)))(o||e)}})();static \u0275dir=P({type:e,selectors:[["","matListItemIcon",""]],hostAttrs:[1,"mat-mdc-list-item-icon"],features:[V]})}return e})(),$e=new A("MAT_LIST_CONFIG"),Rt=(()=>{class e{_isNonInteractive=!0;get disableRipple(){return this._disableRipple}set disableRipple(t){this._disableRipple=X(t)}_disableRipple=!1;get disabled(){return this._disabled}set disabled(t){this._disabled=X(t)}_disabled=!1;_defaultOptions=d($e,{optional:!0});static \u0275fac=function(i){return new(i||e)};static \u0275dir=P({type:e,hostVars:1,hostBindings:function(i,o){i&2&&D("aria-disabled",o.disabled)},inputs:{disableRipple:"disableRipple",disabled:"disabled"}})}return e})(),qe=(()=>{class e{_elementRef=d(F);_ngZone=d(lt);_listBase=d(Rt,{optional:!0});_platform=d(ee);_hostElement;_isButtonElement;_noopAnimations;_avatars;_icons;set lines(t){this._explicitLines=ie(t,null),this._updateItemLines(!1)}_explicitLines=null;get disableRipple(){return this.disabled||this._disableRipple||this._noopAnimations||!!this._listBase?.disableRipple}set disableRipple(t){this._disableRipple=X(t)}_disableRipple=!1;get disabled(){return this._disabled||!!this._listBase?.disabled}set disabled(t){this._disabled=X(t)}_disabled=!1;_subscriptions=new jt;_rippleRenderer=null;_hasUnscopedTextContent=!1;rippleConfig;get rippleDisabled(){return this.disableRipple||!!this.rippleConfig.disabled}constructor(){d(ht).load(pt);let t=d(de,{optional:!0}),i=d(Y,{optional:!0});this.rippleConfig=t||{},this._hostElement=this._elementRef.nativeElement,this._isButtonElement=this._hostElement.nodeName.toLowerCase()==="button",this._noopAnimations=i==="NoopAnimations",this._listBase&&!this._listBase._isNonInteractive&&this._initInteractiveListItem(),this._isButtonElement&&!this._hostElement.hasAttribute("type")&&this._hostElement.setAttribute("type","button")}ngAfterViewInit(){this._monitorProjectedLinesAndTitle(),this._updateItemLines(!0)}ngOnDestroy(){this._subscriptions.unsubscribe(),this._rippleRenderer!==null&&this._rippleRenderer._removeTriggerEvents()}_hasIconOrAvatar(){return!!(this._avatars.length||this._icons.length)}_initInteractiveListItem(){this._hostElement.classList.add("mat-mdc-list-item-interactive"),this._rippleRenderer=new le(this,this._ngZone,this._hostElement,this._platform,d(ct)),this._rippleRenderer.setupTriggerEvents(this._hostElement)}_monitorProjectedLinesAndTitle(){this._ngZone.runOutsideAngular(()=>{this._subscriptions.add(Vt(this._lines.changes,this._titles.changes).subscribe(()=>this._updateItemLines(!1)))})}_updateItemLines(t){if(!this._lines||!this._titles||!this._unscopedContent)return;t&&this._checkDomForUnscopedTextContent();let i=this._explicitLines??this._inferLinesFromContent(),o=this._unscopedContent.nativeElement;if(this._hostElement.classList.toggle("mat-mdc-list-item-single-line",i<=1),this._hostElement.classList.toggle("mdc-list-item--with-one-line",i<=1),this._hostElement.classList.toggle("mdc-list-item--with-two-lines",i===2),this._hostElement.classList.toggle("mdc-list-item--with-three-lines",i===3),this._hasUnscopedTextContent){let l=this._titles.length===0&&i===1;o.classList.toggle("mdc-list-item__primary-text",l),o.classList.toggle("mdc-list-item__secondary-text",!l)}else o.classList.remove("mdc-list-item__primary-text"),o.classList.remove("mdc-list-item__secondary-text")}_inferLinesFromContent(){let t=this._titles.length+this._lines.length;return this._hasUnscopedTextContent&&(t+=1),t}_checkDomForUnscopedTextContent(){this._hasUnscopedTextContent=Array.from(this._unscopedContent.nativeElement.childNodes).filter(t=>t.nodeType!==t.COMMENT_NODE).some(t=>!!(t.textContent&&t.textContent.trim()))}static \u0275fac=function(i){return new(i||e)};static \u0275dir=P({type:e,contentQueries:function(i,o,l){if(i&1&&(R(l,Ye,4),R(l,zt,4)),i&2){let y;C(y=T())&&(o._avatars=y),C(y=T())&&(o._icons=y)}},hostVars:4,hostBindings:function(i,o){i&2&&(D("aria-disabled",o.disabled)("disabled",o._isButtonElement&&o.disabled||null),f("mdc-list-item--disabled",o.disabled))},inputs:{lines:"lines",disableRipple:"disableRipple",disabled:"disabled"}})}return e})();var ve=(()=>{class e extends qe{_lines;_titles;_meta;_unscopedContent;_itemText;get activated(){return this._activated}set activated(t){this._activated=X(t)}_activated=!1;_getAriaCurrent(){return this._hostElement.nodeName==="A"&&this._activated?"page":null}_hasBothLeadingAndTrailing(){return this._meta.length!==0&&(this._avatars.length!==0||this._icons.length!==0)}static \u0275fac=(()=>{let t;return function(o){return(t||(t=j(e)))(o||e)}})();static \u0275cmp=g({type:e,selectors:[["mat-list-item"],["a","mat-list-item",""],["button","mat-list-item",""]],contentQueries:function(i,o,l){if(i&1&&(R(l,Ge,5),R(l,Lt,5),R(l,Ft,5)),i&2){let y;C(y=T())&&(o._lines=y),C(y=T())&&(o._titles=y),C(y=T())&&(o._meta=y)}},viewQuery:function(i,o){if(i&1&&(z(je,5),z(Ve,5)),i&2){let l;C(l=T())&&(o._unscopedContent=l.first),C(l=T())&&(o._itemText=l.first)}},hostAttrs:[1,"mat-mdc-list-item","mdc-list-item"],hostVars:13,hostBindings:function(i,o){i&2&&(D("aria-current",o._getAriaCurrent()),f("mdc-list-item--activated",o.activated)("mdc-list-item--with-leading-avatar",o._avatars.length!==0)("mdc-list-item--with-leading-icon",o._icons.length!==0)("mdc-list-item--with-trailing-meta",o._meta.length!==0)("mat-mdc-list-item-both-leading-and-trailing",o._hasBothLeadingAndTrailing())("_mat-animation-noopable",o._noopAnimations))},inputs:{activated:"activated"},exportAs:["matListItem"],features:[V],ngContentSelectors:He,decls:10,vars:0,consts:[["unscopedContent",""],[1,"mdc-list-item__content"],[1,"mat-mdc-list-item-unscoped-content",3,"cdkObserveContent"],[1,"mat-focus-indicator"]],template:function(i,o){if(i&1){let l=I();S(Ue),v(0),a(1,"span",1),v(2,1),v(3,2),a(4,"span",2,0),M("cdkObserveContent",function(){return x(l),k(o._updateItemLines(!0))}),v(6,3),r()(),v(7,4),v(8,5),O(9,"div",3)}},dependencies:[ne],encapsulation:2,changeDetection:0})}return e})();var we=(()=>{class e extends Rt{_isNonInteractive=!1;static \u0275fac=(()=>{let t;return function(o){return(t||(t=j(e)))(o||e)}})();static \u0275cmp=g({type:e,selectors:[["mat-nav-list"]],hostAttrs:["role","navigation",1,"mat-mdc-nav-list","mat-mdc-list-base","mdc-list"],exportAs:["matNavList"],features:[$([{provide:Rt,useExisting:e}]),V],ngContentSelectors:Be,decls:1,vars:0,template:function(i,o){i&1&&(S(),v(0))},styles:[Ne],encapsulation:2,changeDetection:0})}return e})();var ye=(()=>{class e{static \u0275fac=function(i){return new(i||e)};static \u0275mod=u({type:e});static \u0275inj=_({imports:[oe,p,_t,fe,ft]})}return e})();var We=["mat-internal-form-field",""],Ze=["*"],xe=(()=>{class e{labelPosition;static \u0275fac=function(i){return new(i||e)};static \u0275cmp=g({type:e,selectors:[["div","mat-internal-form-field",""]],hostAttrs:[1,"mdc-form-field","mat-internal-form-field"],hostVars:2,hostBindings:function(i,o){i&2&&f("mdc-form-field--align-end",o.labelPosition==="before")},inputs:{labelPosition:"labelPosition"},attrs:We,ngContentSelectors:Ze,decls:1,vars:0,template:function(i,o){i&1&&(S(),v(0))},styles:[`.mat-internal-form-field{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:inline-flex;align-items:center;vertical-align:middle}.mat-internal-form-field>label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0;order:0}[dir=rtl] .mat-internal-form-field>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px}.mdc-form-field--align-end>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px;order:-1}[dir=rtl] .mdc-form-field--align-end .mdc-form-field--align-end label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0}
`],encapsulation:2,changeDetection:0})}return e})();var Ke=["switch"],Je=["*"];function ti(e,n){e&1&&(a(0,"span",10),st(),a(1,"svg",12),O(2,"path",13),r(),a(3,"svg",14),O(4,"path",15),r()())}var ei=new A("mat-slide-toggle-default-options",{providedIn:"root",factory:()=>({disableToggleValue:!1,hideIcon:!1,disabledInteractive:!1})}),ii={provide:Jt,useExisting:Pt(()=>vt),multi:!0},bt=class{source;checked;constructor(n,t){this.source=n,this.checked=t}},vt=(()=>{class e{_elementRef=d(F);_focusMonitor=d(Et);_changeDetectorRef=d(nt);defaults=d(ei);_onChange=t=>{};_onTouched=()=>{};_validatorOnChange=()=>{};_uniqueId;_checked=!1;_createChangeEvent(t){return new bt(this,t)}_labelId;get buttonId(){return`${this.id||this._uniqueId}-button`}_switchElement;focus(){this._switchElement.nativeElement.focus()}_noopAnimations;_focused;name=null;id;labelPosition="after";ariaLabel=null;ariaLabelledby=null;ariaDescribedby;required;color;disabled=!1;disableRipple=!1;tabIndex=0;get checked(){return this._checked}set checked(t){this._checked=t,this._changeDetectorRef.markForCheck()}hideIcon;disabledInteractive;change=new tt;toggleChange=new tt;get inputId(){return`${this.id||this._uniqueId}-input`}constructor(){d(ht).load(pt);let t=d(new Qt("tabindex"),{optional:!0}),i=this.defaults,o=d(Y,{optional:!0});this.tabIndex=t==null?0:parseInt(t)||0,this.color=i.color||"accent",this._noopAnimations=o==="NoopAnimations",this.id=this._uniqueId=d(re).getId("mat-mdc-slide-toggle-"),this.hideIcon=i.hideIcon??!1,this.disabledInteractive=i.disabledInteractive??!1,this._labelId=this._uniqueId+"-label"}ngAfterContentInit(){this._focusMonitor.monitor(this._elementRef,!0).subscribe(t=>{t==="keyboard"||t==="program"?(this._focused=!0,this._changeDetectorRef.markForCheck()):t||Promise.resolve().then(()=>{this._focused=!1,this._onTouched(),this._changeDetectorRef.markForCheck()})})}ngOnChanges(t){t.required&&this._validatorOnChange()}ngOnDestroy(){this._focusMonitor.stopMonitoring(this._elementRef)}writeValue(t){this.checked=!!t}registerOnChange(t){this._onChange=t}registerOnTouched(t){this._onTouched=t}validate(t){return this.required&&t.value!==!0?{required:!0}:null}registerOnValidatorChange(t){this._validatorOnChange=t}setDisabledState(t){this.disabled=t,this._changeDetectorRef.markForCheck()}toggle(){this.checked=!this.checked,this._onChange(this.checked)}_emitChangeEvent(){this._onChange(this.checked),this.change.emit(this._createChangeEvent(this.checked))}_handleClick(){this.disabled||(this.toggleChange.emit(),this.defaults.disableToggleValue||(this.checked=!this.checked,this._onChange(this.checked),this.change.emit(new bt(this,this.checked))))}_getAriaLabelledBy(){return this.ariaLabelledby?this.ariaLabelledby:this.ariaLabel?null:this._labelId}static \u0275fac=function(i){return new(i||e)};static \u0275cmp=g({type:e,selectors:[["mat-slide-toggle"]],viewQuery:function(i,o){if(i&1&&z(Ke,5),i&2){let l;C(l=T())&&(o._switchElement=l.first)}},hostAttrs:[1,"mat-mdc-slide-toggle"],hostVars:13,hostBindings:function(i,o){i&2&&(qt("id",o.id),D("tabindex",null)("aria-label",null)("name",null)("aria-labelledby",null),dt(o.color?"mat-"+o.color:""),f("mat-mdc-slide-toggle-focused",o._focused)("mat-mdc-slide-toggle-checked",o.checked)("_mat-animation-noopable",o._noopAnimations))},inputs:{name:"name",id:"id",labelPosition:"labelPosition",ariaLabel:[0,"aria-label","ariaLabel"],ariaLabelledby:[0,"aria-labelledby","ariaLabelledby"],ariaDescribedby:[0,"aria-describedby","ariaDescribedby"],required:[2,"required","required",N],color:"color",disabled:[2,"disabled","disabled",N],disableRipple:[2,"disableRipple","disableRipple",N],tabIndex:[2,"tabIndex","tabIndex",t=>t==null?0:Xt(t)],checked:[2,"checked","checked",N],hideIcon:[2,"hideIcon","hideIcon",N],disabledInteractive:[2,"disabledInteractive","disabledInteractive",N]},outputs:{change:"change",toggleChange:"toggleChange"},exportAs:["matSlideToggle"],features:[$([ii,{provide:te,useExisting:e,multi:!0}]),Ot],ngContentSelectors:Je,decls:13,vars:27,consts:[["switch",""],["mat-internal-form-field","",3,"labelPosition"],["role","switch","type","button",1,"mdc-switch",3,"click","tabIndex","disabled"],[1,"mdc-switch__track"],[1,"mdc-switch__handle-track"],[1,"mdc-switch__handle"],[1,"mdc-switch__shadow"],[1,"mdc-elevation-overlay"],[1,"mdc-switch__ripple"],["mat-ripple","",1,"mat-mdc-slide-toggle-ripple","mat-focus-indicator",3,"matRippleTrigger","matRippleDisabled","matRippleCentered"],[1,"mdc-switch__icons"],[1,"mdc-label",3,"click","for"],["viewBox","0 0 24 24","aria-hidden","true",1,"mdc-switch__icon","mdc-switch__icon--on"],["d","M19.69,5.23L8.96,15.96l-4.23-4.23L2.96,13.5l6,6L21.46,7L19.69,5.23z"],["viewBox","0 0 24 24","aria-hidden","true",1,"mdc-switch__icon","mdc-switch__icon--off"],["d","M20 13H4v-2h16v2z"]],template:function(i,o){if(i&1){let l=I();S(),a(0,"div",1)(1,"button",2,0),M("click",function(){return x(l),k(o._handleClick())}),O(3,"span",3),a(4,"span",4)(5,"span",5)(6,"span",6),O(7,"span",7),r(),a(8,"span",8),O(9,"span",9),r(),b(10,ti,5,0,"span",10),r()()(),a(11,"label",11),M("click",function(Le){return x(l),k(Le.stopPropagation())}),v(12),r()()}if(i&2){let l=U(2);m("labelPosition",o.labelPosition),s(),f("mdc-switch--selected",o.checked)("mdc-switch--unselected",!o.checked)("mdc-switch--checked",o.checked)("mdc-switch--disabled",o.disabled)("mat-mdc-slide-toggle-disabled-interactive",o.disabledInteractive),m("tabIndex",o.disabled&&!o.disabledInteractive?-1:o.tabIndex)("disabled",o.disabled&&!o.disabledInteractive),D("id",o.buttonId)("name",o.name)("aria-label",o.ariaLabel)("aria-labelledby",o._getAriaLabelledBy())("aria-describedby",o.ariaDescribedby)("aria-required",o.required||null)("aria-checked",o.checked)("aria-disabled",o.disabled&&o.disabledInteractive?"true":null),s(8),m("matRippleTrigger",l)("matRippleDisabled",o.disableRipple||o.disabled)("matRippleCentered",!0),s(),et(o.hideIcon?-1:10),s(),m("for",o.buttonId),D("id",o._labelId)}},dependencies:[me,xe],styles:[`.mdc-switch{align-items:center;background:none;border:none;cursor:pointer;display:inline-flex;flex-shrink:0;margin:0;outline:none;overflow:visible;padding:0;position:relative;width:var(--mdc-switch-track-width, 52px)}.mdc-switch.mdc-switch--disabled{cursor:default;pointer-events:none}.mdc-switch.mat-mdc-slide-toggle-disabled-interactive{pointer-events:auto}.mdc-switch__track{overflow:hidden;position:relative;width:100%;height:var(--mdc-switch-track-height, 32px);border-radius:var(--mdc-switch-track-shape, var(--mat-sys-corner-full))}.mdc-switch--disabled.mdc-switch .mdc-switch__track{opacity:var(--mdc-switch-disabled-track-opacity, 0.12)}.mdc-switch__track::before,.mdc-switch__track::after{border:1px solid rgba(0,0,0,0);border-radius:inherit;box-sizing:border-box;content:"";height:100%;left:0;position:absolute;width:100%;border-width:var(--mat-switch-track-outline-width, 2px);border-color:var(--mat-switch-track-outline-color, var(--mat-sys-outline))}.mdc-switch--selected .mdc-switch__track::before,.mdc-switch--selected .mdc-switch__track::after{border-width:var(--mat-switch-selected-track-outline-width, 2px);border-color:var(--mat-switch-selected-track-outline-color, transparent)}.mdc-switch--disabled .mdc-switch__track::before,.mdc-switch--disabled .mdc-switch__track::after{border-width:var(--mat-switch-disabled-unselected-track-outline-width, 2px);border-color:var(--mat-switch-disabled-unselected-track-outline-color, var(--mat-sys-on-surface))}@media(forced-colors: active){.mdc-switch__track{border-color:currentColor}}.mdc-switch__track::before{transition:transform 75ms 0ms cubic-bezier(0, 0, 0.2, 1);transform:translateX(0);background:var(--mdc-switch-unselected-track-color, var(--mat-sys-surface-variant))}.mdc-switch--selected .mdc-switch__track::before{transition:transform 75ms 0ms cubic-bezier(0.4, 0, 0.6, 1);transform:translateX(100%)}[dir=rtl] .mdc-switch--selected .mdc-switch--selected .mdc-switch__track::before{transform:translateX(-100%)}.mdc-switch--selected .mdc-switch__track::before{opacity:var(--mat-switch-hidden-track-opacity, 0);transition:var(--mat-switch-hidden-track-transition, opacity 75ms)}.mdc-switch--unselected .mdc-switch__track::before{opacity:var(--mat-switch-visible-track-opacity, 1);transition:var(--mat-switch-visible-track-transition, opacity 75ms)}.mdc-switch:enabled:hover:not(:focus):not(:active) .mdc-switch__track::before{background:var(--mdc-switch-unselected-hover-track-color, var(--mat-sys-surface-variant))}.mdc-switch:enabled:focus:not(:active) .mdc-switch__track::before{background:var(--mdc-switch-unselected-focus-track-color, var(--mat-sys-surface-variant))}.mdc-switch:enabled:active .mdc-switch__track::before{background:var(--mdc-switch-unselected-pressed-track-color, var(--mat-sys-surface-variant))}.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:hover:not(:focus):not(:active) .mdc-switch__track::before,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:focus:not(:active) .mdc-switch__track::before,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:active .mdc-switch__track::before,.mdc-switch.mdc-switch--disabled .mdc-switch__track::before{background:var(--mdc-switch-disabled-unselected-track-color, var(--mat-sys-surface-variant))}.mdc-switch__track::after{transform:translateX(-100%);background:var(--mdc-switch-selected-track-color, var(--mat-sys-primary))}[dir=rtl] .mdc-switch__track::after{transform:translateX(100%)}.mdc-switch--selected .mdc-switch__track::after{transform:translateX(0)}.mdc-switch--selected .mdc-switch__track::after{opacity:var(--mat-switch-visible-track-opacity, 1);transition:var(--mat-switch-visible-track-transition, opacity 75ms)}.mdc-switch--unselected .mdc-switch__track::after{opacity:var(--mat-switch-hidden-track-opacity, 0);transition:var(--mat-switch-hidden-track-transition, opacity 75ms)}.mdc-switch:enabled:hover:not(:focus):not(:active) .mdc-switch__track::after{background:var(--mdc-switch-selected-hover-track-color, var(--mat-sys-primary))}.mdc-switch:enabled:focus:not(:active) .mdc-switch__track::after{background:var(--mdc-switch-selected-focus-track-color, var(--mat-sys-primary))}.mdc-switch:enabled:active .mdc-switch__track::after{background:var(--mdc-switch-selected-pressed-track-color, var(--mat-sys-primary))}.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:hover:not(:focus):not(:active) .mdc-switch__track::after,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:focus:not(:active) .mdc-switch__track::after,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:active .mdc-switch__track::after,.mdc-switch.mdc-switch--disabled .mdc-switch__track::after{background:var(--mdc-switch-disabled-selected-track-color, var(--mat-sys-on-surface))}.mdc-switch__handle-track{height:100%;pointer-events:none;position:absolute;top:0;transition:transform 75ms 0ms cubic-bezier(0.4, 0, 0.2, 1);left:0;right:auto;transform:translateX(0);width:calc(100% - var(--mdc-switch-handle-width))}[dir=rtl] .mdc-switch__handle-track{left:auto;right:0}.mdc-switch--selected .mdc-switch__handle-track{transform:translateX(100%)}[dir=rtl] .mdc-switch--selected .mdc-switch__handle-track{transform:translateX(-100%)}.mdc-switch__handle{display:flex;pointer-events:auto;position:absolute;top:50%;transform:translateY(-50%);left:0;right:auto;transition:width 75ms cubic-bezier(0.4, 0, 0.2, 1),height 75ms cubic-bezier(0.4, 0, 0.2, 1),margin 75ms cubic-bezier(0.4, 0, 0.2, 1);width:var(--mdc-switch-handle-width);height:var(--mdc-switch-handle-height);border-radius:var(--mdc-switch-handle-shape, var(--mat-sys-corner-full))}[dir=rtl] .mdc-switch__handle{left:auto;right:0}.mat-mdc-slide-toggle .mdc-switch--unselected .mdc-switch__handle{width:var(--mat-switch-unselected-handle-size, 16px);height:var(--mat-switch-unselected-handle-size, 16px);margin:var(--mat-switch-unselected-handle-horizontal-margin, 0 8px)}.mat-mdc-slide-toggle .mdc-switch--unselected .mdc-switch__handle:has(.mdc-switch__icons){margin:var(--mat-switch-unselected-with-icon-handle-horizontal-margin, 0 4px)}.mat-mdc-slide-toggle .mdc-switch--selected .mdc-switch__handle{width:var(--mat-switch-selected-handle-size, 24px);height:var(--mat-switch-selected-handle-size, 24px);margin:var(--mat-switch-selected-handle-horizontal-margin, 0 24px)}.mat-mdc-slide-toggle .mdc-switch--selected .mdc-switch__handle:has(.mdc-switch__icons){margin:var(--mat-switch-selected-with-icon-handle-horizontal-margin, 0 24px)}.mat-mdc-slide-toggle .mdc-switch__handle:has(.mdc-switch__icons){width:var(--mat-switch-with-icon-handle-size, 24px);height:var(--mat-switch-with-icon-handle-size, 24px)}.mat-mdc-slide-toggle .mdc-switch:active:not(.mdc-switch--disabled) .mdc-switch__handle{width:var(--mat-switch-pressed-handle-size, 28px);height:var(--mat-switch-pressed-handle-size, 28px)}.mat-mdc-slide-toggle .mdc-switch--selected:active:not(.mdc-switch--disabled) .mdc-switch__handle{margin:var(--mat-switch-selected-pressed-handle-horizontal-margin, 0 22px)}.mat-mdc-slide-toggle .mdc-switch--unselected:active:not(.mdc-switch--disabled) .mdc-switch__handle{margin:var(--mat-switch-unselected-pressed-handle-horizontal-margin, 0 2px)}.mdc-switch--disabled.mdc-switch--selected .mdc-switch__handle::after{opacity:var(--mat-switch-disabled-selected-handle-opacity, 1)}.mdc-switch--disabled.mdc-switch--unselected .mdc-switch__handle::after{opacity:var(--mat-switch-disabled-unselected-handle-opacity, 0.38)}.mdc-switch__handle::before,.mdc-switch__handle::after{border:1px solid rgba(0,0,0,0);border-radius:inherit;box-sizing:border-box;content:"";width:100%;height:100%;left:0;position:absolute;top:0;transition:background-color 75ms 0ms cubic-bezier(0.4, 0, 0.2, 1),border-color 75ms 0ms cubic-bezier(0.4, 0, 0.2, 1);z-index:-1}@media(forced-colors: active){.mdc-switch__handle::before,.mdc-switch__handle::after{border-color:currentColor}}.mdc-switch--selected:enabled .mdc-switch__handle::after{background:var(--mdc-switch-selected-handle-color, var(--mat-sys-on-primary))}.mdc-switch--selected:enabled:hover:not(:focus):not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-selected-hover-handle-color, var(--mat-sys-primary-container))}.mdc-switch--selected:enabled:focus:not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-selected-focus-handle-color, var(--mat-sys-primary-container))}.mdc-switch--selected:enabled:active .mdc-switch__handle::after{background:var(--mdc-switch-selected-pressed-handle-color, var(--mat-sys-primary-container))}.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled.mdc-switch--selected:hover:not(:focus):not(:active) .mdc-switch__handle::after,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled.mdc-switch--selected:focus:not(:active) .mdc-switch__handle::after,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled.mdc-switch--selected:active .mdc-switch__handle::after,.mdc-switch--selected.mdc-switch--disabled .mdc-switch__handle::after{background:var(--mdc-switch-disabled-selected-handle-color, var(--mat-sys-surface))}.mdc-switch--unselected:enabled .mdc-switch__handle::after{background:var(--mdc-switch-unselected-handle-color, var(--mat-sys-outline))}.mdc-switch--unselected:enabled:hover:not(:focus):not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-unselected-hover-handle-color, var(--mat-sys-on-surface-variant))}.mdc-switch--unselected:enabled:focus:not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-unselected-focus-handle-color, var(--mat-sys-on-surface-variant))}.mdc-switch--unselected:enabled:active .mdc-switch__handle::after{background:var(--mdc-switch-unselected-pressed-handle-color, var(--mat-sys-on-surface-variant))}.mdc-switch--unselected.mdc-switch--disabled .mdc-switch__handle::after{background:var(--mdc-switch-disabled-unselected-handle-color, var(--mat-sys-on-surface))}.mdc-switch__handle::before{background:var(--mdc-switch-handle-surface-color)}.mdc-switch__shadow{border-radius:inherit;bottom:0;left:0;position:absolute;right:0;top:0}.mdc-switch:enabled .mdc-switch__shadow{box-shadow:var(--mdc-switch-handle-elevation-shadow)}.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:hover:not(:focus):not(:active) .mdc-switch__shadow,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:focus:not(:active) .mdc-switch__shadow,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:active .mdc-switch__shadow,.mdc-switch.mdc-switch--disabled .mdc-switch__shadow{box-shadow:var(--mdc-switch-disabled-handle-elevation-shadow)}.mdc-switch__ripple{left:50%;position:absolute;top:50%;transform:translate(-50%, -50%);z-index:-1;width:var(--mdc-switch-state-layer-size, 40px);height:var(--mdc-switch-state-layer-size, 40px)}.mdc-switch__ripple::after{content:"";opacity:0}.mdc-switch--disabled .mdc-switch__ripple::after{display:none}.mat-mdc-slide-toggle-disabled-interactive .mdc-switch__ripple::after{display:block}.mdc-switch:hover .mdc-switch__ripple::after{opacity:.04;transition:75ms opacity cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-slide-toggle.mat-mdc-slide-toggle-focused .mdc-switch .mdc-switch__ripple::after{opacity:.12}.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:enabled:focus .mdc-switch__ripple::after,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:enabled:active .mdc-switch__ripple::after,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:enabled:hover:not(:focus) .mdc-switch__ripple::after,.mdc-switch--unselected:enabled:hover:not(:focus) .mdc-switch__ripple::after{background:var(--mdc-switch-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-switch--unselected:enabled:focus .mdc-switch__ripple::after{background:var(--mdc-switch-unselected-focus-state-layer-color, var(--mat-sys-on-surface))}.mdc-switch--unselected:enabled:active .mdc-switch__ripple::after{background:var(--mdc-switch-unselected-pressed-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mdc-switch-unselected-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity));transition:opacity 75ms linear}.mdc-switch--selected:enabled:hover:not(:focus) .mdc-switch__ripple::after{background:var(--mdc-switch-selected-hover-state-layer-color, var(--mat-sys-primary))}.mdc-switch--selected:enabled:focus .mdc-switch__ripple::after{background:var(--mdc-switch-selected-focus-state-layer-color, var(--mat-sys-primary))}.mdc-switch--selected:enabled:active .mdc-switch__ripple::after{background:var(--mdc-switch-selected-pressed-state-layer-color, var(--mat-sys-primary));opacity:var(--mdc-switch-selected-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity));transition:opacity 75ms linear}.mdc-switch__icons{position:relative;height:100%;width:100%;z-index:1;transform:translateZ(0)}.mdc-switch--disabled.mdc-switch--unselected .mdc-switch__icons{opacity:var(--mdc-switch-disabled-unselected-icon-opacity, 0.38)}.mdc-switch--disabled.mdc-switch--selected .mdc-switch__icons{opacity:var(--mdc-switch-disabled-selected-icon-opacity, 0.38)}.mdc-switch__icon{bottom:0;left:0;margin:auto;position:absolute;right:0;top:0;opacity:0;transition:opacity 30ms 0ms cubic-bezier(0.4, 0, 1, 1)}.mdc-switch--unselected .mdc-switch__icon{width:var(--mdc-switch-unselected-icon-size, 16px);height:var(--mdc-switch-unselected-icon-size, 16px);fill:var(--mdc-switch-unselected-icon-color, var(--mat-sys-surface-variant))}.mdc-switch--unselected.mdc-switch--disabled .mdc-switch__icon{fill:var(--mdc-switch-disabled-unselected-icon-color, var(--mat-sys-surface-variant))}.mdc-switch--selected .mdc-switch__icon{width:var(--mdc-switch-selected-icon-size, 16px);height:var(--mdc-switch-selected-icon-size, 16px);fill:var(--mdc-switch-selected-icon-color, var(--mat-sys-on-primary-container))}.mdc-switch--selected.mdc-switch--disabled .mdc-switch__icon{fill:var(--mdc-switch-disabled-selected-icon-color, var(--mat-sys-on-surface))}.mdc-switch--selected .mdc-switch__icon--on,.mdc-switch--unselected .mdc-switch__icon--off{opacity:1;transition:opacity 45ms 30ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-slide-toggle{-webkit-user-select:none;user-select:none;display:inline-block;-webkit-tap-highlight-color:rgba(0,0,0,0);outline:0}.mat-mdc-slide-toggle .mat-mdc-slide-toggle-ripple,.mat-mdc-slide-toggle .mdc-switch__ripple::after{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:50%;pointer-events:none}.mat-mdc-slide-toggle .mat-mdc-slide-toggle-ripple:not(:empty),.mat-mdc-slide-toggle .mdc-switch__ripple::after:not(:empty){transform:translateZ(0)}.mat-mdc-slide-toggle.mat-mdc-slide-toggle-focused .mat-focus-indicator::before{content:""}.mat-mdc-slide-toggle .mat-internal-form-field{color:var(--mat-switch-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-switch-label-text-font, var(--mat-sys-body-medium-font));line-height:var(--mat-switch-label-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-switch-label-text-size, var(--mat-sys-body-medium-size));letter-spacing:var(--mat-switch-label-text-tracking, var(--mat-sys-body-medium-tracking));font-weight:var(--mat-switch-label-text-weight, var(--mat-sys-body-medium-weight))}.mat-mdc-slide-toggle .mat-ripple-element{opacity:.12}.mat-mdc-slide-toggle .mat-focus-indicator::before{border-radius:50%}.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__handle-track,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__icon,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__handle::before,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__handle::after,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__track::before,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__track::after{transition:none}.mat-mdc-slide-toggle .mdc-switch:enabled+.mdc-label{cursor:pointer}.mat-mdc-slide-toggle .mdc-switch--disabled+label{color:var(--mdc-switch-disabled-label-text-color)}
`],encapsulation:2,changeDetection:0})}return e})();var ke=(()=>{class e{static \u0275fac=function(i){return new(i||e)};static \u0275mod=u({type:e});static \u0275inj=_({imports:[vt,p,p]})}return e})();var ri=new A("mat-menu-scroll-strategy",{providedIn:"root",factory:()=>{let e=d(W);return()=>e.scrollStrategies.reposition()}});function si(e){return()=>e.scrollStrategies.reposition()}var ci={provide:ri,deps:[W],useFactory:si};var Me=(()=>{class e{static \u0275fac=function(i){return new(i||e)};static \u0275mod=u({type:e});static \u0275inj=_({providers:[ci],imports:[_t,p,gt,H,p]})}return e})(),Ce={transformMenu:{type:7,name:"transformMenu",definitions:[{type:0,name:"void",styles:{type:6,styles:{opacity:0,transform:"scale(0.8)"},offset:null}},{type:1,expr:"void => enter",animation:{type:4,styles:{type:6,styles:{opacity:1,transform:"scale(1)"},offset:null},timings:"120ms cubic-bezier(0, 0, 0.2, 1)"},options:null},{type:1,expr:"* => void",animation:{type:4,styles:{type:6,styles:{opacity:0},offset:null},timings:"100ms 25ms linear"},options:null}],options:{}},fadeInItems:{type:7,name:"fadeInItems",definitions:[{type:0,name:"showing",styles:{type:6,styles:{opacity:1},offset:null}},{type:1,expr:"void => *",animation:[{type:6,styles:{opacity:0},offset:null},{type:4,styles:null,timings:"400ms 100ms cubic-bezier(0.55, 0, 0.55, 0.2)"}],options:null}],options:{}}},Vn=Ce.fadeInItems,Un=Ce.transformMenu;var Te=(()=>{class e{static \u0275fac=function(i){return new(i||e)};static \u0275mod=u({type:e});static \u0275inj=_({imports:[p,p]})}return e})();var Bt=20;var Pe=new A("mat-tooltip-scroll-strategy",{providedIn:"root",factory:()=>{let e=d(W);return()=>e.scrollStrategies.reposition({scrollThrottle:Bt})}});function Oe(e){return()=>e.scrollStrategies.reposition({scrollThrottle:Bt})}var Ie={provide:Pe,deps:[W],useFactory:Oe};var Nt=(()=>{class e{static \u0275fac=function(i){return new(i||e)};static \u0275mod=u({type:e});static \u0275inj=_({providers:[Ie],imports:[ae,gt,p,p,H]})}return e})();var wt=class e{constructor(n){this.authState=n;this.authState.user$.subscribe(t=>{t?this.updateUserProfile(t):this._userProfile$.next(null)})}S3_BASE_URL="https://chattrix-bucket.s3.eu-north-1.amazonaws.com/";_userProfile$=new rt(null);userProfile$=this._userProfile$.asObservable();updateUserProfile(n){let t=Tt(at({},n),{displayName:this.getDisplayName(n),initials:this.getInitials(n),profilePictureUrl:this.getProfilePictureUrl(n)});this._userProfile$.next(t)}get currentProfile(){return this._userProfile$.value}getDisplayName(n){return n.name||n.email||"User"}getInitials(n){let i=(n.name||n.email||"U").split(" ").filter(o=>o.length>0);return i.length>=2?(i[0][0]+i[1][0]).toUpperCase():i.length===1?i[0].substring(0,2).toUpperCase():"U"}getProfilePictureUrl(n){}hasAdminAccess(){return this.userProfile$.pipe(L(n=>n?(Array.isArray(n.role)?n.role:[n.role]).some(i=>i.toLowerCase()==="admin"||i.toLowerCase()==="super admin"||i.toLowerCase()==="superadmin"):!1))}hasRole(n){return this.userProfile$.pipe(L(t=>t?(Array.isArray(t.role)?t.role:[t.role]).some(o=>o.toLowerCase()===n.toLowerCase()):!1))}getUserRoles(){return this.userProfile$.pipe(L(n=>n?Array.isArray(n.role)?n.role:[n.role]:[]))}getRoleDisplay(){return this.userProfile$.pipe(L(n=>n?Array.isArray(n.role)?n.role.join(", "):n.role||"User":"User"))}getS3Url(n){return n?n.startsWith("http")?n:`${this.S3_BASE_URL}${n}`:""}getAvatarUrl(){return this.userProfile$.pipe(L(n=>n?.profilePictureUrl))}static \u0275fac=function(t){return new(t||e)(G(Z))};static \u0275prov=Q({token:e,factory:e.\u0275fac,providedIn:"root"})};var yt=class e{THEME_STORAGE_KEY="chattrix-theme";DEFAULT_THEME="dark";_currentTheme$=new rt(this.DEFAULT_THEME);currentTheme$=this._currentTheme$.asObservable();constructor(){this.initializeTheme()}initializeTheme(){let n=this.getSavedTheme(),t=this.getSystemTheme(),i=n||t||this.DEFAULT_THEME;this.setTheme(i,!1)}getSavedTheme(){try{let n=localStorage.getItem(this.THEME_STORAGE_KEY);return n==="light"||n==="dark"?n:null}catch{return null}}getSystemTheme(){return typeof window<"u"&&window.matchMedia?window.matchMedia("(prefers-color-scheme: light)").matches?"light":"dark":this.DEFAULT_THEME}get currentTheme(){return this._currentTheme$.value}get isDarkMode(){return this.currentTheme==="dark"}get isLightMode(){return this.currentTheme==="light"}setTheme(n,t=!0){this._currentTheme$.next(n),this.applyThemeToDocument(n),t&&this.saveTheme(n)}toggleTheme(){let n=this.isDarkMode?"light":"dark";this.setTheme(n)}applyThemeToDocument(n){if(typeof document<"u"){let t=document.body;t.classList.remove("light-theme","dark-theme"),t.classList.add(`${n}-theme`),this.updateCSSVariables(n)}}updateCSSVariables(n){if(typeof document<"u"){let t=document.documentElement;n==="light"?(t.style.setProperty("--primary-dark","#ffffff"),t.style.setProperty("--primary-darker","#f8f9fa"),t.style.setProperty("--secondary-dark","#ffffff"),t.style.setProperty("--text-primary","#000000"),t.style.setProperty("--text-secondary","#6c757d"),t.style.setProperty("--text-muted","#adb5bd"),t.style.setProperty("--bg-primary","#ffffff"),t.style.setProperty("--bg-secondary","#f8f9fa"),t.style.setProperty("--bg-tertiary","#e9ecef"),t.style.setProperty("--bg-card","#ffffff"),t.style.setProperty("--border-primary","#dee2e6"),t.style.setProperty("--border-secondary","#adb5bd"),t.style.setProperty("--mdc-theme-surface","#ffffff"),t.style.setProperty("--mdc-theme-background","#fafafa"),t.style.setProperty("--mdc-theme-on-surface","#000000"),t.style.setProperty("--mdc-theme-on-background","#000000")):(t.style.setProperty("--primary-dark","#0f172a"),t.style.setProperty("--primary-darker","#020617"),t.style.setProperty("--secondary-dark","#1e293b"),t.style.setProperty("--text-primary","#ffffff"),t.style.setProperty("--text-secondary","#94a3b8"),t.style.setProperty("--text-muted","#64748b"),t.style.setProperty("--bg-primary","#0f172a"),t.style.setProperty("--bg-secondary","#1e293b"),t.style.setProperty("--bg-tertiary","#334155"),t.style.setProperty("--bg-card","#1e293b"),t.style.setProperty("--border-primary","#334155"),t.style.setProperty("--border-secondary","#475569"),t.style.setProperty("--mdc-theme-surface","#1e293b"),t.style.setProperty("--mdc-theme-background","#0f172a"),t.style.setProperty("--mdc-theme-on-surface","#ffffff"),t.style.setProperty("--mdc-theme-on-background","#ffffff"))}}saveTheme(n){try{localStorage.setItem(this.THEME_STORAGE_KEY,n)}catch(t){console.warn("Failed to save theme to localStorage:",t)}}listenToSystemThemeChanges(){typeof window<"u"&&window.matchMedia&&window.matchMedia("(prefers-color-scheme: light)").addEventListener("change",t=>{if(!this.getSavedTheme()){let i=t.matches?"light":"dark";this.setTheme(i,!1)}})}resetToSystemTheme(){try{localStorage.removeItem(this.THEME_STORAGE_KEY)}catch{}let n=this.getSystemTheme();this.setTheme(n,!1)}static \u0275fac=function(t){return new(t||e)};static \u0275prov=Q({token:e,factory:e.\u0275fac,providedIn:"root"})};function pi(e,n){if(e&1){let t=I();a(0,"img",30,1),M("error",function(){x(t);let o=U(1);return k(o.style.display="none")}),r()}if(e&2){let t=h().ngIf,i=h();m("src",t.profilePictureUrl,Gt)("alt",i.getUserDisplayName(t))}}function _i(e,n){if(e&1&&(a(0,"span",31),c(1),r()),e&2){let t=h().ngIf,i=h();s(),it(" ",i.getUserInitials(t)," ")}}function ui(e,n){if(e&1&&(a(0,"p",32),c(1),r()),e&2){let t=h().ngIf;s(),it(" ",t.phoneNumber," ")}}function gi(e,n){if(e&1&&(a(0,"p",33),c(1),r()),e&2){let t=h().ngIf;s(),it(" ",t.description," ")}}function fi(e,n){if(e&1&&(a(0,"span",39),c(1),r()),e&2){let t=h(2).$implicit;s(),it(" ",t.badge," ")}}function bi(e,n){if(e&1){let t=I();a(0,"mat-list-item",35),M("click",function(){x(t);let o=h().$implicit,l=h(2);return k(l.onNavItemClick(o))}),a(1,"mat-icon",36),c(2),r(),a(3,"span",37),c(4),r(),b(5,fi,2,1,"span",38),r()}if(e&2){let t=h().$implicit,i=h(2);f("active",i.router.url===t.route),s(2),B(t.icon),s(2),B(t.label),s(),m("ngIf",t.badge&&t.badge>0)}}function vi(e,n){if(e&1&&(Yt(0),b(1,bi,6,5,"mat-list-item",34),Dt(2,"async"),$t()),e&2){let t=n.$implicit,i=h(2);s(),m("ngIf",i.shouldShowNavItem(t,St(2,1,i.hasAdminAccess$)||!1))}}function wi(e,n){if(e&1){let t=I();a(0,"div",3)(1,"div",4)(2,"div",5)(3,"div",6)(4,"img",7,0),M("error",function(){x(t);let o=U(5);return k(o.style.display="none")}),r(),a(6,"span",8),c(7,"C"),r()(),a(8,"h2",9),c(9,"Chattrix"),r()(),a(10,"mat-card",10)(11,"div",11)(12,"div",12),b(13,pi,2,2,"img",13)(14,_i,2,1,"span",14),r(),a(15,"div",15)(16,"h3",16),c(17),r(),b(18,ui,2,1,"p",17),a(19,"p",18),c(20),r(),a(21,"p",19),c(22),r(),b(23,gi,2,1,"p",20),r()()()(),a(24,"div",21)(25,"mat-nav-list",22),b(26,vi,3,3,"ng-container",23),r()(),a(27,"div",24),O(28,"mat-divider"),a(29,"div",25)(30,"mat-icon",26),c(31),r(),a(32,"span",27),c(33,"Dark Mode"),r(),a(34,"mat-slide-toggle",28),M("change",function(){x(t);let o=h();return k(o.toggleTheme())}),r()(),a(35,"button",29),M("click",function(){x(t);let o=h();return k(o.onLogout())}),a(36,"mat-icon"),c(37,"logout"),r(),a(38,"span"),c(39,"Logout"),r()()()()}if(e&2){let t=n.ngIf,i=U(5),o=h();s(6),It("display",i.style.display==="none"?"flex":"none"),s(6),f("has-image",t.profilePictureUrl),s(),m("ngIf",t.profilePictureUrl),s(),m("ngIf",!t.profilePictureUrl),s(3),B(o.getUserDisplayName(t)),s(),m("ngIf",t.phoneNumber),s(2),B(t.email),s(2),B(o.getUserRole(t)),s(),m("ngIf",t.description),s(3),m("ngForOf",o.navigationItems),s(5),B(o.isDarkMode?"light_mode":"dark_mode"),s(3),m("checked",o.isDarkMode)}}var xt=class e{constructor(n,t,i,o){this.userProfileService=n;this.themeService=t;this.authService=i;this.router=o;this.userProfile$=this.userProfileService.userProfile$,this.hasAdminAccess$=this.userProfileService.hasAdminAccess(),this.currentTheme$=this.themeService.currentTheme$}destroy$=new J;userProfile$;hasAdminAccess$;currentTheme$;navigationItems=[{label:"Messages",icon:"message",route:"/dashboard",badge:0},{label:"User Management",icon:"people",route:"/dashboard/users",requiresAdmin:!0}];ngOnInit(){this.themeService.listenToSystemThemeChanges()}ngOnDestroy(){this.destroy$.next(),this.destroy$.complete()}navigateTo(n){this.router.navigate([n])}toggleTheme(){this.themeService.toggleTheme()}get isDarkMode(){return this.themeService.isDarkMode}onLogout(){this.authService.logout()}getUserInitials(n){return n?.initials||"U"}getUserDisplayName(n){return n?.displayName||"User"}getUserRole(n){return n?Array.isArray(n.role)?n.role.join(", "):n.role||"User":"User"}shouldShowNavItem(n,t){return n.requiresAdmin?t:!0}onNavItemClick(n){n.action?n.action():n.route&&this.navigateTo(n.route)}static \u0275fac=function(t){return new(t||e)(E(wt),E(yt),E(K),E(q))};static \u0275cmp=g({type:e,selectors:[["app-side-bar"]],standalone:!1,decls:2,vars:3,consts:[["logoImg",""],["avatarImg",""],["class","sidebar-container",4,"ngIf"],[1,"sidebar-container"],[1,"user-profile-section"],[1,"profile-header"],[1,"app-logo"],["src","logo/logo2.png","alt","Chattrix Logo",1,"logo-image",3,"error"],[1,"logo-fallback"],[1,"app-name"],[1,"user-info-card"],[1,"user-avatar-section"],[1,"user-avatar"],["class","avatar-image",3,"src","alt","error",4,"ngIf"],["class","avatar-initials",4,"ngIf"],[1,"user-details"],[1,"user-name"],["class","user-phone",4,"ngIf"],[1,"user-email"],[1,"user-role"],["class","user-description",4,"ngIf"],[1,"navigation-section"],[1,"nav-list"],[4,"ngFor","ngForOf"],[1,"settings-section"],[1,"setting-item"],[1,"setting-icon"],[1,"setting-label"],["color","primary",1,"theme-toggle",3,"change","checked"],["mat-stroked-button","","color","warn",1,"logout-button",3,"click"],[1,"avatar-image",3,"error","src","alt"],[1,"avatar-initials"],[1,"user-phone"],[1,"user-description"],["class","nav-item","matRipple","",3,"active","click",4,"ngIf"],["matRipple","",1,"nav-item",3,"click"],["matListItemIcon","",1,"nav-icon"],["matListItemTitle","",1,"nav-label"],["class","nav-badge","matListItemMeta","",4,"ngIf"],["matListItemMeta","",1,"nav-badge"]],template:function(t,i){t&1&&(b(0,wi,40,14,"div",2),Dt(1,"async")),t&2&&m("ngIf",St(1,1,i.userProfile$))},dependencies:[Wt,mt,we,ve,zt,ue,Lt,Ft,ut,he,se,vt,Zt],styles:[".sidebar-container[_ngcontent-%COMP%]{width:280px;height:100vh;background:var(--bg-secondary);border-right:1px solid var(--border-primary);display:flex;flex-direction:column;overflow-y:auto;padding:var(--spacing-md);box-sizing:border-box}.user-profile-section[_ngcontent-%COMP%]{margin-bottom:var(--spacing-lg)}.profile-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spacing-sm);margin-bottom:var(--spacing-md);padding:var(--spacing-sm) 0}.app-logo[_ngcontent-%COMP%]{position:relative;width:40px;height:40px;display:flex;align-items:center;justify-content:center}.logo-image[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:contain;border-radius:8px}.logo-fallback[_ngcontent-%COMP%]{width:40px;height:40px;background:var(--accent-green);color:#fff;border-radius:8px;display:flex;align-items:center;justify-content:center;font-weight:600;font-size:18px}.app-name[_ngcontent-%COMP%]{color:var(--text-primary);font-size:1.5rem;font-weight:600;margin:0}.user-info-card[_ngcontent-%COMP%]{background:var(--bg-card);border:1px solid var(--border-primary);border-radius:var(--radius-lg);padding:var(--spacing-md);box-shadow:var(--shadow-sm)}.user-avatar-section[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;text-align:center;gap:var(--spacing-sm)}.user-avatar[_ngcontent-%COMP%]{width:80px;height:80px;border-radius:50%;display:flex;align-items:center;justify-content:center;background:var(--bg-tertiary);border:3px solid var(--border-primary);overflow:hidden;margin-bottom:var(--spacing-sm)}.user-avatar.has-image[_ngcontent-%COMP%]{border-color:var(--accent-green)}.avatar-image[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover}.avatar-initials[_ngcontent-%COMP%]{font-size:1.5rem;font-weight:600;color:var(--text-primary)}.user-details[_ngcontent-%COMP%]{width:100%}.user-name[_ngcontent-%COMP%]{color:var(--text-primary);font-size:1.1rem;font-weight:600;margin:0 0 var(--spacing-xs) 0}.user-phone[_ngcontent-%COMP%], .user-email[_ngcontent-%COMP%], .user-role[_ngcontent-%COMP%], .user-description[_ngcontent-%COMP%]{color:var(--text-secondary);font-size:.875rem;margin:var(--spacing-xs) 0;line-height:1.4}.user-phone[_ngcontent-%COMP%]{font-weight:500}.user-description[_ngcontent-%COMP%]{font-style:italic;color:var(--text-muted)}.navigation-section[_ngcontent-%COMP%]{flex:1;margin-bottom:var(--spacing-lg)}.nav-list[_ngcontent-%COMP%]{padding:0}.nav-item[_ngcontent-%COMP%]{border-radius:var(--radius-md);margin-bottom:var(--spacing-xs);cursor:pointer;transition:all .2s ease;color:var(--text-secondary)}.nav-item[_ngcontent-%COMP%]:hover{background:var(--bg-hover);color:var(--text-primary)}.nav-item.active[_ngcontent-%COMP%]{background:var(--accent-green);color:#fff}.nav-item.active[_ngcontent-%COMP%]   .nav-icon[_ngcontent-%COMP%]{color:#fff}.nav-item[_ngcontent-%COMP%]   .nav-icon[_ngcontent-%COMP%]{color:var(--text-secondary);margin-right:var(--spacing-sm);transition:color .2s ease}.nav-item[_ngcontent-%COMP%]   .nav-label[_ngcontent-%COMP%]{font-weight:500;font-size:.95rem}.nav-item[_ngcontent-%COMP%]   .nav-badge[_ngcontent-%COMP%]{background:var(--accent-green);color:#fff;border-radius:12px;padding:2px 8px;font-size:.75rem;font-weight:600;min-width:20px;text-align:center}.settings-section[_ngcontent-%COMP%]{margin-top:auto;padding-top:var(--spacing-md)}.setting-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spacing-sm);padding:var(--spacing-sm) 0;margin-bottom:var(--spacing-sm)}.setting-icon[_ngcontent-%COMP%]{color:var(--text-secondary);font-size:20px;width:20px;height:20px}.setting-label[_ngcontent-%COMP%]{flex:1;color:var(--text-primary);font-weight:500;font-size:.95rem}.theme-toggle[_ngcontent-%COMP%]     .mat-slide-toggle-bar{background-color:var(--bg-tertiary)}.theme-toggle[_ngcontent-%COMP%]     .mat-slide-toggle-thumb{background-color:var(--text-secondary)}.theme-toggle.mat-checked[_ngcontent-%COMP%]     .mat-slide-toggle-bar{background-color:var(--accent-green)}.theme-toggle.mat-checked[_ngcontent-%COMP%]     .mat-slide-toggle-thumb{background-color:#fff}.logout-button[_ngcontent-%COMP%]{width:100%;margin-top:var(--spacing-md);border:1px solid var(--error);color:var(--error);background:transparent;border-radius:var(--radius-md);padding:var(--spacing-sm) var(--spacing-md);font-weight:500;transition:all .2s ease}.logout-button[_ngcontent-%COMP%]:hover{background:var(--error);color:#fff}.logout-button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-right:var(--spacing-xs);font-size:18px;width:18px;height:18px}@media (max-width: 768px){.sidebar-container[_ngcontent-%COMP%]{width:100%;height:auto;min-height:100vh}.user-info-card[_ngcontent-%COMP%]{margin-bottom:var(--spacing-md)}.user-avatar[_ngcontent-%COMP%]{width:60px;height:60px}.app-name[_ngcontent-%COMP%]{font-size:1.25rem}}.light-theme[_nghost-%COMP%]   .sidebar-container[_ngcontent-%COMP%], .light-theme   [_nghost-%COMP%]   .sidebar-container[_ngcontent-%COMP%]{background:#fff;border-right-color:#e0e0e0}.light-theme[_nghost-%COMP%]   .user-info-card[_ngcontent-%COMP%], .light-theme   [_nghost-%COMP%]   .user-info-card[_ngcontent-%COMP%]{background:#fff;border-color:#e0e0e0;box-shadow:0 2px 4px #0000001a}.light-theme[_nghost-%COMP%]   .nav-item[_ngcontent-%COMP%], .light-theme   [_nghost-%COMP%]   .nav-item[_ngcontent-%COMP%]{color:#666}.light-theme[_nghost-%COMP%]   .nav-item[_ngcontent-%COMP%]:hover, .light-theme   [_nghost-%COMP%]   .nav-item[_ngcontent-%COMP%]:hover{background:#f5f5f5;color:#333}.light-theme[_nghost-%COMP%]   .nav-item.active[_ngcontent-%COMP%], .light-theme   [_nghost-%COMP%]   .nav-item.active[_ngcontent-%COMP%]{background:var(--accent-green);color:#fff}.light-theme[_nghost-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-icon[_ngcontent-%COMP%], .light-theme   [_nghost-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-icon[_ngcontent-%COMP%]{color:#666}.light-theme[_nghost-%COMP%]   .setting-icon[_ngcontent-%COMP%], .light-theme   [_nghost-%COMP%]   .setting-icon[_ngcontent-%COMP%]{color:#666}.light-theme[_nghost-%COMP%]   .setting-label[_ngcontent-%COMP%], .light-theme   [_nghost-%COMP%]   .setting-label[_ngcontent-%COMP%]{color:#333}.light-theme[_nghost-%COMP%]   .app-name[_ngcontent-%COMP%], .light-theme   [_nghost-%COMP%]   .app-name[_ngcontent-%COMP%]{color:#333}.light-theme[_nghost-%COMP%]   .user-name[_ngcontent-%COMP%], .light-theme   [_nghost-%COMP%]   .user-name[_ngcontent-%COMP%]{color:#333}.light-theme[_nghost-%COMP%]   .user-phone[_ngcontent-%COMP%], .light-theme   [_nghost-%COMP%]   .user-phone[_ngcontent-%COMP%], .light-theme[_nghost-%COMP%]   .user-email[_ngcontent-%COMP%], .light-theme   [_nghost-%COMP%]   .user-email[_ngcontent-%COMP%], .light-theme[_nghost-%COMP%]   .user-role[_ngcontent-%COMP%], .light-theme   [_nghost-%COMP%]   .user-role[_ngcontent-%COMP%]{color:#666}.light-theme[_nghost-%COMP%]   .user-description[_ngcontent-%COMP%], .light-theme   [_nghost-%COMP%]   .user-description[_ngcontent-%COMP%]{color:#999}.light-theme[_nghost-%COMP%]   .avatar-initials[_ngcontent-%COMP%], .light-theme   [_nghost-%COMP%]   .avatar-initials[_ngcontent-%COMP%]{color:#333}"]})};function xi(e,n){e&1&&(a(0,"div",3),O(1,"app-side-bar",4),a(2,"div",5)(3,"div",6)(4,"div",7)(5,"mat-icon",8),c(6,"chat"),r(),a(7,"h2"),c(8,"Welcome to Chattrix"),r(),a(9,"p"),c(10," Chat window and user list will be implemented in the next iteration. "),r(),a(11,"p"),c(12,"Use the sidebar to navigate to User Management or other features."),r()()()()())}function ki(e,n){e&1&&(a(0,"div",9)(1,"div",10)(2,"mat-icon",11),c(3,"hourglass_empty"),r(),a(4,"p",12),c(5,"Loading dashboard..."),r()()())}function Mi(e,n){e&1&&(a(0,"div",13)(1,"div",14)(2,"mat-icon",15),c(3,"lock"),r(),a(4,"h2"),c(5,"Access Denied"),r(),a(6,"p"),c(7,"You need to be logged in to access the dashboard."),r(),a(8,"p"),c(9,"Redirecting to login..."),r()()())}var kt=class e{constructor(n,t,i){this.authService=n;this.authState=t;this.router=i}destroy$=new J;user=null;isLoading=!1;isAuthenticated=!1;ngOnInit(){this.authState.authState$.pipe(Ht(this.destroy$)).subscribe(n=>{this.isAuthenticated=n.isAuthenticated,this.isLoading=n.isLoading,this.user=n.user,n.isAuthenticated||this.router.navigate(["/auth/login"])})}ngOnDestroy(){this.destroy$.next(),this.destroy$.complete()}onLogout(){this.authService.logout()}get userDisplayName(){return this.user&&(this.user.name||this.user.email)||"User"}get userRoleDisplay(){return this.user?.role?Array.isArray(this.user.role)?this.user.role.join(", "):this.user.role:"User"}static \u0275fac=function(t){return new(t||e)(E(K),E(Z),E(q))};static \u0275cmp=g({type:e,selectors:[["app-dashboard"]],standalone:!1,decls:3,vars:3,consts:[["class","dashboard-container",4,"ngIf"],["class","loading-container",4,"ngIf"],["class","not-authenticated",4,"ngIf"],[1,"dashboard-container"],[1,"sidebar"],[1,"main-content"],[1,"content-placeholder"],[1,"placeholder-content"],[1,"placeholder-icon"],[1,"loading-container"],[1,"loading-content"],[1,"loading-icon"],[1,"loading-text"],[1,"not-authenticated"],[1,"error-content"],[1,"error-icon"]],template:function(t,i){t&1&&b(0,xi,13,0,"div",0)(1,ki,6,0,"div",1)(2,Mi,10,0,"div",2),t&2&&(m("ngIf",!i.isLoading&&i.isAuthenticated),s(),m("ngIf",i.isLoading),s(),m("ngIf",!i.isLoading&&!i.isAuthenticated))},dependencies:[mt,ut,xt],styles:[".dashboard-container[_ngcontent-%COMP%]{display:flex;height:100vh;background:var(--bg-primary);overflow:hidden}.sidebar[_ngcontent-%COMP%]{flex-shrink:0}.main-content[_ngcontent-%COMP%]{flex:1;display:flex;flex-direction:column;overflow:hidden;background:var(--bg-primary)}.content-placeholder[_ngcontent-%COMP%]{flex:1;display:flex;align-items:center;justify-content:center;padding:var(--spacing-xl)}.placeholder-content[_ngcontent-%COMP%]{text-align:center;color:var(--text-secondary);max-width:400px}.placeholder-icon[_ngcontent-%COMP%]{font-size:4rem;width:4rem;height:4rem;color:var(--text-muted);margin-bottom:var(--spacing-md)}.placeholder-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{color:var(--text-primary);font-size:1.5rem;font-weight:600;margin:0 0 var(--spacing-md) 0}.placeholder-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:var(--text-secondary);font-size:1rem;line-height:1.5;margin:var(--spacing-sm) 0}.loading-container[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;min-height:100vh;background:var(--bg-primary)}.loading-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:var(--spacing-md)}.loading-icon[_ngcontent-%COMP%]{font-size:3rem;width:3rem;height:3rem;color:var(--accent-green);animation:_ngcontent-%COMP%_spin 2s linear infinite}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.loading-text[_ngcontent-%COMP%]{color:var(--text-secondary);font-size:var(--font-size-md);font-weight:500;margin:0}.not-authenticated[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;min-height:100vh;background:var(--bg-primary);padding:var(--spacing-lg)}.error-content[_ngcontent-%COMP%]{text-align:center;color:var(--text-secondary);max-width:400px}.error-icon[_ngcontent-%COMP%]{font-size:4rem;width:4rem;height:4rem;color:var(--error);margin-bottom:var(--spacing-md)}.error-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{color:var(--text-primary);font-size:1.5rem;font-weight:600;margin:0 0 var(--spacing-md) 0}.error-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:var(--text-secondary);font-size:1rem;line-height:1.5;margin:var(--spacing-sm) 0}@media (max-width: 768px){.dashboard-container[_ngcontent-%COMP%]{flex-direction:column;height:auto;min-height:100vh}.sidebar[_ngcontent-%COMP%]{width:100%;height:auto}.main-content[_ngcontent-%COMP%]{flex:1;min-height:calc(100vh - 200px)}.placeholder-icon[_ngcontent-%COMP%]{font-size:3rem;width:3rem;height:3rem}.placeholder-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:1.25rem}.placeholder-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:.9rem}}.light-theme[_nghost-%COMP%]   .dashboard-container[_ngcontent-%COMP%], .light-theme   [_nghost-%COMP%]   .dashboard-container[_ngcontent-%COMP%], .light-theme[_nghost-%COMP%]   .main-content[_ngcontent-%COMP%], .light-theme   [_nghost-%COMP%]   .main-content[_ngcontent-%COMP%], .light-theme[_nghost-%COMP%]   .loading-container[_ngcontent-%COMP%], .light-theme   [_nghost-%COMP%]   .loading-container[_ngcontent-%COMP%], .light-theme[_nghost-%COMP%]   .not-authenticated[_ngcontent-%COMP%], .light-theme   [_nghost-%COMP%]   .not-authenticated[_ngcontent-%COMP%]{background:#fff}.light-theme[_nghost-%COMP%]   .placeholder-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .light-theme   [_nghost-%COMP%]   .placeholder-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{color:#333}.light-theme[_nghost-%COMP%]   .placeholder-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .light-theme   [_nghost-%COMP%]   .placeholder-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666}.light-theme[_nghost-%COMP%]   .placeholder-icon[_ngcontent-%COMP%], .light-theme   [_nghost-%COMP%]   .placeholder-icon[_ngcontent-%COMP%]{color:#999}.light-theme[_nghost-%COMP%]   .loading-text[_ngcontent-%COMP%], .light-theme   [_nghost-%COMP%]   .loading-text[_ngcontent-%COMP%]{color:#666}.light-theme[_nghost-%COMP%]   .error-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .light-theme   [_nghost-%COMP%]   .error-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{color:#333}.light-theme[_nghost-%COMP%]   .error-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .light-theme   [_nghost-%COMP%]   .error-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666}"]})};var Mt=class e{static \u0275fac=function(t){return new(t||e)};static \u0275cmp=g({type:e,selectors:[["app-user-management"]],standalone:!1,decls:2,vars:0,template:function(t,i){t&1&&(a(0,"p"),c(1,"user-management works!"),r())},encapsulation:2})};var ot=class e{constructor(n,t,i){this.authService=n;this.authState=t;this.router=i}canActivate(n,t){return this.authState.authState$.pipe(Ut(1),L(i=>i.isAuthenticated?!0:(this.router.navigate(["/auth/login"],{queryParams:{returnUrl:t.url}}),!1)))}static \u0275fac=function(t){return new(t||e)(G(K),G(Z),G(q))};static \u0275prov=Q({token:e,factory:e.\u0275fac,providedIn:"root"})};var Ci=[{path:"",redirectTo:"dashboard",pathMatch:"full"},{path:"dashboard",component:kt,title:"Dashboard - Chattrix",canActivate:[ot]},{path:"users",component:Mt,title:"User Management - Chattrix",canActivate:[ot]},{path:"**",redirectTo:"dashboard"}],Ct=class e{static \u0275fac=function(t){return new(t||e)};static \u0275mod=u({type:e});static \u0275inj=_({imports:[At.forChild(Ci),At]})};var Re=class e{static \u0275fac=function(t){return new(t||e)};static \u0275mod=u({type:e});static \u0275inj=_({imports:[Kt,Ct,ge,ye,_e,pe,ce,ke,ft,Me,Te,Nt]})};export{Re as ChattrixModule};
