var gv=Object.defineProperty,mv=Object.defineProperties;var vv=Object.getOwnPropertyDescriptors;var Co=Object.getOwnPropertySymbols;var dd=Object.prototype.hasOwnProperty,fd=Object.prototype.propertyIsEnumerable;var ld=(e,t,n)=>t in e?gv(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,D=(e,t)=>{for(var n in t||={})dd.call(t,n)&&ld(e,n,t[n]);if(Co)for(var n of Co(t))fd.call(t,n)&&ld(e,n,t[n]);return e},j=(e,t)=>mv(e,vv(t));var yv=(e,t)=>{var n={};for(var r in e)dd.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&Co)for(var r of Co(e))t.indexOf(r)<0&&fd.call(e,r)&&(n[r]=e[r]);return n};function ya(e,t){return Object.is(e,t)}var X=null,bo=!1,Da=1,pe=Symbol("SIGNAL");function k(e){let t=X;return X=e,t}function Ea(){return X}var Dn={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,kind:"unknown",producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function yr(e){if(bo)throw new Error("");if(X===null)return;X.consumerOnSignalRead(e);let t=X.nextProducerIndex++;if(Ro(X),t<X.producerNode.length&&X.producerNode[t]!==e&&vr(X)){let n=X.producerNode[t];Mo(n,X.producerIndexOfThis[t])}X.producerNode[t]!==e&&(X.producerNode[t]=e,X.producerIndexOfThis[t]=vr(X)?pd(e,X,t):0),X.producerLastReadVersion[t]=e.version}function hd(){Da++}function wa(e){if(!(vr(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===Da)){if(!e.producerMustRecompute(e)&&!_o(e)){va(e);return}e.producerRecomputeValue(e),va(e)}}function Ia(e){if(e.liveConsumerNode===void 0)return;let t=bo;bo=!0;try{for(let n of e.liveConsumerNode)n.dirty||Dv(n)}finally{bo=t}}function Ca(){return X?.consumerAllowSignalWrites!==!1}function Dv(e){e.dirty=!0,Ia(e),e.consumerMarkedDirty?.(e)}function va(e){e.dirty=!1,e.lastCleanEpoch=Da}function Dr(e){return e&&(e.nextProducerIndex=0),k(e)}function To(e,t){if(k(t),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if(vr(e))for(let n=e.nextProducerIndex;n<e.producerNode.length;n++)Mo(e.producerNode[n],e.producerIndexOfThis[n]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function _o(e){Ro(e);for(let t=0;t<e.producerNode.length;t++){let n=e.producerNode[t],r=e.producerLastReadVersion[t];if(r!==n.version||(wa(n),r!==n.version))return!0}return!1}function Er(e){if(Ro(e),vr(e))for(let t=0;t<e.producerNode.length;t++)Mo(e.producerNode[t],e.producerIndexOfThis[t]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function pd(e,t,n){if(gd(e),e.liveConsumerNode.length===0&&md(e))for(let r=0;r<e.producerNode.length;r++)e.producerIndexOfThis[r]=pd(e.producerNode[r],e,r);return e.liveConsumerIndexOfThis.push(n),e.liveConsumerNode.push(t)-1}function Mo(e,t){if(gd(e),e.liveConsumerNode.length===1&&md(e))for(let r=0;r<e.producerNode.length;r++)Mo(e.producerNode[r],e.producerIndexOfThis[r]);let n=e.liveConsumerNode.length-1;if(e.liveConsumerNode[t]=e.liveConsumerNode[n],e.liveConsumerIndexOfThis[t]=e.liveConsumerIndexOfThis[n],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,t<e.liveConsumerNode.length){let r=e.liveConsumerIndexOfThis[t],o=e.liveConsumerNode[t];Ro(o),o.producerIndexOfThis[r]=t}}function vr(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function Ro(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function gd(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function md(e){return e.producerNode!==void 0}function No(e,t){let n=Object.create(Ev);n.computation=e,t!==void 0&&(n.equal=t);let r=()=>{if(wa(n),yr(n),n.value===So)throw n.error;return n.value};return r[pe]=n,r}var ga=Symbol("UNSET"),ma=Symbol("COMPUTING"),So=Symbol("ERRORED"),Ev=j(D({},Dn),{value:ga,dirty:!0,error:null,equal:ya,kind:"computed",producerMustRecompute(e){return e.value===ga||e.value===ma},producerRecomputeValue(e){if(e.value===ma)throw new Error("Detected cycle in computations.");let t=e.value;e.value=ma;let n=Dr(e),r,o=!1;try{r=e.computation(),k(null),o=t!==ga&&t!==So&&r!==So&&e.equal(t,r)}catch(i){r=So,e.error=i}finally{To(e,n)}if(o){e.value=t;return}e.value=r,e.version++}});function wv(){throw new Error}var vd=wv;function yd(e){vd(e)}function ba(e){vd=e}var Iv=null;function Sa(e,t){let n=Object.create(xo);n.value=e,t!==void 0&&(n.equal=t);let r=()=>(yr(n),n.value);return r[pe]=n,r}function wr(e,t){Ca()||yd(e),e.equal(e.value,t)||(e.value=t,Cv(e))}function Ta(e,t){Ca()||yd(e),wr(e,t(e.value))}var xo=j(D({},Dn),{equal:ya,value:void 0,kind:"signal"});function Cv(e){e.version++,hd(),Ia(e),Iv?.()}function _a(e){let t=k(null);try{return e()}finally{k(t)}}var Ma;function Ir(){return Ma}function st(e){let t=Ma;return Ma=e,t}var Ao=Symbol("NotFound");function M(e){return typeof e=="function"}function En(e){let n=e(r=>{Error.call(r),r.stack=new Error().stack});return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}var Oo=En(e=>function(n){e(this),this.message=n?`${n.length} errors occurred during unsubscription:
${n.map((r,o)=>`${o+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=n});function Vt(e,t){if(e){let n=e.indexOf(t);0<=n&&e.splice(n,1)}}var q=class e{constructor(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let t;if(!this.closed){this.closed=!0;let{_parentage:n}=this;if(n)if(this._parentage=null,Array.isArray(n))for(let i of n)i.remove(this);else n.remove(this);let{initialTeardown:r}=this;if(M(r))try{r()}catch(i){t=i instanceof Oo?i.errors:[i]}let{_finalizers:o}=this;if(o){this._finalizers=null;for(let i of o)try{Dd(i)}catch(s){t=t??[],s instanceof Oo?t=[...t,...s.errors]:t.push(s)}}if(t)throw new Oo(t)}}add(t){var n;if(t&&t!==this)if(this.closed)Dd(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=(n=this._finalizers)!==null&&n!==void 0?n:[]).push(t)}}_hasParent(t){let{_parentage:n}=this;return n===t||Array.isArray(n)&&n.includes(t)}_addParent(t){let{_parentage:n}=this;this._parentage=Array.isArray(n)?(n.push(t),n):n?[n,t]:t}_removeParent(t){let{_parentage:n}=this;n===t?this._parentage=null:Array.isArray(n)&&Vt(n,t)}remove(t){let{_finalizers:n}=this;n&&Vt(n,t),t instanceof e&&t._removeParent(this)}};q.EMPTY=(()=>{let e=new q;return e.closed=!0,e})();var Ra=q.EMPTY;function ko(e){return e instanceof q||e&&"closed"in e&&M(e.remove)&&M(e.add)&&M(e.unsubscribe)}function Dd(e){M(e)?e():e.unsubscribe()}var Ae={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var wn={setTimeout(e,t,...n){let{delegate:r}=wn;return r?.setTimeout?r.setTimeout(e,t,...n):setTimeout(e,t,...n)},clearTimeout(e){let{delegate:t}=wn;return(t?.clearTimeout||clearTimeout)(e)},delegate:void 0};function Po(e){wn.setTimeout(()=>{let{onUnhandledError:t}=Ae;if(t)t(e);else throw e})}function Cr(){}var Ed=Na("C",void 0,void 0);function wd(e){return Na("E",void 0,e)}function Id(e){return Na("N",e,void 0)}function Na(e,t,n){return{kind:e,value:t,error:n}}var Ut=null;function In(e){if(Ae.useDeprecatedSynchronousErrorHandling){let t=!Ut;if(t&&(Ut={errorThrown:!1,error:null}),e(),t){let{errorThrown:n,error:r}=Ut;if(Ut=null,n)throw r}}else e()}function Cd(e){Ae.useDeprecatedSynchronousErrorHandling&&Ut&&(Ut.errorThrown=!0,Ut.error=e)}var Bt=class extends q{constructor(t){super(),this.isStopped=!1,t?(this.destination=t,ko(t)&&t.add(this)):this.destination=Rv}static create(t,n,r){return new at(t,n,r)}next(t){this.isStopped?Aa(Id(t),this):this._next(t)}error(t){this.isStopped?Aa(wd(t),this):(this.isStopped=!0,this._error(t))}complete(){this.isStopped?Aa(Ed,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(t){this.destination.next(t)}_error(t){try{this.destination.error(t)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},_v=Function.prototype.bind;function xa(e,t){return _v.call(e,t)}var Oa=class{constructor(t){this.partialObserver=t}next(t){let{partialObserver:n}=this;if(n.next)try{n.next(t)}catch(r){Fo(r)}}error(t){let{partialObserver:n}=this;if(n.error)try{n.error(t)}catch(r){Fo(r)}else Fo(t)}complete(){let{partialObserver:t}=this;if(t.complete)try{t.complete()}catch(n){Fo(n)}}},at=class extends Bt{constructor(t,n,r){super();let o;if(M(t)||!t)o={next:t??void 0,error:n??void 0,complete:r??void 0};else{let i;this&&Ae.useDeprecatedNextContext?(i=Object.create(t),i.unsubscribe=()=>this.unsubscribe(),o={next:t.next&&xa(t.next,i),error:t.error&&xa(t.error,i),complete:t.complete&&xa(t.complete,i)}):o=t}this.destination=new Oa(o)}};function Fo(e){Ae.useDeprecatedSynchronousErrorHandling?Cd(e):Po(e)}function Mv(e){throw e}function Aa(e,t){let{onStoppedNotification:n}=Ae;n&&wn.setTimeout(()=>n(e,t))}var Rv={closed:!0,next:Cr,error:Mv,complete:Cr};var Cn=typeof Symbol=="function"&&Symbol.observable||"@@observable";function ue(e){return e}function ka(...e){return Pa(e)}function Pa(e){return e.length===0?ue:e.length===1?e[0]:function(n){return e.reduce((r,o)=>o(r),n)}}var F=(()=>{class e{constructor(n){n&&(this._subscribe=n)}lift(n){let r=new e;return r.source=this,r.operator=n,r}subscribe(n,r,o){let i=xv(n)?n:new at(n,r,o);return In(()=>{let{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(n){try{return this._subscribe(n)}catch(r){n.error(r)}}forEach(n,r){return r=bd(r),new r((o,i)=>{let s=new at({next:a=>{try{n(a)}catch(c){i(c),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(n){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(n)}[Cn](){return this}pipe(...n){return Pa(n)(this)}toPromise(n){return n=bd(n),new n((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return e.create=t=>new e(t),e})();function bd(e){var t;return(t=e??Ae.Promise)!==null&&t!==void 0?t:Promise}function Nv(e){return e&&M(e.next)&&M(e.error)&&M(e.complete)}function xv(e){return e&&e instanceof Bt||Nv(e)&&ko(e)}function Fa(e){return M(e?.lift)}function T(e){return t=>{if(Fa(t))return t.lift(function(n){try{return e(n,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function S(e,t,n,r,o){return new La(e,t,n,r,o)}var La=class extends Bt{constructor(t,n,r,o,i,s){super(t),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=n?function(a){try{n(a)}catch(c){t.error(c)}}:super._next,this._error=o?function(a){try{o(a)}catch(c){t.error(c)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){t.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:n}=this;super.unsubscribe(),!n&&((t=this.onFinalize)===null||t===void 0||t.call(this))}}};function bn(){return T((e,t)=>{let n=null;e._refCount++;let r=S(t,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount){n=null;return}let o=e._connection,i=n;n=null,o&&(!i||o===i)&&o.unsubscribe(),t.unsubscribe()});e.subscribe(r),r.closed||(n=e.connect())})}var Sn=class extends F{constructor(t,n){super(),this.source=t,this.subjectFactory=n,this._subject=null,this._refCount=0,this._connection=null,Fa(t)&&(this.lift=t.lift)}_subscribe(t){return this.getSubject().subscribe(t)}getSubject(){let t=this._subject;return(!t||t.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:t}=this;this._subject=this._connection=null,t?.unsubscribe()}connect(){let t=this._connection;if(!t){t=this._connection=new q;let n=this.getSubject();t.add(this.source.subscribe(S(n,void 0,()=>{this._teardown(),n.complete()},r=>{this._teardown(),n.error(r)},()=>this._teardown()))),t.closed&&(this._connection=null,t=q.EMPTY)}return t}refCount(){return bn()(this)}};var Sd=En(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var G=(()=>{class e extends F{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(n){let r=new Lo(this,this);return r.operator=n,r}_throwIfClosed(){if(this.closed)throw new Sd}next(n){In(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(n)}})}error(n){In(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=n;let{observers:r}=this;for(;r.length;)r.shift().error(n)}})}complete(){In(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:n}=this;for(;n.length;)n.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var n;return((n=this.observers)===null||n===void 0?void 0:n.length)>0}_trySubscribe(n){return this._throwIfClosed(),super._trySubscribe(n)}_subscribe(n){return this._throwIfClosed(),this._checkFinalizedStatuses(n),this._innerSubscribe(n)}_innerSubscribe(n){let{hasError:r,isStopped:o,observers:i}=this;return r||o?Ra:(this.currentObservers=null,i.push(n),new q(()=>{this.currentObservers=null,Vt(i,n)}))}_checkFinalizedStatuses(n){let{hasError:r,thrownError:o,isStopped:i}=this;r?n.error(o):i&&n.complete()}asObservable(){let n=new F;return n.source=this,n}}return e.create=(t,n)=>new Lo(t,n),e})(),Lo=class extends G{constructor(t,n){super(),this.destination=t,this.source=n}next(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.next)===null||r===void 0||r.call(n,t)}error(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.error)===null||r===void 0||r.call(n,t)}complete(){var t,n;(n=(t=this.destination)===null||t===void 0?void 0:t.complete)===null||n===void 0||n.call(t)}_subscribe(t){var n,r;return(r=(n=this.source)===null||n===void 0?void 0:n.subscribe(t))!==null&&r!==void 0?r:Ra}};var z=class extends G{constructor(t){super(),this._value=t}get value(){return this.getValue()}_subscribe(t){let n=super._subscribe(t);return!n.closed&&t.next(this._value),n}getValue(){let{hasError:t,thrownError:n,_value:r}=this;if(t)throw n;return this._throwIfClosed(),r}next(t){super.next(this._value=t)}};var br={now(){return(br.delegate||Date).now()},delegate:void 0};var jo=class extends G{constructor(t=1/0,n=1/0,r=br){super(),this._bufferSize=t,this._windowTime=n,this._timestampProvider=r,this._buffer=[],this._infiniteTimeWindow=!0,this._infiniteTimeWindow=n===1/0,this._bufferSize=Math.max(1,t),this._windowTime=Math.max(1,n)}next(t){let{isStopped:n,_buffer:r,_infiniteTimeWindow:o,_timestampProvider:i,_windowTime:s}=this;n||(r.push(t),!o&&r.push(i.now()+s)),this._trimBuffer(),super.next(t)}_subscribe(t){this._throwIfClosed(),this._trimBuffer();let n=this._innerSubscribe(t),{_infiniteTimeWindow:r,_buffer:o}=this,i=o.slice();for(let s=0;s<i.length&&!t.closed;s+=r?1:2)t.next(i[s]);return this._checkFinalizedStatuses(t),n}_trimBuffer(){let{_bufferSize:t,_timestampProvider:n,_buffer:r,_infiniteTimeWindow:o}=this,i=(o?1:2)*t;if(t<1/0&&i<r.length&&r.splice(0,r.length-i),!o){let s=n.now(),a=0;for(let c=1;c<r.length&&r[c]<=s;c+=2)a=c;a&&r.splice(0,a+1)}}};var Vo=class extends q{constructor(t,n){super()}schedule(t,n=0){return this}};var Sr={setInterval(e,t,...n){let{delegate:r}=Sr;return r?.setInterval?r.setInterval(e,t,...n):setInterval(e,t,...n)},clearInterval(e){let{delegate:t}=Sr;return(t?.clearInterval||clearInterval)(e)},delegate:void 0};var Uo=class extends Vo{constructor(t,n){super(t,n),this.scheduler=t,this.work=n,this.pending=!1}schedule(t,n=0){var r;if(this.closed)return this;this.state=t;let o=this.id,i=this.scheduler;return o!=null&&(this.id=this.recycleAsyncId(i,o,n)),this.pending=!0,this.delay=n,this.id=(r=this.id)!==null&&r!==void 0?r:this.requestAsyncId(i,this.id,n),this}requestAsyncId(t,n,r=0){return Sr.setInterval(t.flush.bind(t,this),r)}recycleAsyncId(t,n,r=0){if(r!=null&&this.delay===r&&this.pending===!1)return n;n!=null&&Sr.clearInterval(n)}execute(t,n){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;let r=this._execute(t,n);if(r)return r;this.pending===!1&&this.id!=null&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))}_execute(t,n){let r=!1,o;try{this.work(t)}catch(i){r=!0,o=i||new Error("Scheduled action threw falsy error")}if(r)return this.unsubscribe(),o}unsubscribe(){if(!this.closed){let{id:t,scheduler:n}=this,{actions:r}=n;this.work=this.state=this.scheduler=null,this.pending=!1,Vt(r,this),t!=null&&(this.id=this.recycleAsyncId(n,t,null)),this.delay=null,super.unsubscribe()}}};var Tn=class e{constructor(t,n=e.now){this.schedulerActionCtor=t,this.now=n}schedule(t,n=0,r){return new this.schedulerActionCtor(this,t).schedule(r,n)}};Tn.now=br.now;var Bo=class extends Tn{constructor(t,n=Tn.now){super(t,n),this.actions=[],this._active=!1}flush(t){let{actions:n}=this;if(this._active){n.push(t);return}let r;this._active=!0;do if(r=t.execute(t.state,t.delay))break;while(t=n.shift());if(this._active=!1,r){for(;t=n.shift();)t.unsubscribe();throw r}}};var $t=new Bo(Uo),Td=$t;var ie=new F(e=>e.complete());function $o(e){return e&&M(e.schedule)}function ja(e){return e[e.length-1]}function Ho(e){return M(ja(e))?e.pop():void 0}function Ve(e){return $o(ja(e))?e.pop():void 0}function _d(e,t){return typeof ja(e)=="number"?e.pop():t}function Rd(e,t,n,r){function o(i){return i instanceof n?i:new n(function(s){s(i)})}return new(n||(n=Promise))(function(i,s){function a(l){try{u(r.next(l))}catch(d){s(d)}}function c(l){try{u(r.throw(l))}catch(d){s(d)}}function u(l){l.done?i(l.value):o(l.value).then(a,c)}u((r=r.apply(e,t||[])).next())})}function Md(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function Ht(e){return this instanceof Ht?(this.v=e,this):new Ht(e)}function Nd(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(e,t||[]),o,i=[];return o=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),o[Symbol.asyncIterator]=function(){return this},o;function s(f){return function(g){return Promise.resolve(g).then(f,d)}}function a(f,g){r[f]&&(o[f]=function(v){return new Promise(function(I,O){i.push([f,v,I,O])>1||c(f,v)})},g&&(o[f]=g(o[f])))}function c(f,g){try{u(r[f](g))}catch(v){h(i[0][3],v)}}function u(f){f.value instanceof Ht?Promise.resolve(f.value.v).then(l,d):h(i[0][2],f)}function l(f){c("next",f)}function d(f){c("throw",f)}function h(f,g){f(g),i.shift(),i.length&&c(i[0][0],i[0][1])}}function xd(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],n;return t?t.call(e):(e=typeof Md=="function"?Md(e):e[Symbol.iterator](),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(i){n[i]=e[i]&&function(s){return new Promise(function(a,c){s=e[i](s),o(a,c,s.done,s.value)})}}function o(i,s,a,c){Promise.resolve(c).then(function(u){i({value:u,done:a})},s)}}var zo=e=>e&&typeof e.length=="number"&&typeof e!="function";function qo(e){return M(e?.then)}function Go(e){return M(e[Cn])}function Wo(e){return Symbol.asyncIterator&&M(e?.[Symbol.asyncIterator])}function Zo(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function Av(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var Yo=Av();function Qo(e){return M(e?.[Yo])}function Ko(e){return Nd(this,arguments,function*(){let n=e.getReader();try{for(;;){let{value:r,done:o}=yield Ht(n.read());if(o)return yield Ht(void 0);yield yield Ht(r)}}finally{n.releaseLock()}})}function Jo(e){return M(e?.getReader)}function V(e){if(e instanceof F)return e;if(e!=null){if(Go(e))return Ov(e);if(zo(e))return kv(e);if(qo(e))return Pv(e);if(Wo(e))return Ad(e);if(Qo(e))return Fv(e);if(Jo(e))return Lv(e)}throw Zo(e)}function Ov(e){return new F(t=>{let n=e[Cn]();if(M(n.subscribe))return n.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function kv(e){return new F(t=>{for(let n=0;n<e.length&&!t.closed;n++)t.next(e[n]);t.complete()})}function Pv(e){return new F(t=>{e.then(n=>{t.closed||(t.next(n),t.complete())},n=>t.error(n)).then(null,Po)})}function Fv(e){return new F(t=>{for(let n of e)if(t.next(n),t.closed)return;t.complete()})}function Ad(e){return new F(t=>{jv(e,t).catch(n=>t.error(n))})}function Lv(e){return Ad(Ko(e))}function jv(e,t){var n,r,o,i;return Rd(this,void 0,void 0,function*(){try{for(n=xd(e);r=yield n.next(),!r.done;){let s=r.value;if(t.next(s),t.closed)return}}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=n.return)&&(yield i.call(n))}finally{if(o)throw o.error}}t.complete()})}function ge(e,t,n,r=0,o=!1){let i=t.schedule(function(){n(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function Xo(e,t=0){return T((n,r)=>{n.subscribe(S(r,o=>ge(r,e,()=>r.next(o),t),()=>ge(r,e,()=>r.complete(),t),o=>ge(r,e,()=>r.error(o),t)))})}function ei(e,t=0){return T((n,r)=>{r.add(e.schedule(()=>n.subscribe(r),t))})}function Od(e,t){return V(e).pipe(ei(t),Xo(t))}function kd(e,t){return V(e).pipe(ei(t),Xo(t))}function Pd(e,t){return new F(n=>{let r=0;return t.schedule(function(){r===e.length?n.complete():(n.next(e[r++]),n.closed||this.schedule())})})}function Fd(e,t){return new F(n=>{let r;return ge(n,t,()=>{r=e[Yo](),ge(n,t,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){n.error(s);return}i?n.complete():n.next(o)},0,!0)}),()=>M(r?.return)&&r.return()})}function ti(e,t){if(!e)throw new Error("Iterable cannot be null");return new F(n=>{ge(n,t,()=>{let r=e[Symbol.asyncIterator]();ge(n,t,()=>{r.next().then(o=>{o.done?n.complete():n.next(o.value)})},0,!0)})})}function Ld(e,t){return ti(Ko(e),t)}function jd(e,t){if(e!=null){if(Go(e))return Od(e,t);if(zo(e))return Pd(e,t);if(qo(e))return kd(e,t);if(Wo(e))return ti(e,t);if(Qo(e))return Fd(e,t);if(Jo(e))return Ld(e,t)}throw Zo(e)}function $(e,t){return t?jd(e,t):V(e)}function C(...e){let t=Ve(e);return $(e,t)}function Ct(e,t){let n=M(e)?e:()=>e,r=o=>o.error(n());return new F(t?o=>t.schedule(r,0,o):r)}function Va(e){return!!e&&(e instanceof F||M(e.lift)&&M(e.subscribe))}var ct=En(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function Vd(e){return e instanceof Date&&!isNaN(e)}function _(e,t){return T((n,r)=>{let o=0;n.subscribe(S(r,i=>{r.next(e.call(t,i,o++))}))})}var{isArray:Vv}=Array;function Uv(e,t){return Vv(t)?e(...t):e(t)}function ni(e){return _(t=>Uv(e,t))}var{isArray:Bv}=Array,{getPrototypeOf:$v,prototype:Hv,keys:zv}=Object;function ri(e){if(e.length===1){let t=e[0];if(Bv(t))return{args:t,keys:null};if(qv(t)){let n=zv(t);return{args:n.map(r=>t[r]),keys:n}}}return{args:e,keys:null}}function qv(e){return e&&typeof e=="object"&&$v(e)===Hv}function oi(e,t){return e.reduce((n,r,o)=>(n[r]=t[o],n),{})}function zt(...e){let t=Ve(e),n=Ho(e),{args:r,keys:o}=ri(e);if(r.length===0)return $([],t);let i=new F(Gv(r,t,o?s=>oi(o,s):ue));return n?i.pipe(ni(n)):i}function Gv(e,t,n=ue){return r=>{Ud(t,()=>{let{length:o}=e,i=new Array(o),s=o,a=o;for(let c=0;c<o;c++)Ud(t,()=>{let u=$(e[c],t),l=!1;u.subscribe(S(r,d=>{i[c]=d,l||(l=!0,a--),a||r.next(n(i.slice()))},()=>{--s||r.complete()}))},r)},r)}}function Ud(e,t,n){e?ge(n,e,t):t()}function Bd(e,t,n,r,o,i,s,a){let c=[],u=0,l=0,d=!1,h=()=>{d&&!c.length&&!u&&t.complete()},f=v=>u<r?g(v):c.push(v),g=v=>{i&&t.next(v),u++;let I=!1;V(n(v,l++)).subscribe(S(t,O=>{o?.(O),i?f(O):t.next(O)},()=>{I=!0},void 0,()=>{if(I)try{for(u--;c.length&&u<r;){let O=c.shift();s?ge(t,s,()=>g(O)):g(O)}h()}catch(O){t.error(O)}}))};return e.subscribe(S(t,f,()=>{d=!0,h()})),()=>{a?.()}}function Z(e,t,n=1/0){return M(t)?Z((r,o)=>_((i,s)=>t(r,i,o,s))(V(e(r,o))),n):(typeof t=="number"&&(n=t),T((r,o)=>Bd(r,o,e,n)))}function bt(e=1/0){return Z(ue,e)}function $d(){return bt(1)}function _n(...e){return $d()($(e,Ve(e)))}function ii(e){return new F(t=>{V(e()).subscribe(t)})}function Wv(...e){let t=Ho(e),{args:n,keys:r}=ri(e),o=new F(i=>{let{length:s}=n;if(!s){i.complete();return}let a=new Array(s),c=s,u=s;for(let l=0;l<s;l++){let d=!1;V(n[l]).subscribe(S(i,h=>{d||(d=!0,u--),a[l]=h},()=>c--,void 0,()=>{(!c||!d)&&(u||i.next(r?oi(r,a):a),i.complete())}))}});return t?o.pipe(ni(t)):o}function si(e=0,t,n=Td){let r=-1;return t!=null&&($o(t)?n=t:r=t),new F(o=>{let i=Vd(e)?+e-n.now():e;i<0&&(i=0);let s=0;return n.schedule(function(){o.closed||(o.next(s++),0<=r?this.schedule(void 0,r):o.complete())},i)})}function Zv(e=0,t=$t){return e<0&&(e=0),si(e,e,t)}function Yv(...e){let t=Ve(e),n=_d(e,1/0),r=e;return r.length?r.length===1?V(r[0]):bt(n)($(r,t)):ie}function re(e,t){return T((n,r)=>{let o=0;n.subscribe(S(r,i=>e.call(t,i,o++)&&r.next(i)))})}function Hd(e){return T((t,n)=>{let r=!1,o=null,i=null,s=!1,a=()=>{if(i?.unsubscribe(),i=null,r){r=!1;let u=o;o=null,n.next(u)}s&&n.complete()},c=()=>{i=null,s&&n.complete()};t.subscribe(S(n,u=>{r=!0,o=u,i||V(e(u)).subscribe(i=S(n,a,c))},()=>{s=!0,(!r||!i||i.closed)&&n.complete()}))})}function Qv(e,t=$t){return Hd(()=>si(e,t))}function te(e){return T((t,n)=>{let r=null,o=!1,i;r=t.subscribe(S(n,void 0,void 0,s=>{i=V(e(s,te(e)(t))),r?(r.unsubscribe(),r=null,i.subscribe(n)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(n))})}function zd(e,t,n,r,o){return(i,s)=>{let a=n,c=t,u=0;i.subscribe(S(s,l=>{let d=u++;c=a?e(c,l,d):(a=!0,l),r&&s.next(c)},o&&(()=>{a&&s.next(c),s.complete()})))}}function Ue(e,t){return M(t)?Z(e,t,1):Z(e,1)}function Kv(e,t=$t){return T((n,r)=>{let o=null,i=null,s=null,a=()=>{if(o){o.unsubscribe(),o=null;let u=i;i=null,r.next(u)}};function c(){let u=s+e,l=t.now();if(l<u){o=this.schedule(void 0,u-l),r.add(o);return}a()}n.subscribe(S(r,u=>{i=u,s=t.now(),o||(o=t.schedule(c,e),r.add(o))},()=>{a(),r.complete()},void 0,()=>{i=o=null}))})}function St(e){return T((t,n)=>{let r=!1;t.subscribe(S(n,o=>{r=!0,n.next(o)},()=>{r||n.next(e),n.complete()}))})}function Be(e){return e<=0?()=>ie:T((t,n)=>{let r=0;t.subscribe(S(n,o=>{++r<=e&&(n.next(o),e<=r&&n.complete())}))})}function Me(e,t=ue){return e=e??Jv,T((n,r)=>{let o,i=!0;n.subscribe(S(r,s=>{let a=t(s);(i||!e(o,a))&&(i=!1,o=a,r.next(s))}))})}function Jv(e,t){return e===t}function ai(e=Xv){return T((t,n)=>{let r=!1;t.subscribe(S(n,o=>{r=!0,n.next(o)},()=>r?n.complete():n.error(e())))})}function Xv(){return new ct}function oe(e){return T((t,n)=>{try{t.subscribe(n)}finally{n.add(e)}})}function ut(e,t){let n=arguments.length>=2;return r=>r.pipe(e?re((o,i)=>e(o,i,r)):ue,Be(1),n?St(t):ai(()=>new ct))}function Mn(e){return e<=0?()=>ie:T((t,n)=>{let r=[];t.subscribe(S(n,o=>{r.push(o),e<r.length&&r.shift()},()=>{for(let o of r)n.next(o);n.complete()},void 0,()=>{r=null}))})}function Ua(e,t){let n=arguments.length>=2;return r=>r.pipe(e?re((o,i)=>e(o,i,r)):ue,Mn(1),n?St(t):ai(()=>new ct))}function ey(){return T((e,t)=>{let n,r=!1;e.subscribe(S(t,o=>{let i=n;n=o,r&&t.next([i,o]),r=!0}))})}function Ba(e,t){return T(zd(e,t,arguments.length>=2,!0))}function Ha(e={}){let{connector:t=()=>new G,resetOnError:n=!0,resetOnComplete:r=!0,resetOnRefCountZero:o=!0}=e;return i=>{let s,a,c,u=0,l=!1,d=!1,h=()=>{a?.unsubscribe(),a=void 0},f=()=>{h(),s=c=void 0,l=d=!1},g=()=>{let v=s;f(),v?.unsubscribe()};return T((v,I)=>{u++,!d&&!l&&h();let O=c=c??t();I.add(()=>{u--,u===0&&!d&&!l&&(a=$a(g,o))}),O.subscribe(I),!s&&u>0&&(s=new at({next:it=>O.next(it),error:it=>{d=!0,h(),a=$a(f,n,it),O.error(it)},complete:()=>{l=!0,h(),a=$a(f,r),O.complete()}}),V(v).subscribe(s))})(i)}}function $a(e,t,...n){if(t===!0){e();return}if(t===!1)return;let r=new at({next:()=>{r.unsubscribe(),e()}});return V(t(...n)).subscribe(r)}function ty(e,t,n){let r,o=!1;return e&&typeof e=="object"?{bufferSize:r=1/0,windowTime:t=1/0,refCount:o=!1,scheduler:n}=e:r=e??1/0,Ha({connector:()=>new jo(r,t,n),resetOnError:!0,resetOnComplete:!1,resetOnRefCountZero:o})}function ny(e){return re((t,n)=>e<=n)}function za(...e){let t=Ve(e);return T((n,r)=>{(t?_n(e,n,t):_n(e,n)).subscribe(r)})}function me(e,t){return T((n,r)=>{let o=null,i=0,s=!1,a=()=>s&&!o&&r.complete();n.subscribe(S(r,c=>{o?.unsubscribe();let u=0,l=i++;V(e(c,l)).subscribe(o=S(r,d=>r.next(t?t(c,d,l,u++):d),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function ci(e){return T((t,n)=>{V(e).subscribe(S(n,()=>n.complete(),Cr)),!n.closed&&t.subscribe(n)})}function W(e,t,n){let r=M(e)||t||n?{next:e,error:t,complete:n}:e;return r?T((o,i)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;o.subscribe(S(i,c=>{var u;(u=r.next)===null||u===void 0||u.call(r,c),i.next(c)},()=>{var c;a=!1,(c=r.complete)===null||c===void 0||c.call(r),i.complete()},c=>{var u;a=!1,(u=r.error)===null||u===void 0||u.call(r,c),i.error(c)},()=>{var c,u;a&&((c=r.unsubscribe)===null||c===void 0||c.call(r)),(u=r.finalize)===null||u===void 0||u.call(r)}))}):ue}var Pf="https://angular.dev/best-practices/security#preventing-cross-site-scripting-xss",y=class extends Error{code;constructor(t,n){super(ou(t,n)),this.code=t}};function ry(e){return`NG0${Math.abs(e)}`}function ou(e,t){return`${ry(e)}${t?": "+t:""}`}var Ff=Symbol("InputSignalNode#UNSET"),oy=j(D({},xo),{transformFn:void 0,applyValueToInputSignal(e,t){wr(e,t)}});function Lf(e,t){let n=Object.create(oy);n.value=e,n.transformFn=t?.transform;function r(){if(yr(n),n.value===Ff){let o=null;throw new y(-950,o)}return n.value}return r[pe]=n,r}function Vr(e){return{toString:e}.toString()}var ui="__parameters__";function iy(e){return function(...n){if(e){let r=e(...n);for(let o in r)this[o]=r[o]}}}function jf(e,t,n){return Vr(()=>{let r=iy(t);function o(...i){if(this instanceof o)return r.apply(this,i),this;let s=new o(...i);return a.annotation=s,a;function a(c,u,l){let d=c.hasOwnProperty(ui)?c[ui]:Object.defineProperty(c,ui,{value:[]})[ui];for(;d.length<=l;)d.push(null);return(d[l]=d[l]||[]).push(s),c}}return o.prototype.ngMetadataName=e,o.annotationCls=o,o})}var Ne=globalThis;function B(e){for(let t in e)if(e[t]===B)return t;throw Error("Could not find renamed property on target object.")}function sy(e,t){for(let n in t)t.hasOwnProperty(n)&&!e.hasOwnProperty(n)&&(e[n]=t[n])}function De(e){if(typeof e=="string")return e;if(Array.isArray(e))return`[${e.map(De).join(", ")}]`;if(e==null)return""+e;let t=e.overriddenName||e.name;if(t)return`${t}`;let n=e.toString();if(n==null)return""+n;let r=n.indexOf(`
`);return r>=0?n.slice(0,r):n}function oc(e,t){return e?t?`${e} ${t}`:e:t||""}var ay=B({__forward_ref__:B});function Vf(e){return e.__forward_ref__=Vf,e.toString=function(){return De(this())},e}function le(e){return Uf(e)?e():e}function Uf(e){return typeof e=="function"&&e.hasOwnProperty(ay)&&e.__forward_ref__===Vf}function m(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function Ze(e){return{providers:e.providers||[],imports:e.imports||[]}}function Yi(e){return qd(e,$f)||qd(e,Hf)}function Bf(e){return Yi(e)!==null}function qd(e,t){return e.hasOwnProperty(t)?e[t]:null}function cy(e){let t=e&&(e[$f]||e[Hf]);return t||null}function Gd(e){return e&&(e.hasOwnProperty(Wd)||e.hasOwnProperty(uy))?e[Wd]:null}var $f=B({\u0275prov:B}),Wd=B({\u0275inj:B}),Hf=B({ngInjectableDef:B}),uy=B({ngInjectorDef:B}),w=class{_desc;ngMetadataName="InjectionToken";\u0275prov;constructor(t,n){this._desc=t,this.\u0275prov=void 0,typeof n=="number"?this.__NG_ELEMENT_ID__=n:n!==void 0&&(this.\u0275prov=m({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function zf(e){return e&&!!e.\u0275providers}var ly=B({\u0275cmp:B}),dy=B({\u0275dir:B}),fy=B({\u0275pipe:B}),hy=B({\u0275mod:B}),Di=B({\u0275fac:B}),Rr=B({__NG_ELEMENT_ID__:B}),Zd=B({__NG_ENV_ID__:B});function Qi(e){return typeof e=="string"?e:e==null?"":String(e)}function py(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():Qi(e)}function qf(e,t){throw new y(-200,e)}function iu(e,t){throw new y(-201,!1)}var N=function(e){return e[e.Default=0]="Default",e[e.Host=1]="Host",e[e.Self=2]="Self",e[e.SkipSelf=4]="SkipSelf",e[e.Optional=8]="Optional",e}(N||{}),ic;function Gf(){return ic}function ve(e){let t=ic;return ic=e,t}function Wf(e,t,n){let r=Yi(e);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(n&N.Optional)return null;if(t!==void 0)return t;iu(e,"Injector")}var gy={},qt=gy,sc="__NG_DI_FLAG__",Ei=class{injector;constructor(t){this.injector=t}retrieve(t,n){let r=n;return this.injector.get(t,r.optional?Ao:qt,r)}},wi="ngTempTokenPath",my="ngTokenPath",vy=/\n/gm,yy="\u0275",Yd="__source";function Dy(e,t=N.Default){if(Ir()===void 0)throw new y(-203,!1);if(Ir()===null)return Wf(e,void 0,t);{let n=Ir(),r;return n instanceof Ei?r=n.injector:r=n,r.get(e,t&N.Optional?null:void 0,t)}}function E(e,t=N.Default){return(Gf()||Dy)(le(e),t)}function p(e,t=N.Default){return E(e,Ki(t))}function Ki(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function ac(e){let t=[];for(let n=0;n<e.length;n++){let r=le(e[n]);if(Array.isArray(r)){if(r.length===0)throw new y(900,!1);let o,i=N.Default;for(let s=0;s<r.length;s++){let a=r[s],c=Ey(a);typeof c=="number"?c===-1?o=a.token:i|=c:o=a}t.push(E(o,i))}else t.push(E(r))}return t}function Zf(e,t){return e[sc]=t,e.prototype[sc]=t,e}function Ey(e){return e[sc]}function wy(e,t,n,r){let o=e[wi];throw t[Yd]&&o.unshift(t[Yd]),e.message=Iy(`
`+e.message,o,n,r),e[my]=o,e[wi]=null,e}function Iy(e,t,n,r=null){e=e&&e.charAt(0)===`
`&&e.charAt(1)==yy?e.slice(2):e;let o=De(t);if(Array.isArray(t))o=t.map(De).join(" -> ");else if(typeof t=="object"){let i=[];for(let s in t)if(t.hasOwnProperty(s)){let a=t[s];i.push(s+":"+(typeof a=="string"?JSON.stringify(a):De(a)))}o=`{${i.join(", ")}}`}return`${n}${r?"("+r+")":""}[${o}]: ${e.replace(vy,`
  `)}`}var Yf=Zf(jf("Optional"),8);var Cy=Zf(jf("SkipSelf"),4);function Wt(e,t){let n=e.hasOwnProperty(Di);return n?e[Di]:null}function by(e,t,n){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++){let o=e[r],i=t[r];if(n&&(o=n(o),i=n(i)),i!==o)return!1}return!0}function Sy(e){return e.flat(Number.POSITIVE_INFINITY)}function su(e,t){e.forEach(n=>Array.isArray(n)?su(n,t):t(n))}function Qf(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function Ii(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}function Ty(e,t){let n=[];for(let r=0;r<e;r++)n.push(t);return n}function _y(e,t,n,r){let o=e.length;if(o==t)e.push(n,r);else if(o===1)e.push(r,e[0]),e[0]=n;else{for(o--,e.push(e[o-1],e[o]);o>t;){let i=o-2;e[o]=e[i],o--}e[t]=n,e[t+1]=r}}function au(e,t,n){let r=Ur(e,t);return r>=0?e[r|1]=n:(r=~r,_y(e,r,t,n)),r}function qa(e,t){let n=Ur(e,t);if(n>=0)return e[n|1]}function Ur(e,t){return My(e,t,1)}function My(e,t,n){let r=0,o=e.length>>n;for(;o!==r;){let i=r+(o-r>>1),s=e[i<<n];if(t===s)return i<<n;s>t?o=i:r=i+1}return~(o<<n)}var Zt={},de=[],Pn=new w(""),Kf=new w("",-1),Jf=new w(""),Ci=class{get(t,n=qt){if(n===qt){let r=new Error(`NullInjectorError: No provider for ${De(t)}!`);throw r.name="NullInjectorError",r}return n}};function Xf(e,t){let n=e[hy]||null;if(!n&&t===!0)throw new Error(`Type ${De(e)} does not have '\u0275mod' property.`);return n}function Mt(e){return e[ly]||null}function Ry(e){return e[dy]||null}function Ny(e){return e[fy]||null}function Br(e){return{\u0275providers:e}}function xy(...e){return{\u0275providers:eh(!0,e),\u0275fromNgModule:!0}}function eh(e,...t){let n=[],r=new Set,o,i=s=>{n.push(s)};return su(t,s=>{let a=s;cc(a,i,[],r)&&(o||=[],o.push(a))}),o!==void 0&&th(o,i),n}function th(e,t){for(let n=0;n<e.length;n++){let{ngModule:r,providers:o}=e[n];cu(o,i=>{t(i,r)})}}function cc(e,t,n,r){if(e=le(e),!e)return!1;let o=null,i=Gd(e),s=!i&&Mt(e);if(!i&&!s){let c=e.ngModule;if(i=Gd(c),i)o=c;else return!1}else{if(s&&!s.standalone)return!1;o=e}let a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){let c=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let u of c)cc(u,t,n,r)}}else if(i){if(i.imports!=null&&!a){r.add(o);let u;try{su(i.imports,l=>{cc(l,t,n,r)&&(u||=[],u.push(l))})}finally{}u!==void 0&&th(u,t)}if(!a){let u=Wt(o)||(()=>new o);t({provide:o,useFactory:u,deps:de},o),t({provide:Jf,useValue:o,multi:!0},o),t({provide:Pn,useValue:()=>E(o),multi:!0},o)}let c=i.providers;if(c!=null&&!a){let u=e;cu(c,l=>{t(l,u)})}}else return!1;return o!==e&&e.providers!==void 0}function cu(e,t){for(let n of e)zf(n)&&(n=n.\u0275providers),Array.isArray(n)?cu(n,t):t(n)}var Ay=B({provide:String,useValue:B});function nh(e){return e!==null&&typeof e=="object"&&Ay in e}function Oy(e){return!!(e&&e.useExisting)}function ky(e){return!!(e&&e.useFactory)}function Fn(e){return typeof e=="function"}function Py(e){return!!e.useClass}var Ji=new w(""),pi={},Qd={},Ga;function Xi(){return Ga===void 0&&(Ga=new Ci),Ga}var se=class{},xr=class extends se{parent;source;scopes;records=new Map;_ngOnDestroyHooks=new Set;_onDestroyHooks=[];get destroyed(){return this._destroyed}_destroyed=!1;injectorDefTypes;constructor(t,n,r,o){super(),this.parent=n,this.source=r,this.scopes=o,lc(t,s=>this.processProvider(s)),this.records.set(Kf,Rn(void 0,this)),o.has("environment")&&this.records.set(se,Rn(void 0,this));let i=this.records.get(Ji);i!=null&&typeof i.value=="string"&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(Jf,de,N.Self))}retrieve(t,n){let r=n;return this.get(t,r.optional?Ao:qt,r)}destroy(){_r(this),this._destroyed=!0;let t=k(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let n=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of n)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),k(t)}}onDestroy(t){return _r(this),this._onDestroyHooks.push(t),()=>this.removeOnDestroy(t)}runInContext(t){_r(this);let n=st(this),r=ve(void 0),o;try{return t()}finally{st(n),ve(r)}}get(t,n=qt,r=N.Default){if(_r(this),t.hasOwnProperty(Zd))return t[Zd](this);r=Ki(r);let o,i=st(this),s=ve(void 0);try{if(!(r&N.SkipSelf)){let c=this.records.get(t);if(c===void 0){let u=Uy(t)&&Yi(t);u&&this.injectableDefInScope(u)?c=Rn(uc(t),pi):c=null,this.records.set(t,c)}if(c!=null)return this.hydrate(t,c,r)}let a=r&N.Self?Xi():this.parent;return n=r&N.Optional&&n===qt?null:n,a.get(t,n)}catch(a){if(a.name==="NullInjectorError"){if((a[wi]=a[wi]||[]).unshift(De(t)),i)throw a;return wy(a,t,"R3InjectorError",this.source)}else throw a}finally{ve(s),st(i)}}resolveInjectorInitializers(){let t=k(null),n=st(this),r=ve(void 0),o;try{let i=this.get(Pn,de,N.Self);for(let s of i)s()}finally{st(n),ve(r),k(t)}}toString(){let t=[],n=this.records;for(let r of n.keys())t.push(De(r));return`R3Injector[${t.join(", ")}]`}processProvider(t){t=le(t);let n=Fn(t)?t:le(t&&t.provide),r=Ly(t);if(!Fn(t)&&t.multi===!0){let o=this.records.get(n);o||(o=Rn(void 0,pi,!0),o.factory=()=>ac(o.multi),this.records.set(n,o)),n=t,o.multi.push(t)}this.records.set(n,r)}hydrate(t,n,r){let o=k(null);try{return n.value===Qd?qf(De(t)):n.value===pi&&(n.value=Qd,n.value=n.factory(void 0,r)),typeof n.value=="object"&&n.value&&Vy(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}finally{k(o)}}injectableDefInScope(t){if(!t.providedIn)return!1;let n=le(t.providedIn);return typeof n=="string"?n==="any"||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(t){let n=this._onDestroyHooks.indexOf(t);n!==-1&&this._onDestroyHooks.splice(n,1)}};function uc(e){let t=Yi(e),n=t!==null?t.factory:Wt(e);if(n!==null)return n;if(e instanceof w)throw new y(204,!1);if(e instanceof Function)return Fy(e);throw new y(204,!1)}function Fy(e){if(e.length>0)throw new y(204,!1);let n=cy(e);return n!==null?()=>n.factory(e):()=>new e}function Ly(e){if(nh(e))return Rn(void 0,e.useValue);{let t=rh(e);return Rn(t,pi)}}function rh(e,t,n){let r;if(Fn(e)){let o=le(e);return Wt(o)||uc(o)}else if(nh(e))r=()=>le(e.useValue);else if(ky(e))r=()=>e.useFactory(...ac(e.deps||[]));else if(Oy(e))r=(o,i)=>E(le(e.useExisting),i!==void 0&&i&N.Optional?N.Optional:void 0);else{let o=le(e&&(e.useClass||e.provide));if(jy(e))r=()=>new o(...ac(e.deps));else return Wt(o)||uc(o)}return r}function _r(e){if(e.destroyed)throw new y(205,!1)}function Rn(e,t,n=!1){return{factory:e,value:t,multi:n?[]:void 0}}function jy(e){return!!e.deps}function Vy(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function Uy(e){return typeof e=="function"||typeof e=="object"&&e instanceof w}function lc(e,t){for(let n of e)Array.isArray(n)?lc(n,t):n&&zf(n)?lc(n.\u0275providers,t):t(n)}function we(e,t){let n;e instanceof xr?(_r(e),n=e):n=new Ei(e);let r,o=st(n),i=ve(void 0);try{return t()}finally{st(o),ve(i)}}function uu(){return Gf()!==void 0||Ir()!=null}function lu(e){if(!uu())throw new y(-203,!1)}function By(e){let t=Ne.ng;if(t&&t.\u0275compilerFacade)return t.\u0275compilerFacade;throw new Error("JIT compiler unavailable")}function $y(e){return typeof e=="function"}var gt=0,x=1,b=2,ae=3,Pe=4,Ie=5,Ar=6,bi=7,fe=8,Ln=9,lt=10,Y=11,Or=12,Kd=13,zn=14,xe=15,Yt=16,Nn=17,dt=18,es=19,oh=20,Tt=21,Wa=22,Qt=23,Re=24,On=25,ne=26,ih=1;var Kt=7,Si=8,jn=9,Ee=10;function _t(e){return Array.isArray(e)&&typeof e[ih]=="object"}function mt(e){return Array.isArray(e)&&e[ih]===!0}function du(e){return(e.flags&4)!==0}function qn(e){return e.componentOffset>-1}function ts(e){return(e.flags&1)===1}function He(e){return!!e.template}function Ti(e){return(e[b]&512)!==0}function Gn(e){return(e[b]&256)===256}var dc=class{previousValue;currentValue;firstChange;constructor(t,n,r){this.previousValue=t,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}};function sh(e,t,n,r){t!==null?t.applyValueToInputSignal(t,r):e[n]=r}var $r=(()=>{let e=()=>ah;return e.ngInherit=!0,e})();function ah(e){return e.type.prototype.ngOnChanges&&(e.setInput=zy),Hy}function Hy(){let e=uh(this),t=e?.current;if(t){let n=e.previous;if(n===Zt)e.previous=t;else for(let r in t)n[r]=t[r];e.current=null,this.ngOnChanges(t)}}function zy(e,t,n,r,o){let i=this.declaredInputs[r],s=uh(e)||qy(e,{previous:Zt,current:null}),a=s.current||(s.current={}),c=s.previous,u=c[i];a[i]=new dc(u&&u.currentValue,n,c===Zt),sh(e,t,o,n)}var ch="__ngSimpleChanges__";function uh(e){return e[ch]||null}function qy(e,t){return e[ch]=t}var Jd=null;var H=function(e,t=null,n){Jd?.(e,t,n)},lh="svg",Gy="math";function ze(e){for(;Array.isArray(e);)e=e[gt];return e}function dh(e,t){return ze(t[e])}function Ye(e,t){return ze(t[e.index])}function fu(e,t){return e.data[t]}function fh(e,t){return e[t]}function hh(e,t,n,r){n>=e.data.length&&(e.data[n]=null,e.blueprint[n]=null),t[n]=r}function qe(e,t){let n=t[e];return _t(n)?n:n[gt]}function Wy(e){return(e[b]&4)===4}function hu(e){return(e[b]&128)===128}function Zy(e){return mt(e[ae])}function Vn(e,t){return t==null?null:e[t]}function ph(e){e[Nn]=0}function gh(e){e[b]&1024||(e[b]|=1024,hu(e)&&Wn(e))}function Yy(e,t){for(;e>0;)t=t[zn],e--;return t}function ns(e){return!!(e[b]&9216||e[Re]?.dirty)}function fc(e){e[lt].changeDetectionScheduler?.notify(8),e[b]&64&&(e[b]|=1024),ns(e)&&Wn(e)}function Wn(e){e[lt].changeDetectionScheduler?.notify(0);let t=Jt(e);for(;t!==null&&!(t[b]&8192||(t[b]|=8192,!hu(t)));)t=Jt(t)}function mh(e,t){if(Gn(e))throw new y(911,!1);e[Tt]===null&&(e[Tt]=[]),e[Tt].push(t)}function Qy(e,t){if(e[Tt]===null)return;let n=e[Tt].indexOf(t);n!==-1&&e[Tt].splice(n,1)}function Jt(e){let t=e[ae];return mt(t)?t[ae]:t}function pu(e){return e[bi]??=[]}function gu(e){return e.cleanup??=[]}function Ky(e,t,n,r){let o=pu(t);o.push(n),e.firstCreatePass&&gu(e).push(r,o.length-1)}var A={lFrame:Ch(null),bindingsEnabled:!0,skipHydrationRootTNode:null};var hc=!1;function Jy(){return A.lFrame.elementDepthCount}function Xy(){A.lFrame.elementDepthCount++}function eD(){A.lFrame.elementDepthCount--}function mu(){return A.bindingsEnabled}function vh(){return A.skipHydrationRootTNode!==null}function tD(e){return A.skipHydrationRootTNode===e}function nD(){A.skipHydrationRootTNode=null}function P(){return A.lFrame.lView}function Q(){return A.lFrame.tView}function YO(e){return A.lFrame.contextLView=e,e[fe]}function QO(e){return A.lFrame.contextLView=null,e}function he(){let e=yh();for(;e!==null&&e.type===64;)e=e.parent;return e}function yh(){return A.lFrame.currentTNode}function rD(){let e=A.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}function Rt(e,t){let n=A.lFrame;n.currentTNode=e,n.isParent=t}function vu(){return A.lFrame.isParent}function yu(){A.lFrame.isParent=!1}function oD(){return A.lFrame.contextLView}function Dh(){return hc}function _i(e){let t=hc;return hc=e,t}function iD(){let e=A.lFrame,t=e.bindingRootIndex;return t===-1&&(t=e.bindingRootIndex=e.tView.bindingStartIndex),t}function sD(e){return A.lFrame.bindingIndex=e}function Hr(){return A.lFrame.bindingIndex++}function Eh(e){let t=A.lFrame,n=t.bindingIndex;return t.bindingIndex=t.bindingIndex+e,n}function aD(){return A.lFrame.inI18n}function cD(e,t){let n=A.lFrame;n.bindingIndex=n.bindingRootIndex=e,pc(t)}function uD(){return A.lFrame.currentDirectiveIndex}function pc(e){A.lFrame.currentDirectiveIndex=e}function lD(e){let t=A.lFrame.currentDirectiveIndex;return t===-1?null:e[t]}function Du(){return A.lFrame.currentQueryIndex}function rs(e){A.lFrame.currentQueryIndex=e}function dD(e){let t=e[x];return t.type===2?t.declTNode:t.type===1?e[Ie]:null}function wh(e,t,n){if(n&N.SkipSelf){let o=t,i=e;for(;o=o.parent,o===null&&!(n&N.Host);)if(o=dD(i),o===null||(i=i[zn],o.type&10))break;if(o===null)return!1;t=o,e=i}let r=A.lFrame=Ih();return r.currentTNode=t,r.lView=e,!0}function Eu(e){let t=Ih(),n=e[x];A.lFrame=t,t.currentTNode=n.firstChild,t.lView=e,t.tView=n,t.contextLView=e,t.bindingIndex=n.bindingStartIndex,t.inI18n=!1}function Ih(){let e=A.lFrame,t=e===null?null:e.child;return t===null?Ch(e):t}function Ch(e){let t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=t),t}function bh(){let e=A.lFrame;return A.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var Sh=bh;function wu(){let e=bh();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function fD(e){return(A.lFrame.contextLView=Yy(e,A.lFrame.contextLView))[fe]}function sn(){return A.lFrame.selectedIndex}function Xt(e){A.lFrame.selectedIndex=e}function Iu(){let e=A.lFrame;return fu(e.tView,e.selectedIndex)}function KO(){A.lFrame.currentNamespace=lh}function JO(){hD()}function hD(){A.lFrame.currentNamespace=null}function pD(){return A.lFrame.currentNamespace}var Th=!0;function os(){return Th}function is(e){Th=e}function gD(e,t,n){let{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=t.type.prototype;if(r){let s=ah(t);(n.preOrderHooks??=[]).push(e,s),(n.preOrderCheckHooks??=[]).push(e,s)}o&&(n.preOrderHooks??=[]).push(0-e,o),i&&((n.preOrderHooks??=[]).push(e,i),(n.preOrderCheckHooks??=[]).push(e,i))}function Cu(e,t){for(let n=t.directiveStart,r=t.directiveEnd;n<r;n++){let i=e.data[n].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:c,ngAfterViewChecked:u,ngOnDestroy:l}=i;s&&(e.contentHooks??=[]).push(-n,s),a&&((e.contentHooks??=[]).push(n,a),(e.contentCheckHooks??=[]).push(n,a)),c&&(e.viewHooks??=[]).push(-n,c),u&&((e.viewHooks??=[]).push(n,u),(e.viewCheckHooks??=[]).push(n,u)),l!=null&&(e.destroyHooks??=[]).push(n,l)}}function gi(e,t,n){_h(e,t,3,n)}function mi(e,t,n,r){(e[b]&3)===n&&_h(e,t,n,r)}function Za(e,t){let n=e[b];(n&3)===t&&(n&=16383,n+=1,e[b]=n)}function _h(e,t,n,r){let o=r!==void 0?e[Nn]&65535:0,i=r??-1,s=t.length-1,a=0;for(let c=o;c<s;c++)if(typeof t[c+1]=="number"){if(a=t[c],r!=null&&a>=r)break}else t[c]<0&&(e[Nn]+=65536),(a<i||i==-1)&&(mD(e,n,t,c),e[Nn]=(e[Nn]&**********)+c+2),c++}function Xd(e,t){H(4,e,t);let n=k(null);try{t.call(e)}finally{k(n),H(5,e,t)}}function mD(e,t,n,r){let o=n[r]<0,i=n[r+1],s=o?-n[r]:n[r],a=e[s];o?e[b]>>14<e[Nn]>>16&&(e[b]&3)===t&&(e[b]+=16384,Xd(a,i)):Xd(a,i)}var kn=-1,en=class{factory;injectImpl;resolving=!1;canSeeViewProviders;multi;componentProviders;index;providerFactory;constructor(t,n,r){this.factory=t,this.canSeeViewProviders=n,this.injectImpl=r}};function vD(e){return(e.flags&8)!==0}function yD(e){return(e.flags&16)!==0}function DD(e,t,n){let r=0;for(;r<n.length;){let o=n[r];if(typeof o=="number"){if(o!==0)break;r++;let i=n[r++],s=n[r++],a=n[r++];e.setAttribute(t,s,a,i)}else{let i=o,s=n[++r];ED(i)?e.setProperty(t,i,s):e.setAttribute(t,i,s),r++}}return r}function Mh(e){return e===3||e===4||e===6}function ED(e){return e.charCodeAt(0)===64}function Un(e,t){if(!(t===null||t.length===0))if(e===null||e.length===0)e=t.slice();else{let n=-1;for(let r=0;r<t.length;r++){let o=t[r];typeof o=="number"?n=o:n===0||(n===-1||n===2?ef(e,n,o,null,t[++r]):ef(e,n,o,null,null))}}return e}function ef(e,t,n,r,o){let i=0,s=e.length;if(t===-1)s=-1;else for(;i<e.length;){let a=e[i++];if(typeof a=="number"){if(a===t){s=-1;break}else if(a>t){s=i-1;break}}}for(;i<e.length;){let a=e[i];if(typeof a=="number")break;if(a===n){o!==null&&(e[i+1]=o);return}i++,o!==null&&i++}s!==-1&&(e.splice(s,0,t),i=s+1),e.splice(i++,0,n),o!==null&&e.splice(i++,0,o)}function Rh(e){return e!==kn}function Mi(e){return e&32767}function wD(e){return e>>16}function Ri(e,t){let n=wD(e),r=t;for(;n>0;)r=r[zn],n--;return r}var gc=!0;function Ni(e){let t=gc;return gc=e,t}var ID=256,Nh=ID-1,xh=5,CD=0,$e={};function bD(e,t,n){let r;typeof n=="string"?r=n.charCodeAt(0)||0:n.hasOwnProperty(Rr)&&(r=n[Rr]),r==null&&(r=n[Rr]=CD++);let o=r&Nh,i=1<<o;t.data[e+(o>>xh)]|=i}function xi(e,t){let n=Ah(e,t);if(n!==-1)return n;let r=t[x];r.firstCreatePass&&(e.injectorIndex=t.length,Ya(r.data,e),Ya(t,null),Ya(r.blueprint,null));let o=bu(e,t),i=e.injectorIndex;if(Rh(o)){let s=Mi(o),a=Ri(o,t),c=a[x].data;for(let u=0;u<8;u++)t[i+u]=a[s+u]|c[s+u]}return t[i+8]=o,i}function Ya(e,t){e.push(0,0,0,0,0,0,0,0,t)}function Ah(e,t){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||t[e.injectorIndex+8]===null?-1:e.injectorIndex}function bu(e,t){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let n=0,r=null,o=t;for(;o!==null;){if(r=Lh(o),r===null)return kn;if(n++,o=o[zn],r.injectorIndex!==-1)return r.injectorIndex|n<<16}return kn}function mc(e,t,n){bD(e,t,n)}function SD(e,t){if(t==="class")return e.classes;if(t==="style")return e.styles;let n=e.attrs;if(n){let r=n.length,o=0;for(;o<r;){let i=n[o];if(Mh(i))break;if(i===0)o=o+2;else if(typeof i=="number")for(o++;o<r&&typeof n[o]=="string";)o++;else{if(i===t)return n[o+1];o=o+2}}}return null}function Oh(e,t,n){if(n&N.Optional||e!==void 0)return e;iu(t,"NodeInjector")}function kh(e,t,n,r){if(n&N.Optional&&r===void 0&&(r=null),(n&(N.Self|N.Host))===0){let o=e[Ln],i=ve(void 0);try{return o?o.get(t,r,n&N.Optional):Wf(t,r,n&N.Optional)}finally{ve(i)}}return Oh(r,t,n)}function Ph(e,t,n,r=N.Default,o){if(e!==null){if(t[b]&2048&&!(r&N.Self)){let s=RD(e,t,n,r,$e);if(s!==$e)return s}let i=Fh(e,t,n,r,$e);if(i!==$e)return i}return kh(t,n,r,o)}function Fh(e,t,n,r,o){let i=_D(n);if(typeof i=="function"){if(!wh(t,e,r))return r&N.Host?Oh(o,n,r):kh(t,n,r,o);try{let s;if(s=i(r),s==null&&!(r&N.Optional))iu(n);else return s}finally{Sh()}}else if(typeof i=="number"){let s=null,a=Ah(e,t),c=kn,u=r&N.Host?t[xe][Ie]:null;for((a===-1||r&N.SkipSelf)&&(c=a===-1?bu(e,t):t[a+8],c===kn||!nf(r,!1)?a=-1:(s=t[x],a=Mi(c),t=Ri(c,t)));a!==-1;){let l=t[x];if(tf(i,a,l.data)){let d=TD(a,t,n,s,r,u);if(d!==$e)return d}c=t[a+8],c!==kn&&nf(r,t[x].data[a+8]===u)&&tf(i,a,t)?(s=l,a=Mi(c),t=Ri(c,t)):a=-1}}return o}function TD(e,t,n,r,o,i){let s=t[x],a=s.data[e+8],c=r==null?qn(a)&&gc:r!=s&&(a.type&3)!==0,u=o&N.Host&&i===a,l=vi(a,s,n,c,u);return l!==null?kr(t,s,l,a,o):$e}function vi(e,t,n,r,o){let i=e.providerIndexes,s=t.data,a=i&1048575,c=e.directiveStart,u=e.directiveEnd,l=i>>20,d=r?a:a+l,h=o?a+l:u;for(let f=d;f<h;f++){let g=s[f];if(f<c&&n===g||f>=c&&g.type===n)return f}if(o){let f=s[c];if(f&&He(f)&&f.type===n)return c}return null}function kr(e,t,n,r,o){let i=e[n],s=t.data;if(i instanceof en){let a=i;a.resolving&&qf(py(s[n]));let c=Ni(a.canSeeViewProviders);a.resolving=!0;let u,l=a.injectImpl?ve(a.injectImpl):null,d=wh(e,r,N.Default);try{i=e[n]=a.factory(void 0,o,s,e,r),t.firstCreatePass&&n>=r.directiveStart&&gD(n,s[n],t)}finally{l!==null&&ve(l),Ni(c),a.resolving=!1,Sh()}}return i}function _D(e){if(typeof e=="string")return e.charCodeAt(0)||0;let t=e.hasOwnProperty(Rr)?e[Rr]:void 0;return typeof t=="number"?t>=0?t&Nh:MD:t}function tf(e,t,n){let r=1<<e;return!!(n[t+(e>>xh)]&r)}function nf(e,t){return!(e&N.Self)&&!(e&N.Host&&t)}var Gt=class{_tNode;_lView;constructor(t,n){this._tNode=t,this._lView=n}get(t,n,r){return Ph(this._tNode,this._lView,t,Ki(r),n)}};function MD(){return new Gt(he(),P())}function Su(e){return Vr(()=>{let t=e.prototype.constructor,n=t[Di]||vc(t),r=Object.prototype,o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==r;){let i=o[Di]||vc(o);if(i&&i!==n)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function vc(e){return Uf(e)?()=>{let t=vc(le(e));return t&&t()}:Wt(e)}function RD(e,t,n,r,o){let i=e,s=t;for(;i!==null&&s!==null&&s[b]&2048&&!Ti(s);){let a=Fh(i,s,n,r|N.Self,$e);if(a!==$e)return a;let c=i.parent;if(!c){let u=s[oh];if(u){let l=u.get(n,$e,r);if(l!==$e)return l}c=Lh(s),s=s[zn]}i=c}return o}function Lh(e){let t=e[x],n=t.type;return n===2?t.declTNode:n===1?e[Ie]:null}function Tu(e){return SD(he(),e)}function rf(e,t=null,n=null,r){let o=jh(e,t,n,r);return o.resolveInjectorInitializers(),o}function jh(e,t=null,n=null,r,o=new Set){let i=[n||de,xy(e)];return r=r||(typeof e=="object"?void 0:De(e)),new xr(i,t||Xi(),r||null,o)}var ce=class e{static THROW_IF_NOT_FOUND=qt;static NULL=new Ci;static create(t,n){if(Array.isArray(t))return rf({name:""},n,t,"");{let r=t.name??"";return rf({name:r},t.parent,t.providers,r)}}static \u0275prov=m({token:e,providedIn:"any",factory:()=>E(Kf)});static __NG_ELEMENT_ID__=-1};var of=class{attributeName;constructor(t){this.attributeName=t}__NG_ELEMENT_ID__=()=>Tu(this.attributeName);toString(){return`HostAttributeToken ${this.attributeName}`}},ND=new w("");ND.__NG_ELEMENT_ID__=e=>{let t=he();if(t===null)throw new y(204,!1);if(t.type&2)return t.value;if(e&N.Optional)return null;throw new y(204,!1)};var Vh=!1,Nt=(()=>{class e{static __NG_ELEMENT_ID__=xD;static __NG_ENV_ID__=n=>n}return e})(),Ai=class extends Nt{_lView;constructor(t){super(),this._lView=t}onDestroy(t){let n=this._lView;return Gn(n)?(t(),()=>{}):(mh(n,t),()=>Qy(n,t))}};function xD(){return new Ai(P())}var tn=class{},_u=new w("",{providedIn:"root",factory:()=>!1});var Uh=new w(""),Bh=new w(""),vt=(()=>{class e{taskId=0;pendingTasks=new Set;get _hasPendingTasks(){return this.hasPendingTasks.value}hasPendingTasks=new z(!1);add(){this._hasPendingTasks||this.hasPendingTasks.next(!0);let n=this.taskId++;return this.pendingTasks.add(n),n}has(n){return this.pendingTasks.has(n)}remove(n){this.pendingTasks.delete(n),this.pendingTasks.size===0&&this._hasPendingTasks&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this._hasPendingTasks&&this.hasPendingTasks.next(!1)}static \u0275prov=m({token:e,providedIn:"root",factory:()=>new e})}return e})();var yc=class extends G{__isAsync;destroyRef=void 0;pendingTasks=void 0;constructor(t=!1){super(),this.__isAsync=t,uu()&&(this.destroyRef=p(Nt,{optional:!0})??void 0,this.pendingTasks=p(vt,{optional:!0})??void 0)}emit(t){let n=k(null);try{super.next(t)}finally{k(n)}}subscribe(t,n,r){let o=t,i=n||(()=>null),s=r;if(t&&typeof t=="object"){let c=t;o=c.next?.bind(c),i=c.error?.bind(c),s=c.complete?.bind(c)}this.__isAsync&&(i=this.wrapInTimeout(i),o&&(o=this.wrapInTimeout(o)),s&&(s=this.wrapInTimeout(s)));let a=super.subscribe({next:o,error:i,complete:s});return t instanceof q&&t.add(a),a}wrapInTimeout(t){return n=>{let r=this.pendingTasks?.add();setTimeout(()=>{try{t(n)}finally{r!==void 0&&this.pendingTasks?.remove(r)}})}}},ye=yc;function Pr(...e){}function $h(e){let t,n;function r(){e=Pr;try{n!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(n),t!==void 0&&clearTimeout(t)}catch{}}return t=setTimeout(()=>{e(),r()}),typeof requestAnimationFrame=="function"&&(n=requestAnimationFrame(()=>{e(),r()})),()=>r()}function sf(e){return queueMicrotask(()=>e()),()=>{e=Pr}}var Mu="isAngularZone",Oi=Mu+"_ID",AD=0,U=class e{hasPendingMacrotasks=!1;hasPendingMicrotasks=!1;isStable=!0;onUnstable=new ye(!1);onMicrotaskEmpty=new ye(!1);onStable=new ye(!1);onError=new ye(!1);constructor(t){let{enableLongStackTrace:n=!1,shouldCoalesceEventChangeDetection:r=!1,shouldCoalesceRunChangeDetection:o=!1,scheduleInRootZone:i=Vh}=t;if(typeof Zone>"u")throw new y(908,!1);Zone.assertZonePatched();let s=this;s._nesting=0,s._outer=s._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(s._inner=s._inner.fork(new Zone.TaskTrackingZoneSpec)),n&&Zone.longStackTraceZoneSpec&&(s._inner=s._inner.fork(Zone.longStackTraceZoneSpec)),s.shouldCoalesceEventChangeDetection=!o&&r,s.shouldCoalesceRunChangeDetection=o,s.callbackScheduled=!1,s.scheduleInRootZone=i,PD(s)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get(Mu)===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new y(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new y(909,!1)}run(t,n,r){return this._inner.run(t,n,r)}runTask(t,n,r,o){let i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,t,OD,Pr,Pr);try{return i.runTask(s,n,r)}finally{i.cancelTask(s)}}runGuarded(t,n,r){return this._inner.runGuarded(t,n,r)}runOutsideAngular(t){return this._outer.run(t)}},OD={};function Ru(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function kD(e){if(e.isCheckStableRunning||e.callbackScheduled)return;e.callbackScheduled=!0;function t(){$h(()=>{e.callbackScheduled=!1,Dc(e),e.isCheckStableRunning=!0,Ru(e),e.isCheckStableRunning=!1})}e.scheduleInRootZone?Zone.root.run(()=>{t()}):e._outer.run(()=>{t()}),Dc(e)}function PD(e){let t=()=>{kD(e)},n=AD++;e._inner=e._inner.fork({name:"angular",properties:{[Mu]:!0,[Oi]:n,[Oi+n]:!0},onInvokeTask:(r,o,i,s,a,c)=>{if(FD(c))return r.invokeTask(i,s,a,c);try{return af(e),r.invokeTask(i,s,a,c)}finally{(e.shouldCoalesceEventChangeDetection&&s.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&t(),cf(e)}},onInvoke:(r,o,i,s,a,c,u)=>{try{return af(e),r.invoke(i,s,a,c,u)}finally{e.shouldCoalesceRunChangeDetection&&!e.callbackScheduled&&!LD(c)&&t(),cf(e)}},onHasTask:(r,o,i,s)=>{r.hasTask(i,s),o===i&&(s.change=="microTask"?(e._hasPendingMicrotasks=s.microTask,Dc(e),Ru(e)):s.change=="macroTask"&&(e.hasPendingMacrotasks=s.macroTask))},onHandleError:(r,o,i,s)=>(r.handleError(i,s),e.runOutsideAngular(()=>e.onError.emit(s)),!1)})}function Dc(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.callbackScheduled===!0?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function af(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function cf(e){e._nesting--,Ru(e)}var ki=class{hasPendingMicrotasks=!1;hasPendingMacrotasks=!1;isStable=!0;onUnstable=new ye;onMicrotaskEmpty=new ye;onStable=new ye;onError=new ye;run(t,n,r){return t.apply(n,r)}runGuarded(t,n,r){return t.apply(n,r)}runOutsideAngular(t){return t()}runTask(t,n,r,o){return t.apply(n,r)}};function FD(e){return Hh(e,"__ignore_ng_zone__")}function LD(e){return Hh(e,"__scheduler_tick__")}function Hh(e,t){return!Array.isArray(e)||e.length!==1?!1:e[0]?.data?.[t]===!0}function jD(e="zone.js",t){return e==="noop"?new ki:e==="zone.js"?new U(t):e}var Ge=class{_console=console;handleError(t){this._console.error("ERROR",t)}},VD=new w("",{providedIn:"root",factory:()=>{let e=p(U),t=p(Ge);return n=>e.runOutsideAngular(()=>t.handleError(n))}});function uf(e,t){return Lf(e,t)}function UD(e){return Lf(Ff,e)}var zh=(uf.required=UD,uf);function BD(){return Zn(he(),P())}function Zn(e,t){return new an(Ye(e,t))}var an=(()=>{class e{nativeElement;constructor(n){this.nativeElement=n}static __NG_ELEMENT_ID__=BD}return e})();function qh(e){return e instanceof an?e.nativeElement:e}function XO(e){return typeof e=="function"&&e[pe]!==void 0}function $D(e,t){let n=Sa(e,t?.equal),r=n[pe];return n.set=o=>wr(r,o),n.update=o=>Ta(r,o),n.asReadonly=HD.bind(n),n}function HD(){let e=this[pe];if(e.readonlyFn===void 0){let t=()=>this();t[pe]=e,e.readonlyFn=t}return e.readonlyFn}function zD(){return this._results[Symbol.iterator]()}var Ec=class{_emitDistinctChangesOnly;dirty=!0;_onDirty=void 0;_results=[];_changesDetected=!1;_changes=void 0;length=0;first=void 0;last=void 0;get changes(){return this._changes??=new G}constructor(t=!1){this._emitDistinctChangesOnly=t}get(t){return this._results[t]}map(t){return this._results.map(t)}filter(t){return this._results.filter(t)}find(t){return this._results.find(t)}reduce(t,n){return this._results.reduce(t,n)}forEach(t){this._results.forEach(t)}some(t){return this._results.some(t)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(t,n){this.dirty=!1;let r=Sy(t);(this._changesDetected=!by(this._results,r,n))&&(this._results=r,this.length=r.length,this.last=r[this.length-1],this.first=r[0])}notifyOnChanges(){this._changes!==void 0&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.next(this)}onDirty(t){this._onDirty=t}setDirty(){this.dirty=!0,this._onDirty?.()}destroy(){this._changes!==void 0&&(this._changes.complete(),this._changes.unsubscribe())}[Symbol.iterator]=zD};function Gh(e){return(e.flags&128)===128}var Wh=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(Wh||{}),Zh=new Map,qD=0;function GD(){return qD++}function WD(e){Zh.set(e[es],e)}function wc(e){Zh.delete(e[es])}var lf="__ngContext__";function Yn(e,t){_t(t)?(e[lf]=t[es],WD(t)):e[lf]=t}function Yh(e){return Kh(e[Or])}function Qh(e){return Kh(e[Pe])}function Kh(e){for(;e!==null&&!mt(e);)e=e[Pe];return e}var Ic;function Jh(e){Ic=e}function ZD(){if(Ic!==void 0)return Ic;if(typeof document<"u")return document;throw new y(210,!1)}var Nu=new w("",{providedIn:"root",factory:()=>YD}),YD="ng",xu=new w(""),Qn=new w("",{providedIn:"platform",factory:()=>"unknown"});var ek=new w(""),Au=new w("",{providedIn:"root",factory:()=>ZD().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});var QD="h",KD="b";var Xh=!1,JD=new w("",{providedIn:"root",factory:()=>Xh});var Ou=function(e){return e[e.CHANGE_DETECTION=0]="CHANGE_DETECTION",e[e.AFTER_NEXT_RENDER=1]="AFTER_NEXT_RENDER",e}(Ou||{}),Kn=new w(""),df=new Set;function cn(e){df.has(e)||(df.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}var ku=(()=>{class e{view;node;constructor(n,r){this.view=n,this.node=r}static __NG_ELEMENT_ID__=XD}return e})();function XD(){return new ku(P(),he())}var xn=function(e){return e[e.EarlyRead=0]="EarlyRead",e[e.Write=1]="Write",e[e.MixedReadWrite=2]="MixedReadWrite",e[e.Read=3]="Read",e}(xn||{}),ep=(()=>{class e{impl=null;execute(){this.impl?.execute()}static \u0275prov=m({token:e,providedIn:"root",factory:()=>new e})}return e})(),eE=[xn.EarlyRead,xn.Write,xn.MixedReadWrite,xn.Read],tE=(()=>{class e{ngZone=p(U);scheduler=p(tn);errorHandler=p(Ge,{optional:!0});sequences=new Set;deferredRegistrations=new Set;executing=!1;constructor(){p(Kn,{optional:!0})}execute(){let n=this.sequences.size>0;n&&H(16),this.executing=!0;for(let r of eE)for(let o of this.sequences)if(!(o.erroredOrDestroyed||!o.hooks[r]))try{o.pipelinedValue=this.ngZone.runOutsideAngular(()=>this.maybeTrace(()=>{let i=o.hooks[r];return i(o.pipelinedValue)},o.snapshot))}catch(i){o.erroredOrDestroyed=!0,this.errorHandler?.handleError(i)}this.executing=!1;for(let r of this.sequences)r.afterRun(),r.once&&(this.sequences.delete(r),r.destroy());for(let r of this.deferredRegistrations)this.sequences.add(r);this.deferredRegistrations.size>0&&this.scheduler.notify(7),this.deferredRegistrations.clear(),n&&H(17)}register(n){let{view:r}=n;r!==void 0?((r[On]??=[]).push(n),Wn(r),r[b]|=8192):this.executing?this.deferredRegistrations.add(n):this.addSequence(n)}addSequence(n){this.sequences.add(n),this.scheduler.notify(7)}unregister(n){this.executing&&this.sequences.has(n)?(n.erroredOrDestroyed=!0,n.pipelinedValue=void 0,n.once=!0):(this.sequences.delete(n),this.deferredRegistrations.delete(n))}maybeTrace(n,r){return r?r.run(Ou.AFTER_NEXT_RENDER,n):n()}static \u0275prov=m({token:e,providedIn:"root",factory:()=>new e})}return e})(),Cc=class{impl;hooks;view;once;snapshot;erroredOrDestroyed=!1;pipelinedValue=void 0;unregisterOnDestroy;constructor(t,n,r,o,i,s=null){this.impl=t,this.hooks=n,this.view=r,this.once=o,this.snapshot=s,this.unregisterOnDestroy=i?.onDestroy(()=>this.destroy())}afterRun(){this.erroredOrDestroyed=!1,this.pipelinedValue=void 0,this.snapshot?.dispose(),this.snapshot=null}destroy(){this.impl.unregister(this),this.unregisterOnDestroy?.();let t=this.view?.[On];t&&(this.view[On]=t.filter(n=>n!==this))}};function nE(e,t){!t?.injector&&lu(nE);let n=t?.injector??p(ce);return cn("NgAfterRender"),tp(e,n,t,!1)}function Pu(e,t){!t?.injector&&lu(Pu);let n=t?.injector??p(ce);return cn("NgAfterNextRender"),tp(e,n,t,!0)}function rE(e,t){if(e instanceof Function){let n=[void 0,void 0,void 0,void 0];return n[t]=e,n}else return[e.earlyRead,e.write,e.mixedReadWrite,e.read]}function tp(e,t,n,r){let o=t.get(ep);o.impl??=t.get(tE);let i=t.get(Kn,null,{optional:!0}),s=n?.phase??xn.MixedReadWrite,a=n?.manualCleanup!==!0?t.get(Nt):null,c=t.get(ku,null,{optional:!0}),u=new Cc(o.impl,rE(e,s),c?.view,r,a,i?.snapshot(null));return o.impl.register(u),u}var oE=(e,t,n,r)=>{};function iE(e,t,n,r){oE(e,t,n,r)}var sE=()=>null;function np(e,t,n=!1){return sE(e,t,n)}function rp(e,t){let n=e.contentQueries;if(n!==null){let r=k(null);try{for(let o=0;o<n.length;o+=2){let i=n[o],s=n[o+1];if(s!==-1){let a=e.data[s];rs(i),a.contentQueries(2,t[s],s)}}}finally{k(r)}}}function bc(e,t,n){rs(0);let r=k(null);try{t(e,n)}finally{k(r)}}function Fu(e,t,n){if(du(t)){let r=k(null);try{let o=t.directiveStart,i=t.directiveEnd;for(let s=o;s<i;s++){let a=e.data[s];if(a.contentQueries){let c=n[s];a.contentQueries(1,c,s)}}}finally{k(r)}}}var We=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(We||{}),li;function aE(){if(li===void 0&&(li=null,Ne.trustedTypes))try{li=Ne.trustedTypes.createPolicy("angular",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return li}function ss(e){return aE()?.createHTML(e)||e}var ft=class{changingThisBreaksApplicationSecurity;constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${Pf})`}},Sc=class extends ft{getTypeName(){return"HTML"}},Tc=class extends ft{getTypeName(){return"Style"}},_c=class extends ft{getTypeName(){return"Script"}},Mc=class extends ft{getTypeName(){return"URL"}},Rc=class extends ft{getTypeName(){return"ResourceURL"}};function Qe(e){return e instanceof ft?e.changingThisBreaksApplicationSecurity:e}function un(e,t){let n=cE(e);if(n!=null&&n!==t){if(n==="ResourceURL"&&t==="URL")return!0;throw new Error(`Required a safe ${t}, got a ${n} (see ${Pf})`)}return n===t}function cE(e){return e instanceof ft&&e.getTypeName()||null}function op(e){return new Sc(e)}function ip(e){return new Tc(e)}function sp(e){return new _c(e)}function ap(e){return new Mc(e)}function cp(e){return new Rc(e)}function uE(e){let t=new xc(e);return lE()?new Nc(t):t}var Nc=class{inertDocumentHelper;constructor(t){this.inertDocumentHelper=t}getInertBodyElement(t){t="<body><remove></remove>"+t;try{let n=new window.DOMParser().parseFromString(ss(t),"text/html").body;return n===null?this.inertDocumentHelper.getInertBodyElement(t):(n.firstChild?.remove(),n)}catch{return null}}},xc=class{defaultDoc;inertDocument;constructor(t){this.defaultDoc=t,this.inertDocument=this.defaultDoc.implementation.createHTMLDocument("sanitization-inert")}getInertBodyElement(t){let n=this.inertDocument.createElement("template");return n.innerHTML=ss(t),n}};function lE(){try{return!!new window.DOMParser().parseFromString(ss(""),"text/html")}catch{return!1}}var dE=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function as(e){return e=String(e),e.match(dE)?e:"unsafe:"+e}function yt(e){let t={};for(let n of e.split(","))t[n]=!0;return t}function zr(...e){let t={};for(let n of e)for(let r in n)n.hasOwnProperty(r)&&(t[r]=!0);return t}var up=yt("area,br,col,hr,img,wbr"),lp=yt("colgroup,dd,dt,li,p,tbody,td,tfoot,th,thead,tr"),dp=yt("rp,rt"),fE=zr(dp,lp),hE=zr(lp,yt("address,article,aside,blockquote,caption,center,del,details,dialog,dir,div,dl,figure,figcaption,footer,h1,h2,h3,h4,h5,h6,header,hgroup,hr,ins,main,map,menu,nav,ol,pre,section,summary,table,ul")),pE=zr(dp,yt("a,abbr,acronym,audio,b,bdi,bdo,big,br,cite,code,del,dfn,em,font,i,img,ins,kbd,label,map,mark,picture,q,ruby,rp,rt,s,samp,small,source,span,strike,strong,sub,sup,time,track,tt,u,var,video")),ff=zr(up,hE,pE,fE),fp=yt("background,cite,href,itemtype,longdesc,poster,src,xlink:href"),gE=yt("abbr,accesskey,align,alt,autoplay,axis,bgcolor,border,cellpadding,cellspacing,class,clear,color,cols,colspan,compact,controls,coords,datetime,default,dir,download,face,headers,height,hidden,hreflang,hspace,ismap,itemscope,itemprop,kind,label,lang,language,loop,media,muted,nohref,nowrap,open,preload,rel,rev,role,rows,rowspan,rules,scope,scrolling,shape,size,sizes,span,srclang,srcset,start,summary,tabindex,target,title,translate,type,usemap,valign,value,vspace,width"),mE=yt("aria-activedescendant,aria-atomic,aria-autocomplete,aria-busy,aria-checked,aria-colcount,aria-colindex,aria-colspan,aria-controls,aria-current,aria-describedby,aria-details,aria-disabled,aria-dropeffect,aria-errormessage,aria-expanded,aria-flowto,aria-grabbed,aria-haspopup,aria-hidden,aria-invalid,aria-keyshortcuts,aria-label,aria-labelledby,aria-level,aria-live,aria-modal,aria-multiline,aria-multiselectable,aria-orientation,aria-owns,aria-placeholder,aria-posinset,aria-pressed,aria-readonly,aria-relevant,aria-required,aria-roledescription,aria-rowcount,aria-rowindex,aria-rowspan,aria-selected,aria-setsize,aria-sort,aria-valuemax,aria-valuemin,aria-valuenow,aria-valuetext"),vE=zr(fp,gE,mE),yE=yt("script,style,template"),Ac=class{sanitizedSomething=!1;buf=[];sanitizeChildren(t){let n=t.firstChild,r=!0,o=[];for(;n;){if(n.nodeType===Node.ELEMENT_NODE?r=this.startElement(n):n.nodeType===Node.TEXT_NODE?this.chars(n.nodeValue):this.sanitizedSomething=!0,r&&n.firstChild){o.push(n),n=wE(n);continue}for(;n;){n.nodeType===Node.ELEMENT_NODE&&this.endElement(n);let i=EE(n);if(i){n=i;break}n=o.pop()}}return this.buf.join("")}startElement(t){let n=hf(t).toLowerCase();if(!ff.hasOwnProperty(n))return this.sanitizedSomething=!0,!yE.hasOwnProperty(n);this.buf.push("<"),this.buf.push(n);let r=t.attributes;for(let o=0;o<r.length;o++){let i=r.item(o),s=i.name,a=s.toLowerCase();if(!vE.hasOwnProperty(a)){this.sanitizedSomething=!0;continue}let c=i.value;fp[a]&&(c=as(c)),this.buf.push(" ",s,'="',pf(c),'"')}return this.buf.push(">"),!0}endElement(t){let n=hf(t).toLowerCase();ff.hasOwnProperty(n)&&!up.hasOwnProperty(n)&&(this.buf.push("</"),this.buf.push(n),this.buf.push(">"))}chars(t){this.buf.push(pf(t))}};function DE(e,t){return(e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY)!==Node.DOCUMENT_POSITION_CONTAINED_BY}function EE(e){let t=e.nextSibling;if(t&&e!==t.previousSibling)throw hp(t);return t}function wE(e){let t=e.firstChild;if(t&&DE(e,t))throw hp(t);return t}function hf(e){let t=e.nodeName;return typeof t=="string"?t:"FORM"}function hp(e){return new Error(`Failed to sanitize html because the element is clobbered: ${e.outerHTML}`)}var IE=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,CE=/([^\#-~ |!])/g;function pf(e){return e.replace(/&/g,"&amp;").replace(IE,function(t){let n=t.charCodeAt(0),r=t.charCodeAt(1);return"&#"+((n-55296)*1024+(r-56320)+65536)+";"}).replace(CE,function(t){return"&#"+t.charCodeAt(0)+";"}).replace(/</g,"&lt;").replace(/>/g,"&gt;")}var di;function pp(e,t){let n=null;try{di=di||uE(e);let r=t?String(t):"";n=di.getInertBodyElement(r);let o=5,i=r;do{if(o===0)throw new Error("Failed to sanitize html because the input is unstable");o--,r=i,i=n.innerHTML,n=di.getInertBodyElement(r)}while(r!==i);let a=new Ac().sanitizeChildren(gf(n)||n);return ss(a)}finally{if(n){let r=gf(n)||n;for(;r.firstChild;)r.firstChild.remove()}}}function gf(e){return"content"in e&&bE(e)?e.content:null}function bE(e){return e.nodeType===Node.ELEMENT_NODE&&e.nodeName==="TEMPLATE"}var Dt=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}(Dt||{});function tk(e){let t=SE();return t?t.sanitize(Dt.URL,e)||"":un(e,"URL")?Qe(e):as(Qi(e))}function SE(){let e=P();return e&&e[lt].sanitizer}var TE=/^>|^->|<!--|-->|--!>|<!-$/g,_E=/(<|>)/g,ME="\u200B$1\u200B";function RE(e){return e.replace(TE,t=>t.replace(_E,ME))}function gp(e){return e instanceof Function?e():e}function NE(e,t,n){let r=e.length;for(;;){let o=e.indexOf(t,n);if(o===-1)return o;if(o===0||e.charCodeAt(o-1)<=32){let i=t.length;if(o+i===r||e.charCodeAt(o+i)<=32)return o}n=o+1}}var mp="ng-template";function xE(e,t,n,r){let o=0;if(r){for(;o<t.length&&typeof t[o]=="string";o+=2)if(t[o]==="class"&&NE(t[o+1].toLowerCase(),n,0)!==-1)return!0}else if(Lu(e))return!1;if(o=t.indexOf(1,o),o>-1){let i;for(;++o<t.length&&typeof(i=t[o])=="string";)if(i.toLowerCase()===n)return!0}return!1}function Lu(e){return e.type===4&&e.value!==mp}function AE(e,t,n){let r=e.type===4&&!n?mp:e.value;return t===r}function OE(e,t,n){let r=4,o=e.attrs,i=o!==null?FE(o):0,s=!1;for(let a=0;a<t.length;a++){let c=t[a];if(typeof c=="number"){if(!s&&!Oe(r)&&!Oe(c))return!1;if(s&&Oe(c))continue;s=!1,r=c|r&1;continue}if(!s)if(r&4){if(r=2|r&1,c!==""&&!AE(e,c,n)||c===""&&t.length===1){if(Oe(r))return!1;s=!0}}else if(r&8){if(o===null||!xE(e,o,c,n)){if(Oe(r))return!1;s=!0}}else{let u=t[++a],l=kE(c,o,Lu(e),n);if(l===-1){if(Oe(r))return!1;s=!0;continue}if(u!==""){let d;if(l>i?d="":d=o[l+1].toLowerCase(),r&2&&u!==d){if(Oe(r))return!1;s=!0}}}}return Oe(r)||s}function Oe(e){return(e&1)===0}function kE(e,t,n,r){if(t===null)return-1;let o=0;if(r||!n){let i=!1;for(;o<t.length;){let s=t[o];if(s===e)return o;if(s===3||s===6)i=!0;else if(s===1||s===2){let a=t[++o];for(;typeof a=="string";)a=t[++o];continue}else{if(s===4)break;if(s===0){o+=4;continue}}o+=i?1:2}return-1}else return LE(t,e)}function vp(e,t,n=!1){for(let r=0;r<t.length;r++)if(OE(e,t[r],n))return!0;return!1}function PE(e){let t=e.attrs;if(t!=null){let n=t.indexOf(5);if((n&1)===0)return t[n+1]}return null}function FE(e){for(let t=0;t<e.length;t++){let n=e[t];if(Mh(n))return t}return e.length}function LE(e,t){let n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){let r=e[n];if(typeof r=="number")return-1;if(r===t)return n;n++}return-1}function jE(e,t){e:for(let n=0;n<t.length;n++){let r=t[n];if(e.length===r.length){for(let o=0;o<e.length;o++)if(e[o]!==r[o])continue e;return!0}}return!1}function mf(e,t){return e?":not("+t.trim()+")":t}function VE(e){let t=e[0],n=1,r=2,o="",i=!1;for(;n<e.length;){let s=e[n];if(typeof s=="string")if(r&2){let a=e[++n];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?o+="."+s:r&4&&(o+=" "+s);else o!==""&&!Oe(s)&&(t+=mf(i,o),o=""),r=s,i=i||!Oe(r);n++}return o!==""&&(t+=mf(i,o)),t}function UE(e){return e.map(VE).join(",")}function BE(e){let t=[],n=[],r=1,o=2;for(;r<e.length;){let i=e[r];if(typeof i=="string")o===2?i!==""&&t.push(i,e[++r]):o===8&&n.push(i);else{if(!Oe(o))break;o=i}r++}return n.length&&t.push(1,...n),t}var Ke={};function $E(e,t){return e.createText(t)}function HE(e,t,n){e.setValue(t,n)}function zE(e,t){return e.createComment(RE(t))}function yp(e,t,n){return e.createElement(t,n)}function Pi(e,t,n,r,o){e.insertBefore(t,n,r,o)}function Dp(e,t,n){e.appendChild(t,n)}function vf(e,t,n,r,o){r!==null?Pi(e,t,n,r,o):Dp(e,t,n)}function qE(e,t,n){e.removeChild(null,t,n)}function GE(e,t,n){e.setAttribute(t,"style",n)}function WE(e,t,n){n===""?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n)}function Ep(e,t,n){let{mergedAttrs:r,classes:o,styles:i}=n;r!==null&&DD(e,t,r),o!==null&&WE(e,t,o),i!==null&&GE(e,t,i)}function ju(e,t,n,r,o,i,s,a,c,u,l){let d=ne+r,h=d+o,f=ZE(d,h),g=typeof u=="function"?u():u;return f[x]={type:e,blueprint:f,template:n,queries:null,viewQuery:a,declTNode:t,data:f.slice().fill(null,d),bindingStartIndex:d,expandoStartIndex:h,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof i=="function"?i():i,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:c,consts:g,incompleteFirstPass:!1,ssrId:l}}function ZE(e,t){let n=[];for(let r=0;r<t;r++)n.push(r<e?null:Ke);return n}function YE(e){let t=e.tView;return t===null||t.incompleteFirstPass?e.tView=ju(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):t}function Vu(e,t,n,r,o,i,s,a,c,u,l){let d=t.blueprint.slice();return d[gt]=o,d[b]=r|4|128|8|64|1024,(u!==null||e&&e[b]&2048)&&(d[b]|=2048),ph(d),d[ae]=d[zn]=e,d[fe]=n,d[lt]=s||e&&e[lt],d[Y]=a||e&&e[Y],d[Ln]=c||e&&e[Ln]||null,d[Ie]=i,d[es]=GD(),d[Ar]=l,d[oh]=u,d[xe]=t.type==2?e[xe]:d,d}function QE(e,t,n){let r=Ye(t,e),o=YE(n),i=e[lt].rendererFactory,s=Uu(e,Vu(e,o,null,wp(n),r,t,null,i.createRenderer(r,n),null,null,null));return e[t.index]=s}function wp(e){let t=16;return e.signals?t=4096:e.onPush&&(t=64),t}function Ip(e,t,n,r){if(n===0)return-1;let o=t.length;for(let i=0;i<n;i++)t.push(r),e.blueprint.push(r),e.data.push(null);return o}function Uu(e,t){return e[Or]?e[Kd][Pe]=t:e[Or]=t,e[Kd]=t,t}function nk(e=1){Cp(Q(),P(),sn()+e,!1)}function Cp(e,t,n,r){if(!r)if((t[b]&3)===3){let i=e.preOrderCheckHooks;i!==null&&gi(t,i,n)}else{let i=e.preOrderHooks;i!==null&&mi(t,i,0,n)}Xt(n)}var cs=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(cs||{});function Oc(e,t,n,r){let o=k(null);try{let[i,s,a]=e.inputs[n],c=null;(s&cs.SignalBased)!==0&&(c=t[i][pe]),c!==null&&c.transformFn!==void 0?r=c.transformFn(r):a!==null&&(r=a.call(t,r)),e.setInput!==null?e.setInput(t,c,r,n,i):sh(t,c,i,r)}finally{k(o)}}function bp(e,t,n,r,o){let i=sn(),s=r&2;try{Xt(-1),s&&t.length>ne&&Cp(e,t,ne,!1),H(s?2:0,o),n(r,o)}finally{Xt(i),H(s?3:1,o)}}function us(e,t,n){nw(e,t,n),(n.flags&64)===64&&rw(e,t,n)}function Bu(e,t,n=Ye){let r=t.localNames;if(r!==null){let o=t.index+1;for(let i=0;i<r.length;i+=2){let s=r[i+1],a=s===-1?n(t,e):e[s];e[o++]=a}}}function KE(e,t,n,r){let i=r.get(JD,Xh)||n===We.ShadowDom,s=e.selectRootElement(t,i);return JE(s),s}function JE(e){XE(e)}var XE=()=>null;function ew(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function Sp(e,t,n,r,o,i,s,a){if(!a&&Hu(t,e,n,r,o)){qn(t)&&tw(n,t.index);return}if(t.type&3){let c=Ye(t,n);r=ew(r),o=s!=null?s(o,t.value||"",r):o,i.setProperty(c,r,o)}else t.type&12}function tw(e,t){let n=qe(t,e);n[b]&16||(n[b]|=64)}function nw(e,t,n){let r=n.directiveStart,o=n.directiveEnd;qn(n)&&QE(t,n,e.data[r+n.componentOffset]),e.firstCreatePass||xi(n,t);let i=n.initialInputs;for(let s=r;s<o;s++){let a=e.data[s],c=kr(t,e,s,n);if(Yn(c,t),i!==null&&aw(t,s-r,c,a,n,i),He(a)){let u=qe(n.index,t);u[fe]=kr(t,e,s,n)}}}function rw(e,t,n){let r=n.directiveStart,o=n.directiveEnd,i=n.index,s=uD();try{Xt(i);for(let a=r;a<o;a++){let c=e.data[a],u=t[a];pc(a),(c.hostBindings!==null||c.hostVars!==0||c.hostAttrs!==null)&&ow(c,u)}}finally{Xt(-1),pc(s)}}function ow(e,t){e.hostBindings!==null&&e.hostBindings(1,t)}function $u(e,t){let n=e.directiveRegistry,r=null;if(n)for(let o=0;o<n.length;o++){let i=n[o];vp(t,i.selectors,!1)&&(r??=[],He(i)?r.unshift(i):r.push(i))}return r}function iw(e,t,n,r,o,i){let s=Ye(e,t);sw(t[Y],s,i,e.value,n,r,o)}function sw(e,t,n,r,o,i,s){if(i==null)e.removeAttribute(t,o,n);else{let a=s==null?Qi(i):s(i,r||"",o);e.setAttribute(t,o,a,n)}}function aw(e,t,n,r,o,i){let s=i[t];if(s!==null)for(let a=0;a<s.length;a+=2){let c=s[a],u=s[a+1];Oc(r,n,c,u)}}function cw(e,t){let n=e[Ln],r=n?n.get(Ge,null):null;r&&r.handleError(t)}function Hu(e,t,n,r,o){let i=e.inputs?.[r],s=e.hostDirectiveInputs?.[r],a=!1;if(s)for(let c=0;c<s.length;c+=2){let u=s[c],l=s[c+1],d=t.data[u];Oc(d,n[u],l,o),a=!0}if(i)for(let c of i){let u=n[c],l=t.data[c];Oc(l,u,r,o),a=!0}return a}function uw(e,t){let n=qe(t,e),r=n[x];lw(r,n);let o=n[gt];o!==null&&n[Ar]===null&&(n[Ar]=np(o,n[Ln])),H(18),zu(r,n,n[fe]),H(19,n[fe])}function lw(e,t){for(let n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])}function zu(e,t,n){Eu(t);try{let r=e.viewQuery;r!==null&&bc(1,r,n);let o=e.template;o!==null&&bp(e,t,o,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),t[dt]?.finishViewCreation(e),e.staticContentQueries&&rp(e,t),e.staticViewQueries&&bc(2,e.viewQuery,n);let i=e.components;i!==null&&dw(t,i)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{t[b]&=-5,wu()}}function dw(e,t){for(let n=0;n<t.length;n++)uw(e,t[n])}function qu(e,t,n,r){let o=k(null);try{let i=t.tView,a=e[b]&4096?4096:16,c=Vu(e,i,n,a,null,t,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null),u=e[t.index];c[Yt]=u;let l=e[dt];return l!==null&&(c[dt]=l.createEmbeddedView(i)),zu(i,c,n),c}finally{k(o)}}function Fi(e,t){return!t||t.firstChild===null||Gh(e)}var fw;function Gu(e,t){return fw(e,t)}var ht=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(ht||{});function Wu(e){return(e.flags&32)===32}function An(e,t,n,r,o){if(r!=null){let i,s=!1;mt(r)?i=r:_t(r)&&(s=!0,r=r[gt]);let a=ze(r);e===0&&n!==null?o==null?Dp(t,n,a):Pi(t,n,a,o||null,!0):e===1&&n!==null?Pi(t,n,a,o||null,!0):e===2?qE(t,a,s):e===3&&t.destroyNode(a),i!=null&&Iw(t,e,i,n,o)}}function hw(e,t){Tp(e,t),t[gt]=null,t[Ie]=null}function pw(e,t,n,r,o,i){r[gt]=o,r[Ie]=t,ds(e,r,n,1,o,i)}function Tp(e,t){t[lt].changeDetectionScheduler?.notify(9),ds(e,t,t[Y],2,null,null)}function gw(e){let t=e[Or];if(!t)return Qa(e[x],e);for(;t;){let n=null;if(_t(t))n=t[Or];else{let r=t[Ee];r&&(n=r)}if(!n){for(;t&&!t[Pe]&&t!==e;)_t(t)&&Qa(t[x],t),t=t[ae];t===null&&(t=e),_t(t)&&Qa(t[x],t),n=t&&t[Pe]}t=n}}function Zu(e,t){let n=e[jn],r=n.indexOf(t);n.splice(r,1)}function Yu(e,t){if(Gn(t))return;let n=t[Y];n.destroyNode&&ds(e,t,n,3,null,null),gw(t)}function Qa(e,t){if(Gn(t))return;let n=k(null);try{t[b]&=-129,t[b]|=256,t[Re]&&Er(t[Re]),vw(e,t),mw(e,t),t[x].type===1&&t[Y].destroy();let r=t[Yt];if(r!==null&&mt(t[ae])){r!==t[ae]&&Zu(r,t);let o=t[dt];o!==null&&o.detachView(e)}wc(t)}finally{k(n)}}function mw(e,t){let n=e.cleanup,r=t[bi];if(n!==null)for(let s=0;s<n.length-1;s+=2)if(typeof n[s]=="string"){let a=n[s+3];a>=0?r[a]():r[-a].unsubscribe(),s+=2}else{let a=r[n[s+1]];n[s].call(a)}r!==null&&(t[bi]=null);let o=t[Tt];if(o!==null){t[Tt]=null;for(let s=0;s<o.length;s++){let a=o[s];a()}}let i=t[Qt];if(i!==null){t[Qt]=null;for(let s of i)s.destroy()}}function vw(e,t){let n;if(e!=null&&(n=e.destroyHooks)!=null)for(let r=0;r<n.length;r+=2){let o=t[n[r]];if(!(o instanceof en)){let i=n[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){let a=o[i[s]],c=i[s+1];H(4,a,c);try{c.call(a)}finally{H(5,a,c)}}else{H(4,o,i);try{i.call(o)}finally{H(5,o,i)}}}}}function _p(e,t,n){return yw(e,t.parent,n)}function yw(e,t,n){let r=t;for(;r!==null&&r.type&168;)t=r,r=t.parent;if(r===null)return n[gt];if(qn(r)){let{encapsulation:o}=e.data[r.directiveStart+r.componentOffset];if(o===We.None||o===We.Emulated)return null}return Ye(r,n)}function Mp(e,t,n){return Ew(e,t,n)}function Dw(e,t,n){return e.type&40?Ye(e,n):null}var Ew=Dw,yf;function ls(e,t,n,r){let o=_p(e,r,t),i=t[Y],s=r.parent||t[Ie],a=Mp(s,r,t);if(o!=null)if(Array.isArray(n))for(let c=0;c<n.length;c++)vf(i,o,n[c],a,!1);else vf(i,o,n,a,!1);yf!==void 0&&yf(i,r,t,n,o)}function Mr(e,t){if(t!==null){let n=t.type;if(n&3)return Ye(t,e);if(n&4)return kc(-1,e[t.index]);if(n&8){let r=t.child;if(r!==null)return Mr(e,r);{let o=e[t.index];return mt(o)?kc(-1,o):ze(o)}}else{if(n&128)return Mr(e,t.next);if(n&32)return Gu(t,e)()||ze(e[t.index]);{let r=Rp(e,t);if(r!==null){if(Array.isArray(r))return r[0];let o=Jt(e[xe]);return Mr(o,r)}else return Mr(e,t.next)}}}return null}function Rp(e,t){if(t!==null){let r=e[xe][Ie],o=t.projection;return r.projection[o]}return null}function kc(e,t){let n=Ee+e+1;if(n<t.length){let r=t[n],o=r[x].firstChild;if(o!==null)return Mr(r,o)}return t[Kt]}function Qu(e,t,n,r,o,i,s){for(;n!=null;){if(n.type===128){n=n.next;continue}let a=r[n.index],c=n.type;if(s&&t===0&&(a&&Yn(ze(a),r),n.flags|=2),!Wu(n))if(c&8)Qu(e,t,n.child,r,o,i,!1),An(t,e,o,a,i);else if(c&32){let u=Gu(n,r),l;for(;l=u();)An(t,e,o,l,i);An(t,e,o,a,i)}else c&16?Np(e,t,r,n,o,i):An(t,e,o,a,i);n=s?n.projectionNext:n.next}}function ds(e,t,n,r,o,i){Qu(n,r,e.firstChild,t,o,i,!1)}function ww(e,t,n){let r=t[Y],o=_p(e,n,t),i=n.parent||t[Ie],s=Mp(i,n,t);Np(r,0,t,n,o,s)}function Np(e,t,n,r,o,i){let s=n[xe],c=s[Ie].projection[r.projection];if(Array.isArray(c))for(let u=0;u<c.length;u++){let l=c[u];An(t,e,o,l,i)}else{let u=c,l=s[ae];Gh(r)&&(u.flags|=128),Qu(e,t,u,l,o,i,!0)}}function Iw(e,t,n,r,o){let i=n[Kt],s=ze(n);i!==s&&An(t,e,r,i,o);for(let a=Ee;a<n.length;a++){let c=n[a];ds(c[x],c,e,t,r,i)}}function Cw(e,t,n,r,o){if(t)o?e.addClass(n,r):e.removeClass(n,r);else{let i=r.indexOf("-")===-1?void 0:ht.DashCase;o==null?e.removeStyle(n,r,i):(typeof o=="string"&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=ht.Important),e.setStyle(n,r,o,i))}}function Li(e,t,n,r,o=!1){for(;n!==null;){if(n.type===128){n=o?n.projectionNext:n.next;continue}let i=t[n.index];i!==null&&r.push(ze(i)),mt(i)&&bw(i,r);let s=n.type;if(s&8)Li(e,t,n.child,r);else if(s&32){let a=Gu(n,t),c;for(;c=a();)r.push(c)}else if(s&16){let a=Rp(t,n);if(Array.isArray(a))r.push(...a);else{let c=Jt(t[xe]);Li(c[x],c,a,r,!0)}}n=o?n.projectionNext:n.next}return r}function bw(e,t){for(let n=Ee;n<e.length;n++){let r=e[n],o=r[x].firstChild;o!==null&&Li(r[x],r,o,t)}e[Kt]!==e[gt]&&t.push(e[Kt])}function xp(e){if(e[On]!==null){for(let t of e[On])t.impl.addSequence(t);e[On].length=0}}var Ap=[];function Sw(e){return e[Re]??Tw(e)}function Tw(e){let t=Ap.pop()??Object.create(Mw);return t.lView=e,t}function _w(e){e.lView[Re]!==e&&(e.lView=null,Ap.push(e))}var Mw=j(D({},Dn),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{Wn(e.lView)},consumerOnSignalRead(){this.lView[Re]=this}});function Rw(e){let t=e[Re]??Object.create(Nw);return t.lView=e,t}var Nw=j(D({},Dn),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{let t=Jt(e.lView);for(;t&&!Op(t[x]);)t=Jt(t);t&&gh(t)},consumerOnSignalRead(){this.lView[Re]=this}});function Op(e){return e.type!==2}function kp(e){if(e[Qt]===null)return;let t=!0;for(;t;){let n=!1;for(let r of e[Qt])r.dirty&&(n=!0,r.zone===null||Zone.current===r.zone?r.run():r.zone.run(()=>r.run()));t=n&&!!(e[b]&8192)}}var xw=100;function Pp(e,t=!0,n=0){let o=e[lt].rendererFactory,i=!1;i||o.begin?.();try{Aw(e,n)}catch(s){throw t&&cw(e,s),s}finally{i||o.end?.()}}function Aw(e,t){let n=Dh();try{_i(!0),Pc(e,t);let r=0;for(;ns(e);){if(r===xw)throw new y(103,!1);r++,Pc(e,1)}}finally{_i(n)}}function Ow(e,t,n,r){if(Gn(t))return;let o=t[b],i=!1,s=!1;Eu(t);let a=!0,c=null,u=null;i||(Op(e)?(u=Sw(t),c=Dr(u)):Ea()===null?(a=!1,u=Rw(t),c=Dr(u)):t[Re]&&(Er(t[Re]),t[Re]=null));try{ph(t),sD(e.bindingStartIndex),n!==null&&bp(e,t,n,2,r);let l=(o&3)===3;if(!i)if(l){let f=e.preOrderCheckHooks;f!==null&&gi(t,f,null)}else{let f=e.preOrderHooks;f!==null&&mi(t,f,0,null),Za(t,0)}if(s||kw(t),kp(t),Fp(t,0),e.contentQueries!==null&&rp(e,t),!i)if(l){let f=e.contentCheckHooks;f!==null&&gi(t,f)}else{let f=e.contentHooks;f!==null&&mi(t,f,1),Za(t,1)}Fw(e,t);let d=e.components;d!==null&&jp(t,d,0);let h=e.viewQuery;if(h!==null&&bc(2,h,r),!i)if(l){let f=e.viewCheckHooks;f!==null&&gi(t,f)}else{let f=e.viewHooks;f!==null&&mi(t,f,2),Za(t,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),t[Wa]){for(let f of t[Wa])f();t[Wa]=null}i||(xp(t),t[b]&=-73)}catch(l){throw i||Wn(t),l}finally{u!==null&&(To(u,c),a&&_w(u)),wu()}}function Fp(e,t){for(let n=Yh(e);n!==null;n=Qh(n))for(let r=Ee;r<n.length;r++){let o=n[r];Lp(o,t)}}function kw(e){for(let t=Yh(e);t!==null;t=Qh(t)){if(!(t[b]&2))continue;let n=t[jn];for(let r=0;r<n.length;r++){let o=n[r];gh(o)}}}function Pw(e,t,n){H(18);let r=qe(t,e);Lp(r,n),H(19,r[fe])}function Lp(e,t){hu(e)&&Pc(e,t)}function Pc(e,t){let r=e[x],o=e[b],i=e[Re],s=!!(t===0&&o&16);if(s||=!!(o&64&&t===0),s||=!!(o&1024),s||=!!(i?.dirty&&_o(i)),s||=!1,i&&(i.dirty=!1),e[b]&=-9217,s)Ow(r,e,r.template,e[fe]);else if(o&8192){kp(e),Fp(e,1);let a=r.components;a!==null&&jp(e,a,1),xp(e)}}function jp(e,t,n){for(let r=0;r<t.length;r++)Pw(e,t[r],n)}function Fw(e,t){let n=e.hostBindingOpCodes;if(n!==null)try{for(let r=0;r<n.length;r++){let o=n[r];if(o<0)Xt(~o);else{let i=o,s=n[++r],a=n[++r];cD(s,i);let c=t[i];H(24,c),a(2,c),H(25,c)}}}finally{Xt(-1)}}function Ku(e,t){let n=Dh()?64:1088;for(e[lt].changeDetectionScheduler?.notify(t);e;){e[b]|=n;let r=Jt(e);if(Ti(e)&&!r)return e;e=r}return null}function Vp(e,t,n,r){return[e,!0,0,t,null,r,null,n,null,null]}function Lw(e,t){let n=Ee+t;if(n<e.length)return e[n]}function Ju(e,t,n,r=!0){let o=t[x];if(Vw(o,t,e,n),r){let s=kc(n,e),a=t[Y],c=a.parentNode(e[Kt]);c!==null&&pw(o,e[Ie],a,t,c,s)}let i=t[Ar];i!==null&&i.firstChild!==null&&(i.firstChild=null)}function jw(e,t){let n=ji(e,t);return n!==void 0&&Yu(n[x],n),n}function ji(e,t){if(e.length<=Ee)return;let n=Ee+t,r=e[n];if(r){let o=r[Yt];o!==null&&o!==e&&Zu(o,r),t>0&&(e[n-1][Pe]=r[Pe]);let i=Ii(e,Ee+t);hw(r[x],r);let s=i[dt];s!==null&&s.detachView(i[x]),r[ae]=null,r[Pe]=null,r[b]&=-129}return r}function Vw(e,t,n,r){let o=Ee+r,i=n.length;r>0&&(n[o-1][Pe]=t),r<i-Ee?(t[Pe]=n[o],Qf(n,Ee+r,t)):(n.push(t),t[Pe]=null),t[ae]=n;let s=t[Yt];s!==null&&n!==s&&Up(s,t);let a=t[dt];a!==null&&a.insertView(e),fc(t),t[b]|=128}function Up(e,t){let n=e[jn],r=t[ae];if(_t(r))e[b]|=2;else{let o=r[ae][xe];t[xe]!==o&&(e[b]|=2)}n===null?e[jn]=[t]:n.push(t)}var Fr=class{_lView;_cdRefInjectingView;notifyErrorHandler;_appRef=null;_attachedToViewContainer=!1;get rootNodes(){let t=this._lView,n=t[x];return Li(n,t,n.firstChild,[])}constructor(t,n,r=!0){this._lView=t,this._cdRefInjectingView=n,this.notifyErrorHandler=r}get context(){return this._lView[fe]}set context(t){this._lView[fe]=t}get destroyed(){return Gn(this._lView)}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let t=this._lView[ae];if(mt(t)){let n=t[Si],r=n?n.indexOf(this):-1;r>-1&&(ji(t,r),Ii(n,r))}this._attachedToViewContainer=!1}Yu(this._lView[x],this._lView)}onDestroy(t){mh(this._lView,t)}markForCheck(){Ku(this._cdRefInjectingView||this._lView,4)}detach(){this._lView[b]&=-129}reattach(){fc(this._lView),this._lView[b]|=128}detectChanges(){this._lView[b]|=1024,Pp(this._lView,this.notifyErrorHandler)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new y(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let t=Ti(this._lView),n=this._lView[Yt];n!==null&&!t&&Zu(n,this._lView),Tp(this._lView[x],this._lView)}attachToAppRef(t){if(this._attachedToViewContainer)throw new y(902,!1);this._appRef=t;let n=Ti(this._lView),r=this._lView[Yt];r!==null&&!n&&Up(r,this._lView),fc(this._lView)}};var nn=(()=>{class e{static __NG_ELEMENT_ID__=$w}return e})(),Uw=nn,Bw=class extends Uw{_declarationLView;_declarationTContainer;elementRef;constructor(t,n,r){super(),this._declarationLView=t,this._declarationTContainer=n,this.elementRef=r}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(t,n){return this.createEmbeddedViewImpl(t,n)}createEmbeddedViewImpl(t,n,r){let o=qu(this._declarationLView,this._declarationTContainer,t,{embeddedViewInjector:n,dehydratedView:r});return new Fr(o)}};function $w(){return fs(he(),P())}function fs(e,t){return e.type&4?new Bw(t,e,Zn(e,t)):null}function Jn(e,t,n,r,o){let i=e.data[t];if(i===null)i=Hw(e,t,n,r,o),aD()&&(i.flags|=32);else if(i.type&64){i.type=n,i.value=r,i.attrs=o;let s=rD();i.injectorIndex=s===null?-1:s.injectorIndex}return Rt(i,!0),i}function Hw(e,t,n,r,o){let i=yh(),s=vu(),a=s?i:i&&i.parent,c=e.data[t]=qw(e,a,n,t,r,o);return zw(e,c,i,s),c}function zw(e,t,n,r){e.firstChild===null&&(e.firstChild=t),n!==null&&(r?n.child==null&&t.parent!==null&&(n.child=t):n.next===null&&(n.next=t,t.prev=n))}function qw(e,t,n,r,o,i){let s=t?t.injectorIndex:-1,a=0;return vh()&&(a|=128),{type:n,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:null,inputs:null,hostDirectiveInputs:null,outputs:null,hostDirectiveOutputs:null,directiveToIndex:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}var ik=new RegExp(`^(\\d+)*(${KD}|${QD})*(.*)`);var Gw=()=>null;function Vi(e,t){return Gw(e,t)}var Ww=class{},Bp=class{},Fc=class{resolveComponentFactory(t){throw Error(`No component factory found for ${De(t)}.`)}},hs=class{static NULL=new Fc},Bn=class{},Xu=(()=>{class e{destroyNode=null;static __NG_ELEMENT_ID__=()=>Zw()}return e})();function Zw(){let e=P(),t=he(),n=qe(t.index,e);return(_t(n)?n:e)[Y]}var Yw=(()=>{class e{static \u0275prov=m({token:e,providedIn:"root",factory:()=>null})}return e})();var Ka={},Lc=class{injector;parentInjector;constructor(t,n){this.injector=t,this.parentInjector=n}get(t,n,r){r=Ki(r);let o=this.injector.get(t,Ka,r);return o!==Ka||n===Ka?o:this.parentInjector.get(t,n,r)}};function jc(e,t,n){let r=n?e.styles:null,o=n?e.classes:null,i=0;if(t!==null)for(let s=0;s<t.length;s++){let a=t[s];if(typeof a=="number")i=a;else if(i==1)o=oc(o,a);else if(i==2){let c=a,u=t[++s];r=oc(r,c+": "+u+";")}}n?e.styles=r:e.stylesWithoutHost=r,n?e.classes=o:e.classesWithoutHost=o}function Se(e,t=N.Default){let n=P();if(n===null)return E(e,t);let r=he();return Ph(r,n,le(e),t)}function $p(){let e="invalid";throw new Error(e)}function el(e,t,n,r,o){let i=r===null?null:{"":-1},s=o(e,n);if(s!==null){let a,c=null,u=null,l=Kw(s);l===null?a=s:[a,c,u]=l,eI(e,t,n,a,i,c,u)}i!==null&&r!==null&&Qw(n,r,i)}function Qw(e,t,n){let r=e.localNames=[];for(let o=0;o<t.length;o+=2){let i=n[t[o+1]];if(i==null)throw new y(-301,!1);r.push(t[o],i)}}function Kw(e){let t=null,n=!1;for(let s=0;s<e.length;s++){let a=e[s];if(s===0&&He(a)&&(t=a),a.findHostDirectiveDefs!==null){n=!0;break}}if(!n)return null;let r=null,o=null,i=null;for(let s of e)s.findHostDirectiveDefs!==null&&(r??=[],o??=new Map,i??=new Map,Jw(s,r,i,o)),s===t&&(r??=[],r.push(s));return r!==null?(r.push(...t===null?e:e.slice(1)),[r,o,i]):null}function Jw(e,t,n,r){let o=t.length;e.findHostDirectiveDefs(e,t,r),n.set(e,[o,t.length-1])}function Xw(e,t,n){t.componentOffset=n,(e.components??=[]).push(t.index)}function eI(e,t,n,r,o,i,s){let a=r.length,c=!1;for(let h=0;h<a;h++){let f=r[h];!c&&He(f)&&(c=!0,Xw(e,n,h)),mc(xi(n,t),e,f.type)}sI(n,e.data.length,a);for(let h=0;h<a;h++){let f=r[h];f.providersResolver&&f.providersResolver(f)}let u=!1,l=!1,d=Ip(e,t,a,null);a>0&&(n.directiveToIndex=new Map);for(let h=0;h<a;h++){let f=r[h];if(n.mergedAttrs=Un(n.mergedAttrs,f.hostAttrs),nI(e,n,t,d,f),iI(d,f,o),s!==null&&s.has(f)){let[v,I]=s.get(f);n.directiveToIndex.set(f.type,[d,v+n.directiveStart,I+n.directiveStart])}else(i===null||!i.has(f))&&n.directiveToIndex.set(f.type,d);f.contentQueries!==null&&(n.flags|=4),(f.hostBindings!==null||f.hostAttrs!==null||f.hostVars!==0)&&(n.flags|=64);let g=f.type.prototype;!u&&(g.ngOnChanges||g.ngOnInit||g.ngDoCheck)&&((e.preOrderHooks??=[]).push(n.index),u=!0),!l&&(g.ngOnChanges||g.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(n.index),l=!0),d++}tI(e,n,i)}function tI(e,t,n){for(let r=t.directiveStart;r<t.directiveEnd;r++){let o=e.data[r];if(n===null||!n.has(o))Df(0,t,o,r),Df(1,t,o,r),wf(t,r,!1);else{let i=n.get(o);Ef(0,t,i,r),Ef(1,t,i,r),wf(t,r,!0)}}}function Df(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s;e===0?s=t.inputs??={}:s=t.outputs??={},s[i]??=[],s[i].push(r),Hp(t,i)}}function Ef(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s=o[i],a;e===0?a=t.hostDirectiveInputs??={}:a=t.hostDirectiveOutputs??={},a[s]??=[],a[s].push(r,i),Hp(t,s)}}function Hp(e,t){t==="class"?e.flags|=8:t==="style"&&(e.flags|=16)}function wf(e,t,n){let{attrs:r,inputs:o,hostDirectiveInputs:i}=e;if(r===null||!n&&o===null||n&&i===null||Lu(e)){e.initialInputs??=[],e.initialInputs.push(null);return}let s=null,a=0;for(;a<r.length;){let c=r[a];if(c===0){a+=4;continue}else if(c===5){a+=2;continue}else if(typeof c=="number")break;if(!n&&o.hasOwnProperty(c)){let u=o[c];for(let l of u)if(l===t){s??=[],s.push(c,r[a+1]);break}}else if(n&&i.hasOwnProperty(c)){let u=i[c];for(let l=0;l<u.length;l+=2)if(u[l]===t){s??=[],s.push(u[l+1],r[a+1]);break}}a+=2}e.initialInputs??=[],e.initialInputs.push(s)}function nI(e,t,n,r,o){e.data[r]=o;let i=o.factory||(o.factory=Wt(o.type,!0)),s=new en(i,He(o),Se);e.blueprint[r]=s,n[r]=s,rI(e,t,r,Ip(e,n,o.hostVars,Ke),o)}function rI(e,t,n,r,o){let i=o.hostBindings;if(i){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~t.index;oI(s)!=a&&s.push(a),s.push(n,r,i)}}function oI(e){let t=e.length;for(;t>0;){let n=e[--t];if(typeof n=="number"&&n<0)return n}return 0}function iI(e,t,n){if(n){if(t.exportAs)for(let r=0;r<t.exportAs.length;r++)n[t.exportAs[r]]=e;He(t)&&(n[""]=e)}}function sI(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}function zp(e,t,n,r,o,i,s,a){let c=t.consts,u=Vn(c,s),l=Jn(t,e,2,r,u);return i&&el(t,n,l,Vn(c,a),o),l.mergedAttrs=Un(l.mergedAttrs,l.attrs),l.attrs!==null&&jc(l,l.attrs,!1),l.mergedAttrs!==null&&jc(l,l.mergedAttrs,!0),t.queries!==null&&t.queries.elementStart(t,l),l}function qp(e,t){Cu(e,t),du(t)&&e.queries.elementEnd(t)}var Ui=class extends hs{ngModule;constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){let n=Mt(t);return new rn(n,this.ngModule)}};function aI(e){return Object.keys(e).map(t=>{let[n,r,o]=e[t],i={propName:n,templateName:t,isSignal:(r&cs.SignalBased)!==0};return o&&(i.transform=o),i})}function cI(e){return Object.keys(e).map(t=>({propName:e[t],templateName:t}))}function uI(e,t,n){let r=t instanceof se?t:t?.injector;return r&&e.getStandaloneInjector!==null&&(r=e.getStandaloneInjector(r)||r),r?new Lc(n,r):n}function lI(e){let t=e.get(Bn,null);if(t===null)throw new y(407,!1);let n=e.get(Yw,null),r=e.get(tn,null);return{rendererFactory:t,sanitizer:n,changeDetectionScheduler:r}}function dI(e,t){let n=(e.selectors[0][0]||"div").toLowerCase();return yp(t,n,n==="svg"?lh:n==="math"?Gy:null)}var rn=class extends Bp{componentDef;ngModule;selector;componentType;ngContentSelectors;isBoundToModule;cachedInputs=null;cachedOutputs=null;get inputs(){return this.cachedInputs??=aI(this.componentDef.inputs),this.cachedInputs}get outputs(){return this.cachedOutputs??=cI(this.componentDef.outputs),this.cachedOutputs}constructor(t,n){super(),this.componentDef=t,this.ngModule=n,this.componentType=t.type,this.selector=UE(t.selectors),this.ngContentSelectors=t.ngContentSelectors??[],this.isBoundToModule=!!n}create(t,n,r,o){H(22);let i=k(null);try{let s=this.componentDef,a=r?["ng-version","19.2.14"]:BE(this.componentDef.selectors[0]),c=ju(0,null,null,1,0,null,null,null,null,[a],null),u=uI(s,o||this.ngModule,t),l=lI(u),d=l.rendererFactory.createRenderer(null,s),h=r?KE(d,r,s.encapsulation,u):dI(s,d),f=Vu(null,c,null,512|wp(s),null,null,l,d,u,null,np(h,u,!0));f[ne]=h,Eu(f);let g=null;try{let v=zp(ne,c,f,"#host",()=>[this.componentDef],!0,0);h&&(Ep(d,h,v),Yn(h,f)),us(c,f,v),Fu(c,v,f),qp(c,v),n!==void 0&&fI(v,this.ngContentSelectors,n),g=qe(v.index,f),f[fe]=g[fe],zu(c,f,null)}catch(v){throw g!==null&&wc(g),wc(f),v}finally{H(23),wu()}return new Vc(this.componentType,f)}finally{k(i)}}},Vc=class extends Ww{_rootLView;instance;hostView;changeDetectorRef;componentType;location;previousInputValues=null;_tNode;constructor(t,n){super(),this._rootLView=n,this._tNode=fu(n[x],ne),this.location=Zn(this._tNode,n),this.instance=qe(this._tNode.index,n)[fe],this.hostView=this.changeDetectorRef=new Fr(n,void 0,!1),this.componentType=t}setInput(t,n){let r=this._tNode;if(this.previousInputValues??=new Map,this.previousInputValues.has(t)&&Object.is(this.previousInputValues.get(t),n))return;let o=this._rootLView,i=Hu(r,o[x],o,t,n);this.previousInputValues.set(t,n);let s=qe(r.index,o);Ku(s,1)}get injector(){return new Gt(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}};function fI(e,t,n){let r=e.projection=[];for(let o=0;o<t.length;o++){let i=n[o];r.push(i!=null&&i.length?Array.from(i):null)}}var Et=(()=>{class e{static __NG_ELEMENT_ID__=hI}return e})();function hI(){let e=he();return Wp(e,P())}var pI=Et,Gp=class extends pI{_lContainer;_hostTNode;_hostLView;constructor(t,n,r){super(),this._lContainer=t,this._hostTNode=n,this._hostLView=r}get element(){return Zn(this._hostTNode,this._hostLView)}get injector(){return new Gt(this._hostTNode,this._hostLView)}get parentInjector(){let t=bu(this._hostTNode,this._hostLView);if(Rh(t)){let n=Ri(t,this._hostLView),r=Mi(t),o=n[x].data[r+8];return new Gt(o,n)}else return new Gt(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){let n=If(this._lContainer);return n!==null&&n[t]||null}get length(){return this._lContainer.length-Ee}createEmbeddedView(t,n,r){let o,i;typeof r=="number"?o=r:r!=null&&(o=r.index,i=r.injector);let s=Vi(this._lContainer,t.ssrId),a=t.createEmbeddedViewImpl(n||{},i,s);return this.insertImpl(a,o,Fi(this._hostTNode,s)),a}createComponent(t,n,r,o,i){let s=t&&!$y(t),a;if(s)a=n;else{let g=n||{};a=g.index,r=g.injector,o=g.projectableNodes,i=g.environmentInjector||g.ngModuleRef}let c=s?t:new rn(Mt(t)),u=r||this.parentInjector;if(!i&&c.ngModule==null){let v=(s?u:this.parentInjector).get(se,null);v&&(i=v)}let l=Mt(c.componentType??{}),d=Vi(this._lContainer,l?.id??null),h=d?.firstChild??null,f=c.create(u,o,h,i);return this.insertImpl(f.hostView,a,Fi(this._hostTNode,d)),f}insert(t,n){return this.insertImpl(t,n,!0)}insertImpl(t,n,r){let o=t._lView;if(Zy(o)){let a=this.indexOf(t);if(a!==-1)this.detach(a);else{let c=o[ae],u=new Gp(c,c[Ie],c[ae]);u.detach(u.indexOf(t))}}let i=this._adjustIndex(n),s=this._lContainer;return Ju(s,o,i,r),t.attachToViewContainerRef(),Qf(Ja(s),i,t),t}move(t,n){return this.insert(t,n)}indexOf(t){let n=If(this._lContainer);return n!==null?n.indexOf(t):-1}remove(t){let n=this._adjustIndex(t,-1),r=ji(this._lContainer,n);r&&(Ii(Ja(this._lContainer),n),Yu(r[x],r))}detach(t){let n=this._adjustIndex(t,-1),r=ji(this._lContainer,n);return r&&Ii(Ja(this._lContainer),n)!=null?new Fr(r):null}_adjustIndex(t,n=0){return t??this.length+n}};function If(e){return e[Si]}function Ja(e){return e[Si]||(e[Si]=[])}function Wp(e,t){let n,r=t[e.index];return mt(r)?n=r:(n=Vp(r,t,null,e),t[e.index]=n,Uu(t,n)),mI(n,t,e,r),new Gp(n,e,t)}function gI(e,t){let n=e[Y],r=n.createComment(""),o=Ye(t,e),i=n.parentNode(o);return Pi(n,i,r,n.nextSibling(o),!1),r}var mI=DI,vI=()=>!1;function yI(e,t,n){return vI(e,t,n)}function DI(e,t,n,r){if(e[Kt])return;let o;n.type&8?o=ze(r):o=gI(t,n),e[Kt]=o}var Uc=class e{queryList;matches=null;constructor(t){this.queryList=t}clone(){return new e(this.queryList)}setDirty(){this.queryList.setDirty()}},Bc=class e{queries;constructor(t=[]){this.queries=t}createEmbeddedView(t){let n=t.queries;if(n!==null){let r=t.contentQueries!==null?t.contentQueries[0]:n.length,o=[];for(let i=0;i<r;i++){let s=n.getByIndex(i),a=this.queries[s.indexInDeclarationView];o.push(a.clone())}return new e(o)}return null}insertView(t){this.dirtyQueriesWithMatches(t)}detachView(t){this.dirtyQueriesWithMatches(t)}finishViewCreation(t){this.dirtyQueriesWithMatches(t)}dirtyQueriesWithMatches(t){for(let n=0;n<this.queries.length;n++)nl(t,n).matches!==null&&this.queries[n].setDirty()}},Bi=class{flags;read;predicate;constructor(t,n,r=null){this.flags=n,this.read=r,typeof t=="string"?this.predicate=SI(t):this.predicate=t}},$c=class e{queries;constructor(t=[]){this.queries=t}elementStart(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].elementStart(t,n)}elementEnd(t){for(let n=0;n<this.queries.length;n++)this.queries[n].elementEnd(t)}embeddedTView(t){let n=null;for(let r=0;r<this.length;r++){let o=n!==null?n.length:0,i=this.getByIndex(r).embeddedTView(t,o);i&&(i.indexInDeclarationView=r,n!==null?n.push(i):n=[i])}return n!==null?new e(n):null}template(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].template(t,n)}getByIndex(t){return this.queries[t]}get length(){return this.queries.length}track(t){this.queries.push(t)}},Hc=class e{metadata;matches=null;indexInDeclarationView=-1;crossesNgTemplate=!1;_declarationNodeIndex;_appliesToNextNode=!0;constructor(t,n=-1){this.metadata=t,this._declarationNodeIndex=n}elementStart(t,n){this.isApplyingToNode(n)&&this.matchTNode(t,n)}elementEnd(t){this._declarationNodeIndex===t.index&&(this._appliesToNextNode=!1)}template(t,n){this.elementStart(t,n)}embeddedTView(t,n){return this.isApplyingToNode(t)?(this.crossesNgTemplate=!0,this.addMatch(-t.index,n),new e(this.metadata)):null}isApplyingToNode(t){if(this._appliesToNextNode&&(this.metadata.flags&1)!==1){let n=this._declarationNodeIndex,r=t.parent;for(;r!==null&&r.type&8&&r.index!==n;)r=r.parent;return n===(r!==null?r.index:-1)}return this._appliesToNextNode}matchTNode(t,n){let r=this.metadata.predicate;if(Array.isArray(r))for(let o=0;o<r.length;o++){let i=r[o];this.matchTNodeWithReadOption(t,n,EI(n,i)),this.matchTNodeWithReadOption(t,n,vi(n,t,i,!1,!1))}else r===nn?n.type&4&&this.matchTNodeWithReadOption(t,n,-1):this.matchTNodeWithReadOption(t,n,vi(n,t,r,!1,!1))}matchTNodeWithReadOption(t,n,r){if(r!==null){let o=this.metadata.read;if(o!==null)if(o===an||o===Et||o===nn&&n.type&4)this.addMatch(n.index,-2);else{let i=vi(n,t,o,!1,!1);i!==null&&this.addMatch(n.index,i)}else this.addMatch(n.index,r)}}addMatch(t,n){this.matches===null?this.matches=[t,n]:this.matches.push(t,n)}};function EI(e,t){let n=e.localNames;if(n!==null){for(let r=0;r<n.length;r+=2)if(n[r]===t)return n[r+1]}return null}function wI(e,t){return e.type&11?Zn(e,t):e.type&4?fs(e,t):null}function II(e,t,n,r){return n===-1?wI(t,e):n===-2?CI(e,t,r):kr(e,e[x],n,t)}function CI(e,t,n){if(n===an)return Zn(t,e);if(n===nn)return fs(t,e);if(n===Et)return Wp(t,e)}function Zp(e,t,n,r){let o=t[dt].queries[r];if(o.matches===null){let i=e.data,s=n.matches,a=[];for(let c=0;s!==null&&c<s.length;c+=2){let u=s[c];if(u<0)a.push(null);else{let l=i[u];a.push(II(t,l,s[c+1],n.metadata.read))}}o.matches=a}return o.matches}function zc(e,t,n,r){let o=e.queries.getByIndex(n),i=o.matches;if(i!==null){let s=Zp(e,t,o,n);for(let a=0;a<i.length;a+=2){let c=i[a];if(c>0)r.push(s[a/2]);else{let u=i[a+1],l=t[-c];for(let d=Ee;d<l.length;d++){let h=l[d];h[Yt]===h[ae]&&zc(h[x],h,u,r)}if(l[jn]!==null){let d=l[jn];for(let h=0;h<d.length;h++){let f=d[h];zc(f[x],f,u,r)}}}}}return r}function tl(e,t){return e[dt].queries[t].queryList}function Yp(e,t,n){let r=new Ec((n&4)===4);return Ky(e,t,r,r.destroy),(t[dt]??=new Bc).queries.push(new Uc(r))-1}function bI(e,t,n){let r=Q();return r.firstCreatePass&&(Kp(r,new Bi(e,t,n),-1),(t&2)===2&&(r.staticViewQueries=!0)),Yp(r,P(),t)}function Qp(e,t,n,r){let o=Q();if(o.firstCreatePass){let i=he();Kp(o,new Bi(t,n,r),i.index),TI(o,e),(n&2)===2&&(o.staticContentQueries=!0)}return Yp(o,P(),n)}function SI(e){return e.split(",").map(t=>t.trim())}function Kp(e,t,n){e.queries===null&&(e.queries=new $c),e.queries.track(new Hc(t,n))}function TI(e,t){let n=e.contentQueries||(e.contentQueries=[]),r=n.length?n[n.length-1]:-1;t!==r&&n.push(e.queries.length-1,t)}function nl(e,t){return e.queries.getByIndex(t)}function Jp(e,t){let n=e[x],r=nl(n,t);return r.crossesNgTemplate?zc(n,e,t,[]):Zp(n,e,r,t)}function Xp(e,t,n){let r,o=No(()=>{r._dirtyCounter();let i=NI(r,e);if(t&&i===void 0)throw new y(-951,!1);return i});return r=o[pe],r._dirtyCounter=$D(0),r._flatValue=void 0,o}function _I(e){return Xp(!0,!1,e)}function MI(e){return Xp(!0,!0,e)}function RI(e,t){let n=e[pe];n._lView=P(),n._queryIndex=t,n._queryList=tl(n._lView,t),n._queryList.onDirty(()=>n._dirtyCounter.update(r=>r+1))}function NI(e,t){let n=e._lView,r=e._queryIndex;if(n===void 0||r===void 0||n[b]&4)return t?void 0:de;let o=tl(n,r),i=Jp(n,r);return o.reset(i,qh),t?o.first:o._changesDetected||e._flatValue===void 0?e._flatValue=o.toArray():e._flatValue}function Cf(e,t){return _I(t)}function xI(e,t){return MI(t)}var lk=(Cf.required=xI,Cf);function AI(e){let t=[],n=new Map;function r(o){let i=n.get(o);if(!i){let s=e(o);n.set(o,i=s.then(FI))}return i}return $i.forEach((o,i)=>{let s=[];o.templateUrl&&s.push(r(o.templateUrl).then(u=>{o.template=u}));let a=typeof o.styles=="string"?[o.styles]:o.styles||[];if(o.styles=a,o.styleUrl&&o.styleUrls?.length)throw new Error("@Component cannot define both `styleUrl` and `styleUrls`. Use `styleUrl` if the component has one stylesheet, or `styleUrls` if it has multiple");if(o.styleUrls?.length){let u=o.styles.length,l=o.styleUrls;o.styleUrls.forEach((d,h)=>{a.push(""),s.push(r(d).then(f=>{a[u+h]=f,l.splice(l.indexOf(d),1),l.length==0&&(o.styleUrls=void 0)}))})}else o.styleUrl&&s.push(r(o.styleUrl).then(u=>{a.push(u),o.styleUrl=void 0}));let c=Promise.all(s).then(()=>LI(i));t.push(c)}),kI(),Promise.all(t).then(()=>{})}var $i=new Map,OI=new Set;function kI(){let e=$i;return $i=new Map,e}function PI(){return $i.size===0}function FI(e){return typeof e=="string"?e:e.text()}function LI(e){OI.delete(e)}var $n=class{},rl=class{};var Hi=class extends $n{ngModuleType;_parent;_bootstrapComponents=[];_r3Injector;instance;destroyCbs=[];componentFactoryResolver=new Ui(this);constructor(t,n,r,o=!0){super(),this.ngModuleType=t,this._parent=n;let i=Xf(t);this._bootstrapComponents=gp(i.bootstrap),this._r3Injector=jh(t,n,[{provide:$n,useValue:this},{provide:hs,useValue:this.componentFactoryResolver},...r],De(t),new Set(["environment"])),o&&this.resolveInjectorInitializers()}resolveInjectorInitializers(){this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(this.ngModuleType)}get injector(){return this._r3Injector}destroy(){let t=this._r3Injector;!t.destroyed&&t.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(t){this.destroyCbs.push(t)}},zi=class extends rl{moduleType;constructor(t){super(),this.moduleType=t}create(t){return new Hi(this.moduleType,t,[])}};function jI(e,t,n){return new Hi(e,t,n,!1)}var qc=class extends $n{injector;componentFactoryResolver=new Ui(this);instance=null;constructor(t){super();let n=new xr([...t.providers,{provide:$n,useValue:this},{provide:hs,useValue:this.componentFactoryResolver}],t.parent||Xi(),t.debugName,new Set(["environment"]));this.injector=n,t.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(t){this.injector.onDestroy(t)}};function qr(e,t,n=null){return new qc({providers:e,parent:t,debugName:n,runEnvironmentInitializers:!0}).injector}var VI=(()=>{class e{_injector;cachedInjectors=new Map;constructor(n){this._injector=n}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n)){let r=eh(!1,n.type),o=r.length>0?qr([r],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n,o)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(let n of this.cachedInjectors.values())n!==null&&n.destroy()}finally{this.cachedInjectors.clear()}}static \u0275prov=m({token:e,providedIn:"environment",factory:()=>new e(E(se))})}return e})();function eg(e){return Vr(()=>{let t=ng(e),n=j(D({},t),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===Wh.OnPush,directiveDefs:null,pipeDefs:null,dependencies:t.standalone&&e.dependencies||null,getStandaloneInjector:t.standalone?o=>o.get(VI).getOrCreateStandaloneInjector(n):null,getExternalStyles:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||We.Emulated,styles:e.styles||de,_:null,schemas:e.schemas||null,tView:null,id:""});t.standalone&&cn("NgStandalone"),rg(n);let r=e.dependencies;return n.directiveDefs=bf(r,!1),n.pipeDefs=bf(r,!0),n.id=zI(n),n})}function UI(e){return Mt(e)||Ry(e)}function BI(e){return e!==null}function Je(e){return Vr(()=>({type:e.type,bootstrap:e.bootstrap||de,declarations:e.declarations||de,imports:e.imports||de,exports:e.exports||de,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function $I(e,t){if(e==null)return Zt;let n={};for(let r in e)if(e.hasOwnProperty(r)){let o=e[r],i,s,a,c;Array.isArray(o)?(a=o[0],i=o[1],s=o[2]??i,c=o[3]||null):(i=o,s=o,a=cs.None,c=null),n[i]=[r,a,c],t[i]=s}return n}function HI(e){if(e==null)return Zt;let t={};for(let n in e)e.hasOwnProperty(n)&&(t[e[n]]=n);return t}function ln(e){return Vr(()=>{let t=ng(e);return rg(t),t})}function tg(e){return{type:e.type,name:e.name,factory:null,pure:e.pure!==!1,standalone:e.standalone??!0,onDestroy:e.type.prototype.ngOnDestroy||null}}function ng(e){let t={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:t,inputConfig:e.inputs||Zt,exportAs:e.exportAs||null,standalone:e.standalone??!0,signals:e.signals===!0,selectors:e.selectors||de,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:$I(e.inputs,t),outputs:HI(e.outputs),debugInfo:null}}function rg(e){e.features?.forEach(t=>t(e))}function bf(e,t){if(!e)return null;let n=t?Ny:UI;return()=>(typeof e=="function"?e():e).map(r=>n(r)).filter(BI)}function zI(e){let t=0,n=typeof e.consts=="function"?"":e.consts,r=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,n,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery];for(let i of r.join("|"))t=Math.imul(31,t)+i.charCodeAt(0)<<0;return t+=2147483648,"c"+t}function qI(e){return Object.getPrototypeOf(e.prototype).constructor}function GI(e){let t=qI(e.type),n=!0,r=[e];for(;t;){let o;if(He(e))o=t.\u0275cmp||t.\u0275dir;else{if(t.\u0275cmp)throw new y(903,!1);o=t.\u0275dir}if(o){if(n){r.push(o);let s=e;s.inputs=Xa(e.inputs),s.declaredInputs=Xa(e.declaredInputs),s.outputs=Xa(e.outputs);let a=o.hostBindings;a&&KI(e,a);let c=o.viewQuery,u=o.contentQueries;if(c&&YI(e,c),u&&QI(e,u),WI(e,o),sy(e.outputs,o.outputs),He(o)&&o.data.animation){let l=e.data;l.animation=(l.animation||[]).concat(o.data.animation)}}let i=o.features;if(i)for(let s=0;s<i.length;s++){let a=i[s];a&&a.ngInherit&&a(e),a===GI&&(n=!1)}}t=Object.getPrototypeOf(t)}ZI(r)}function WI(e,t){for(let n in t.inputs){if(!t.inputs.hasOwnProperty(n)||e.inputs.hasOwnProperty(n))continue;let r=t.inputs[n];r!==void 0&&(e.inputs[n]=r,e.declaredInputs[n]=t.declaredInputs[n])}}function ZI(e){let t=0,n=null;for(let r=e.length-1;r>=0;r--){let o=e[r];o.hostVars=t+=o.hostVars,o.hostAttrs=Un(o.hostAttrs,n=Un(n,o.hostAttrs))}}function Xa(e){return e===Zt?{}:e===de?[]:e}function YI(e,t){let n=e.viewQuery;n?e.viewQuery=(r,o)=>{t(r,o),n(r,o)}:e.viewQuery=t}function QI(e,t){let n=e.contentQueries;n?e.contentQueries=(r,o,i)=>{t(r,o,i),n(r,o,i)}:e.contentQueries=t}function KI(e,t){let n=e.hostBindings;n?e.hostBindings=(r,o)=>{t(r,o),n(r,o)}:e.hostBindings=t}function og(e){return XI(e)?Array.isArray(e)||!(e instanceof Map)&&Symbol.iterator in e:!1}function JI(e,t){if(Array.isArray(e))for(let n=0;n<e.length;n++)t(e[n]);else{let n=e[Symbol.iterator](),r;for(;!(r=n.next()).done;)t(r.value)}}function XI(e){return e!==null&&(typeof e=="function"||typeof e=="object")}function eC(e,t,n){return e[t]=n}function xt(e,t,n){let r=e[t];return Object.is(r,n)?!1:(e[t]=n,!0)}function tC(e,t,n,r,o,i,s,a,c){let u=t.consts,l=Jn(t,e,4,s||null,a||null);mu()&&el(t,n,l,Vn(u,c),$u),l.mergedAttrs=Un(l.mergedAttrs,l.attrs),Cu(t,l);let d=l.tView=ju(2,l,r,o,i,t.directiveRegistry,t.pipeRegistry,null,t.schemas,u,null);return t.queries!==null&&(t.queries.template(t,l),d.queries=t.queries.embeddedTView(l)),l}function ig(e,t,n,r,o,i,s,a,c,u){let l=n+ne,d=t.firstCreatePass?tC(l,t,e,r,o,i,s,a,c):t.data[l];Rt(d,!1);let h=rC(t,e,d,n);os()&&ls(t,e,h,d),Yn(h,e);let f=Vp(h,e,h,d);return e[l]=f,Uu(e,f),yI(f,d,e),ts(d)&&us(t,e,d),c!=null&&Bu(e,d,u),d}function nC(e,t,n,r,o,i,s,a){let c=P(),u=Q(),l=Vn(u.consts,i);return ig(c,u,e,t,n,r,o,l,s,a),nC}var rC=oC;function oC(e,t,n,r){return is(!0),t[Y].createComment("")}var ol=(()=>{class e{log(n){console.log(n)}warn(n){console.warn(n)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=m({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})();var il=new w(""),Gr=new w(""),ps=(()=>{class e{_ngZone;registry;_isZoneStable=!0;_callbacks=[];_taskTrackingZone=null;_destroyRef;constructor(n,r,o){this._ngZone=n,this.registry=r,uu()&&(this._destroyRef=p(Nt,{optional:!0})??void 0),sl||(iC(o),o.addToWindow(r)),this._watchAngularEvents(),n.run(()=>{this._taskTrackingZone=typeof Zone>"u"?null:Zone.current.get("TaskTrackingZone")})}_watchAngularEvents(){let n=this._ngZone.onUnstable.subscribe({next:()=>{this._isZoneStable=!1}}),r=this._ngZone.runOutsideAngular(()=>this._ngZone.onStable.subscribe({next:()=>{U.assertNotInAngularZone(),queueMicrotask(()=>{this._isZoneStable=!0,this._runCallbacksIfReady()})}}));this._destroyRef?.onDestroy(()=>{n.unsubscribe(),r.unsubscribe()})}isStable(){return this._isZoneStable&&!this._ngZone.hasPendingMacrotasks}_runCallbacksIfReady(){if(this.isStable())queueMicrotask(()=>{for(;this._callbacks.length!==0;){let n=this._callbacks.pop();clearTimeout(n.timeoutId),n.doneCb()}});else{let n=this.getPendingTasks();this._callbacks=this._callbacks.filter(r=>r.updateCb&&r.updateCb(n)?(clearTimeout(r.timeoutId),!1):!0)}}getPendingTasks(){return this._taskTrackingZone?this._taskTrackingZone.macroTasks.map(n=>({source:n.source,creationLocation:n.creationLocation,data:n.data})):[]}addCallback(n,r,o){let i=-1;r&&r>0&&(i=setTimeout(()=>{this._callbacks=this._callbacks.filter(s=>s.timeoutId!==i),n()},r)),this._callbacks.push({doneCb:n,timeoutId:i,updateCb:o})}whenStable(n,r,o){if(o&&!this._taskTrackingZone)throw new Error('Task tracking zone is required when passing an update callback to whenStable(). Is "zone.js/plugins/task-tracking" loaded?');this.addCallback(n,r,o),this._runCallbacksIfReady()}registerApplication(n){this.registry.registerApplication(n,this)}unregisterApplication(n){this.registry.unregisterApplication(n)}findProviders(n,r,o){return[]}static \u0275fac=function(r){return new(r||e)(E(U),E(gs),E(Gr))};static \u0275prov=m({token:e,factory:e.\u0275fac})}return e})(),gs=(()=>{class e{_applications=new Map;registerApplication(n,r){this._applications.set(n,r)}unregisterApplication(n){this._applications.delete(n)}unregisterAllApplications(){this._applications.clear()}getTestability(n){return this._applications.get(n)||null}getAllTestabilities(){return Array.from(this._applications.values())}getAllRootElements(){return Array.from(this._applications.keys())}findTestabilityInTree(n,r=!0){return sl?.findTestabilityInTree(this,n,r)??null}static \u0275fac=function(r){return new(r||e)};static \u0275prov=m({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})();function iC(e){sl=e}var sl,sg=(()=>{class e{static \u0275prov=m({token:e,providedIn:"root",factory:()=>new Gc})}return e})(),Gc=class{queuedEffectCount=0;queues=new Map;schedule(t){this.enqueue(t)}remove(t){let n=t.zone,r=this.queues.get(n);r.has(t)&&(r.delete(t),this.queuedEffectCount--)}enqueue(t){let n=t.zone;this.queues.has(n)||this.queues.set(n,new Set);let r=this.queues.get(n);r.has(t)||(this.queuedEffectCount++,r.add(t))}flush(){for(;this.queuedEffectCount>0;)for(let[t,n]of this.queues)t===null?this.flushQueue(n):t.run(()=>this.flushQueue(n))}flushQueue(t){for(let n of t)t.delete(n),this.queuedEffectCount--,n.run()}};function Xn(e){return!!e&&typeof e.then=="function"}function al(e){return!!e&&typeof e.subscribe=="function"}var ag=new w("");function cl(e){return Br([{provide:ag,multi:!0,useValue:e}])}var cg=(()=>{class e{resolve;reject;initialized=!1;done=!1;donePromise=new Promise((n,r)=>{this.resolve=n,this.reject=r});appInits=p(ag,{optional:!0})??[];injector=p(ce);constructor(){}runInitializers(){if(this.initialized)return;let n=[];for(let o of this.appInits){let i=we(this.injector,o);if(Xn(i))n.push(i);else if(al(i)){let s=new Promise((a,c)=>{i.subscribe({complete:a,error:c})});n.push(s)}}let r=()=>{this.done=!0,this.resolve()};Promise.all(n).then(()=>{r()}).catch(o=>{this.reject(o)}),n.length===0&&r(),this.initialized=!0}static \u0275fac=function(r){return new(r||e)};static \u0275prov=m({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),ul=new w("");function sC(){ba(()=>{throw new y(600,!1)})}function aC(e){return e.isBoundToModule}var cC=10;function ug(e,t){return Array.isArray(t)?t.reduce(ug,e):D(D({},e),t)}var pt=(()=>{class e{_runningTick=!1;_destroyed=!1;_destroyListeners=[];_views=[];internalErrorHandler=p(VD);afterRenderManager=p(ep);zonelessEnabled=p(_u);rootEffectScheduler=p(sg);dirtyFlags=0;tracingSnapshot=null;externalTestViews=new Set;afterTick=new G;get allViews(){return[...this.externalTestViews.keys(),...this._views]}get destroyed(){return this._destroyed}componentTypes=[];components=[];isStable=p(vt).hasPendingTasks.pipe(_(n=>!n));constructor(){p(Kn,{optional:!0})}whenStable(){let n;return new Promise(r=>{n=this.isStable.subscribe({next:o=>{o&&r()}})}).finally(()=>{n.unsubscribe()})}_injector=p(se);_rendererFactory=null;get injector(){return this._injector}bootstrap(n,r){return this.bootstrapImpl(n,r)}bootstrapImpl(n,r,o=ce.NULL){H(10);let i=n instanceof Bp;if(!this._injector.get(cg).done){let f="";throw new y(405,f)}let a;i?a=n:a=this._injector.get(hs).resolveComponentFactory(n),this.componentTypes.push(a.componentType);let c=aC(a)?void 0:this._injector.get($n),u=r||a.selector,l=a.create(o,[],u,c),d=l.location.nativeElement,h=l.injector.get(il,null);return h?.registerApplication(d),l.onDestroy(()=>{this.detachView(l.hostView),yi(this.components,l),h?.unregisterApplication(d)}),this._loadComponent(l),H(11,l),l}tick(){this.zonelessEnabled||(this.dirtyFlags|=1),this._tick()}_tick(){H(12),this.tracingSnapshot!==null?this.tracingSnapshot.run(Ou.CHANGE_DETECTION,this.tickImpl):this.tickImpl()}tickImpl=()=>{if(this._runningTick)throw new y(101,!1);let n=k(null);try{this._runningTick=!0,this.synchronize()}catch(r){this.internalErrorHandler(r)}finally{this._runningTick=!1,this.tracingSnapshot?.dispose(),this.tracingSnapshot=null,k(n),this.afterTick.next(),H(13)}};synchronize(){this._rendererFactory===null&&!this._injector.destroyed&&(this._rendererFactory=this._injector.get(Bn,null,{optional:!0}));let n=0;for(;this.dirtyFlags!==0&&n++<cC;)H(14),this.synchronizeOnce(),H(15)}synchronizeOnce(){if(this.dirtyFlags&16&&(this.dirtyFlags&=-17,this.rootEffectScheduler.flush()),this.dirtyFlags&7){let n=!!(this.dirtyFlags&1);this.dirtyFlags&=-8,this.dirtyFlags|=8;for(let{_lView:r,notifyErrorHandler:o}of this.allViews)uC(r,o,n,this.zonelessEnabled);if(this.dirtyFlags&=-5,this.syncDirtyFlagsWithViews(),this.dirtyFlags&23)return}else this._rendererFactory?.begin?.(),this._rendererFactory?.end?.();this.dirtyFlags&8&&(this.dirtyFlags&=-9,this.afterRenderManager.execute()),this.syncDirtyFlagsWithViews()}syncDirtyFlagsWithViews(){if(this.allViews.some(({_lView:n})=>ns(n))){this.dirtyFlags|=2;return}else this.dirtyFlags&=-8}attachView(n){let r=n;this._views.push(r),r.attachToAppRef(this)}detachView(n){let r=n;yi(this._views,r),r.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView),this.tick(),this.components.push(n),this._injector.get(ul,[]).forEach(o=>o(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy())}finally{this._destroyed=!0,this._views=[],this._destroyListeners=[]}}onDestroy(n){return this._destroyListeners.push(n),()=>yi(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new y(406,!1);let n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}static \u0275fac=function(r){return new(r||e)};static \u0275prov=m({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function yi(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}function uC(e,t,n,r){if(!n&&!ns(e))return;Pp(e,t,n&&!r?0:1)}function lg(e,t,n,r){let o=P(),i=Hr();if(xt(o,i,t)){let s=Q(),a=Iu();iw(a,o,e,t,n,r)}return lg}function lC(e,t,n,r){return xt(e,Hr(),n)?t+Qi(n)+r:Ke}function fi(e,t){return e<<17|t<<2}function on(e){return e>>17&32767}function dC(e){return(e&2)==2}function fC(e,t){return e&131071|t<<17}function Wc(e){return e|2}function Hn(e){return(e&131068)>>2}function ec(e,t){return e&-131069|t<<2}function hC(e){return(e&1)===1}function Zc(e){return e|1}function pC(e,t,n,r,o,i){let s=i?t.classBindings:t.styleBindings,a=on(s),c=Hn(s);e[r]=n;let u=!1,l;if(Array.isArray(n)){let d=n;l=d[1],(l===null||Ur(d,l)>0)&&(u=!0)}else l=n;if(o)if(c!==0){let h=on(e[a+1]);e[r+1]=fi(h,a),h!==0&&(e[h+1]=ec(e[h+1],r)),e[a+1]=fC(e[a+1],r)}else e[r+1]=fi(a,0),a!==0&&(e[a+1]=ec(e[a+1],r)),a=r;else e[r+1]=fi(c,0),a===0?a=r:e[c+1]=ec(e[c+1],r),c=r;u&&(e[r+1]=Wc(e[r+1])),Sf(e,l,r,!0),Sf(e,l,r,!1),gC(t,l,e,r,i),s=fi(a,c),i?t.classBindings=s:t.styleBindings=s}function gC(e,t,n,r,o){let i=o?e.residualClasses:e.residualStyles;i!=null&&typeof t=="string"&&Ur(i,t)>=0&&(n[r+1]=Zc(n[r+1]))}function Sf(e,t,n,r){let o=e[n+1],i=t===null,s=r?on(o):Hn(o),a=!1;for(;s!==0&&(a===!1||i);){let c=e[s],u=e[s+1];mC(c,t)&&(a=!0,e[s+1]=r?Zc(u):Wc(u)),s=r?on(u):Hn(u)}a&&(e[n+1]=r?Wc(o):Zc(o))}function mC(e,t){return e===null||t==null||(Array.isArray(e)?e[1]:e)===t?!0:Array.isArray(e)&&typeof t=="string"?Ur(e,t)>=0:!1}var ke={textEnd:0,key:0,keyEnd:0,value:0,valueEnd:0};function vC(e){return e.substring(ke.key,ke.keyEnd)}function yC(e){return DC(e),dg(e,fg(e,0,ke.textEnd))}function dg(e,t){let n=ke.textEnd;return n===t?-1:(t=ke.keyEnd=EC(e,ke.key=t,n),fg(e,t,n))}function DC(e){ke.key=0,ke.keyEnd=0,ke.value=0,ke.valueEnd=0,ke.textEnd=e.length}function fg(e,t,n){for(;t<n&&e.charCodeAt(t)<=32;)t++;return t}function EC(e,t,n){for(;t<n&&e.charCodeAt(t)>32;)t++;return t}function wC(e,t,n){let r=P(),o=Hr();if(xt(r,o,t)){let i=Q(),s=Iu();Sp(i,s,r,e,t,r[Y],n,!1)}return wC}function Yc(e,t,n,r,o){Hu(t,e,n,o?"class":"style",r)}function IC(e,t,n){return hg(e,t,n,!1),IC}function CC(e,t){return hg(e,t,null,!0),CC}function hk(e){SC(xC,bC,e,!0)}function bC(e,t){for(let n=yC(t);n>=0;n=dg(t,n))au(e,vC(t),!0)}function hg(e,t,n,r){let o=P(),i=Q(),s=Eh(2);if(i.firstUpdatePass&&gg(i,e,s,r),t!==Ke&&xt(o,s,t)){let a=i.data[sn()];mg(i,a,o,o[Y],e,o[s+1]=OC(t,n),r,s)}}function SC(e,t,n,r){let o=Q(),i=Eh(2);o.firstUpdatePass&&gg(o,null,i,r);let s=P();if(n!==Ke&&xt(s,i,n)){let a=o.data[sn()];if(vg(a,r)&&!pg(o,i)){let c=r?a.classesWithoutHost:a.stylesWithoutHost;c!==null&&(n=oc(c,n||"")),Yc(o,a,s,n,r)}else AC(o,a,s,s[Y],s[i+1],s[i+1]=NC(e,t,n),r,i)}}function pg(e,t){return t>=e.expandoStartIndex}function gg(e,t,n,r){let o=e.data;if(o[n+1]===null){let i=o[sn()],s=pg(e,n);vg(i,r)&&t===null&&!s&&(t=!1),t=TC(o,i,t,r),pC(o,i,t,n,s,r)}}function TC(e,t,n,r){let o=lD(e),i=r?t.residualClasses:t.residualStyles;if(o===null)(r?t.classBindings:t.styleBindings)===0&&(n=tc(null,e,t,n,r),n=Lr(n,t.attrs,r),i=null);else{let s=t.directiveStylingLast;if(s===-1||e[s]!==o)if(n=tc(o,e,t,n,r),i===null){let c=_C(e,t,r);c!==void 0&&Array.isArray(c)&&(c=tc(null,e,t,c[1],r),c=Lr(c,t.attrs,r),MC(e,t,r,c))}else i=RC(e,t,r)}return i!==void 0&&(r?t.residualClasses=i:t.residualStyles=i),n}function _C(e,t,n){let r=n?t.classBindings:t.styleBindings;if(Hn(r)!==0)return e[on(r)]}function MC(e,t,n,r){let o=n?t.classBindings:t.styleBindings;e[on(o)]=r}function RC(e,t,n){let r,o=t.directiveEnd;for(let i=1+t.directiveStylingLast;i<o;i++){let s=e[i].hostAttrs;r=Lr(r,s,n)}return Lr(r,t.attrs,n)}function tc(e,t,n,r,o){let i=null,s=n.directiveEnd,a=n.directiveStylingLast;for(a===-1?a=n.directiveStart:a++;a<s&&(i=t[a],r=Lr(r,i.hostAttrs,o),i!==e);)a++;return e!==null&&(n.directiveStylingLast=a),r}function Lr(e,t,n){let r=n?1:2,o=-1;if(t!==null)for(let i=0;i<t.length;i++){let s=t[i];typeof s=="number"?o=s:o===r&&(Array.isArray(e)||(e=e===void 0?[]:["",e]),au(e,s,n?!0:t[++i]))}return e===void 0?null:e}function NC(e,t,n){if(n==null||n==="")return de;let r=[],o=Qe(n);if(Array.isArray(o))for(let i=0;i<o.length;i++)e(r,o[i],!0);else if(typeof o=="object")for(let i in o)o.hasOwnProperty(i)&&e(r,i,o[i]);else typeof o=="string"&&t(r,o);return r}function xC(e,t,n){let r=String(t);r!==""&&!r.includes(" ")&&au(e,r,n)}function AC(e,t,n,r,o,i,s,a){o===Ke&&(o=de);let c=0,u=0,l=0<o.length?o[0]:null,d=0<i.length?i[0]:null;for(;l!==null||d!==null;){let h=c<o.length?o[c+1]:void 0,f=u<i.length?i[u+1]:void 0,g=null,v;l===d?(c+=2,u+=2,h!==f&&(g=d,v=f)):d===null||l!==null&&l<d?(c+=2,g=l):(u+=2,g=d,v=f),g!==null&&mg(e,t,n,r,g,v,s,a),l=c<o.length?o[c]:null,d=u<i.length?i[u]:null}}function mg(e,t,n,r,o,i,s,a){if(!(t.type&3))return;let c=e.data,u=c[a+1],l=hC(u)?Tf(c,t,n,o,Hn(u),s):void 0;if(!qi(l)){qi(i)||dC(u)&&(i=Tf(c,null,n,o,a,s));let d=dh(sn(),n);Cw(r,s,d,o,i)}}function Tf(e,t,n,r,o,i){let s=t===null,a;for(;o>0;){let c=e[o],u=Array.isArray(c),l=u?c[1]:c,d=l===null,h=n[o+1];h===Ke&&(h=d?de:void 0);let f=d?qa(h,r):l===r?h:void 0;if(u&&!qi(f)&&(f=qa(c,r)),qi(f)&&(a=f,s))return a;let g=e[o+1];o=s?on(g):Hn(g)}if(t!==null){let c=i?t.residualClasses:t.residualStyles;c!=null&&(a=qa(c,r))}return a}function qi(e){return e!==void 0}function OC(e,t){return e==null||e===""||(typeof t=="string"?e=e+t:typeof e=="object"&&(e=De(Qe(e)))),e}function vg(e,t){return(e.flags&(t?8:16))!==0}function pk(e,t){cn("NgControlFlow");let n=P(),r=Hr(),o=n[r]!==Ke?n[r]:-1,i=o!==-1?_f(n,ne+o):void 0,s=0;if(xt(n,r,e)){let a=k(null);try{if(i!==void 0&&jw(i,s),e!==-1){let c=ne+e,u=_f(n,c),l=kC(n[x],c),d=Vi(u,l.tView.ssrId),h=qu(n,l,t,{dehydratedView:d});Ju(u,h,s,Fi(l,d))}}finally{k(a)}}else if(i!==void 0){let a=Lw(i,s);a!==void 0&&(a[fe]=t)}}function _f(e,t){return e[t]}function kC(e,t){return fu(e,t)}function yg(e,t,n,r){let o=P(),i=Q(),s=ne+e,a=o[Y],c=i.firstCreatePass?zp(s,i,o,t,$u,mu(),n,r):i.data[s],u=PC(i,o,c,a,t,e);o[s]=u;let l=ts(c);return Rt(c,!0),Ep(a,u,c),!Wu(c)&&os()&&ls(i,o,u,c),(Jy()===0||l)&&Yn(u,o),Xy(),l&&(us(i,o,c),Fu(i,c,o)),r!==null&&Bu(o,c),yg}function Dg(){let e=he();vu()?yu():(e=e.parent,Rt(e,!1));let t=e;tD(t)&&nD(),eD();let n=Q();return n.firstCreatePass&&qp(n,t),t.classesWithoutHost!=null&&vD(t)&&Yc(n,t,P(),t.classesWithoutHost,!0),t.stylesWithoutHost!=null&&yD(t)&&Yc(n,t,P(),t.stylesWithoutHost,!1),Dg}function ll(e,t,n,r){return yg(e,t,n,r),Dg(),ll}var PC=(e,t,n,r,o,i)=>(is(!0),yp(r,o,pD()));function FC(e,t,n,r,o){let i=t.consts,s=Vn(i,r),a=Jn(t,e,8,"ng-container",s);s!==null&&jc(a,s,!0);let c=Vn(i,o);return mu()&&el(t,n,a,c,$u),a.mergedAttrs=Un(a.mergedAttrs,a.attrs),t.queries!==null&&t.queries.elementStart(t,a),a}function Eg(e,t,n){let r=P(),o=Q(),i=e+ne,s=o.firstCreatePass?FC(i,o,r,t,n):o.data[i];Rt(s,!0);let a=jC(o,r,s,e);return r[i]=a,os()&&ls(o,r,a,s),Yn(a,r),ts(s)&&(us(o,r,s),Fu(o,s,r)),n!=null&&Bu(r,s),Eg}function wg(){let e=he(),t=Q();return vu()?yu():(e=e.parent,Rt(e,!1)),t.firstCreatePass&&(Cu(t,e),du(e)&&t.queries.elementEnd(e)),wg}function LC(e,t,n){return Eg(e,t,n),wg(),LC}var jC=(e,t,n,r)=>(is(!0),zE(t[Y],""));function gk(){return P()}function VC(e,t,n){let r=P(),o=Hr();if(xt(r,o,t)){let i=Q(),s=Iu();Sp(i,s,r,e,t,r[Y],n,!0)}return VC}var Gi="en-US";var UC=Gi;function BC(e){typeof e=="string"&&(UC=e.toLowerCase().replace(/_/g,"-"))}function Mf(e,t,n){return function r(o){if(o===Function)return n;let i=qn(e)?qe(e.index,t):t;Ku(i,5);let s=t[fe],a=Rf(t,s,n,o),c=r.__ngNextListenerFn__;for(;c;)a=Rf(t,s,c,o)&&a,c=c.__ngNextListenerFn__;return a}}function Rf(e,t,n,r){let o=k(null);try{return H(6,t,n),n(r)!==!1}catch(i){return $C(e,i),!1}finally{H(7,t,n),k(o)}}function $C(e,t){let n=e[Ln],r=n?n.get(Ge,null):null;r&&r.handleError(t)}function Nf(e,t,n,r,o,i){let s=t[n],a=t[x],u=a.data[n].outputs[r],l=s[u],d=a.firstCreatePass?gu(a):null,h=pu(t),f=l.subscribe(i),g=h.length;h.push(i,f),d&&d.push(o,e.index,g,-(g+1))}function Ig(e,t,n,r){let o=P(),i=Q(),s=he();return zC(i,o,o[Y],s,e,t,r),Ig}function HC(e,t,n,r){let o=e.cleanup;if(o!=null)for(let i=0;i<o.length-1;i+=2){let s=o[i];if(s===n&&o[i+1]===r){let a=t[bi],c=o[i+2];return a.length>c?a[c]:null}typeof s=="string"&&(i+=2)}return null}function zC(e,t,n,r,o,i,s){let a=ts(r),u=e.firstCreatePass?gu(e):null,l=pu(t),d=!0;if(r.type&3||s){let h=Ye(r,t),f=s?s(h):h,g=l.length,v=s?O=>s(ze(O[r.index])):r.index,I=null;if(!s&&a&&(I=HC(e,t,o,r.index)),I!==null){let O=I.__ngLastListenerFn__||I;O.__ngNextListenerFn__=i,I.__ngLastListenerFn__=i,d=!1}else{i=Mf(r,t,i),iE(t,f,o,i);let O=n.listen(f,o,i);l.push(i,O),u&&u.push(o,v,g,g+1)}}else i=Mf(r,t,i);if(d){let h=r.outputs?.[o],f=r.hostDirectiveOutputs?.[o];if(f&&f.length)for(let g=0;g<f.length;g+=2){let v=f[g],I=f[g+1];Nf(r,t,v,I,o,i)}if(h&&h.length)for(let g of h)Nf(r,t,g,o,o,i)}}function mk(e=1){return fD(e)}function qC(e,t){let n=null,r=PE(e);for(let o=0;o<t.length;o++){let i=t[o];if(i==="*"){n=o;continue}if(r===null?vp(e,i,!0):jE(r,i))return o}return n}function vk(e){let t=P()[xe][Ie];if(!t.projection){let n=e?e.length:1,r=t.projection=Ty(n,null),o=r.slice(),i=t.child;for(;i!==null;){if(i.type!==128){let s=e?qC(i,e):0;s!==null&&(o[s]?o[s].projectionNext=i:r[s]=i,o[s]=i)}i=i.next}}}function yk(e,t=0,n,r,o,i){let s=P(),a=Q(),c=r?e+1:null;c!==null&&ig(s,a,c,r,o,i,null,n);let u=Jn(a,ne+e,16,null,n||null);u.projection===null&&(u.projection=t),yu();let d=!s[Ar]||vh();s[xe][Ie].projection[u.projection]===null&&c!==null?GC(s,a,c):d&&!Wu(u)&&ww(a,s,u)}function GC(e,t,n){let r=ne+n,o=t.data[r],i=e[r],s=Vi(i,o.tView.ssrId),a=qu(e,o,void 0,{dehydratedView:s});Ju(i,a,0,Fi(o,s))}function WC(e,t,n,r){Qp(e,t,n,r)}function Dk(e,t,n){bI(e,t,n)}function ZC(e){let t=P(),n=Q(),r=Du();rs(r+1);let o=nl(n,r);if(e.dirty&&Wy(t)===((o.metadata.flags&2)===2)){if(o.matches===null)e.reset([]);else{let i=Jp(t,r);e.reset(i,qh),e.notifyOnChanges()}return!0}return!1}function YC(){return tl(P(),Du())}function Ek(e,t,n,r,o){RI(t,Qp(e,n,r,o))}function wk(e=1){rs(Du()+e)}function Ik(e){let t=oD();return fh(t,ne+e)}function Ck(e,t=""){let n=P(),r=Q(),o=e+ne,i=r.firstCreatePass?Jn(r,o,1,t,null):r.data[o],s=QC(r,n,i,t,e);n[o]=s,os()&&ls(r,n,s,i),Rt(i,!1)}var QC=(e,t,n,r,o)=>(is(!0),$E(t[Y],r));function KC(e){return Cg("",e,""),KC}function Cg(e,t,n){let r=P(),o=lC(r,e,t,n);return o!==Ke&&JC(r,sn(),o),Cg}function JC(e,t,n){let r=dh(t,e);HE(e[Y],r,n)}var XC={};function eb(e){let t=Q(),n=P(),r=e+ne,o=Jn(t,r,128,null,null);return Rt(o,!1),hh(t,n,r,XC),eb}function tb(e,t,n){let r=Q();if(r.firstCreatePass){let o=He(e);Qc(n,r.data,r.blueprint,o,!0),Qc(t,r.data,r.blueprint,o,!1)}}function Qc(e,t,n,r,o){if(e=le(e),Array.isArray(e))for(let i=0;i<e.length;i++)Qc(e[i],t,n,r,o);else{let i=Q(),s=P(),a=he(),c=Fn(e)?e:le(e.provide),u=rh(e),l=a.providerIndexes&1048575,d=a.directiveStart,h=a.providerIndexes>>20;if(Fn(e)||!e.multi){let f=new en(u,o,Se),g=rc(c,t,o?l:l+h,d);g===-1?(mc(xi(a,s),i,c),nc(i,e,t.length),t.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(f),s.push(f)):(n[g]=f,s[g]=f)}else{let f=rc(c,t,l+h,d),g=rc(c,t,l,l+h),v=f>=0&&n[f],I=g>=0&&n[g];if(o&&!I||!o&&!v){mc(xi(a,s),i,c);let O=ob(o?rb:nb,n.length,o,r,u);!o&&I&&(n[g].providerFactory=O),nc(i,e,t.length,0),t.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(O),s.push(O)}else{let O=bg(n[o?g:f],u,!o&&r);nc(i,e,f>-1?f:g,O)}!o&&r&&I&&n[g].componentProviders++}}}function nc(e,t,n,r){let o=Fn(t),i=Py(t);if(o||i){let c=(i?le(t.useClass):t).prototype.ngOnDestroy;if(c){let u=e.destroyHooks||(e.destroyHooks=[]);if(!o&&t.multi){let l=u.indexOf(n);l===-1?u.push(n,[r,c]):u[l+1].push(r,c)}else u.push(n,c)}}}function bg(e,t,n){return n&&e.componentProviders++,e.multi.push(t)-1}function rc(e,t,n,r){for(let o=n;o<r;o++)if(t[o]===e)return o;return-1}function nb(e,t,n,r,o){return Kc(this.multi,[])}function rb(e,t,n,r,o){let i=this.multi,s;if(this.providerFactory){let a=this.providerFactory.componentProviders,c=kr(r,r[x],this.providerFactory.index,o);s=c.slice(0,a),Kc(i,s);for(let u=a;u<c.length;u++)s.push(c[u])}else s=[],Kc(i,s);return s}function Kc(e,t){for(let n=0;n<e.length;n++){let r=e[n];t.push(r())}return t}function ob(e,t,n,r,o){let i=new en(e,n,Se);return i.multi=[],i.index=t,i.componentProviders=0,bg(i,o,r&&!n),i}function bk(e,t=[]){return n=>{n.providersResolver=(r,o)=>tb(r,o?o(e):e,t)}}function ib(e,t){let n=e[t];return n===Ke?void 0:n}function sb(e,t,n,r,o,i){let s=t+n;return xt(e,s,o)?eC(e,s+1,i?r.call(i,o):r(o)):ib(e,s+1)}function Sk(e,t){let n=Q(),r,o=e+ne;n.firstCreatePass?(r=ab(t,n.pipeRegistry),n.data[o]=r,r.onDestroy&&(n.destroyHooks??=[]).push(o,r.onDestroy)):r=n.data[o];let i=r.factory||(r.factory=Wt(r.type,!0)),s,a=ve(Se);try{let c=Ni(!1),u=i();return Ni(c),hh(n,P(),o,u),u}finally{ve(a)}}function ab(e,t){if(t)for(let n=t.length-1;n>=0;n--){let r=t[n];if(e===r.name)return r}}function Tk(e,t,n){let r=e+ne,o=P(),i=fh(o,r);return cb(o,r)?sb(o,iD(),t,i.transform,n,i):i.transform(n)}function cb(e,t){return e[x].data[t].pure}function _k(e,t){return fs(e,t)}var hi=null;function ub(e){hi!==null&&(e.defaultEncapsulation!==hi.defaultEncapsulation||e.preserveWhitespaces!==hi.preserveWhitespaces)||(hi=e)}var jr=class{full;major;minor;patch;constructor(t){this.full=t;let n=t.split(".");this.major=n[0],this.minor=n[1],this.patch=n.slice(2).join(".")}},Mk=new jr("19.2.14"),Jc=class{ngModuleFactory;componentFactories;constructor(t,n){this.ngModuleFactory=t,this.componentFactories=n}},Sg=(()=>{class e{compileModuleSync(n){return new zi(n)}compileModuleAsync(n){return Promise.resolve(this.compileModuleSync(n))}compileModuleAndAllComponentsSync(n){let r=this.compileModuleSync(n),o=Xf(n),i=gp(o.declarations).reduce((s,a)=>{let c=Mt(a);return c&&s.push(new rn(c)),s},[]);return new Jc(r,i)}compileModuleAndAllComponentsAsync(n){return Promise.resolve(this.compileModuleAndAllComponentsSync(n))}clearCache(){}clearCacheFor(n){}getModuleId(n){}static \u0275fac=function(r){return new(r||e)};static \u0275prov=m({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),lb=new w("");function db(e,t,n){let r=new zi(n);return Promise.resolve(r)}function xf(e){for(let t=e.length-1;t>=0;t--)if(e[t]!==void 0)return e[t]}var fb=(()=>{class e{zone=p(U);changeDetectionScheduler=p(tn);applicationRef=p(pt);_onMicrotaskEmptySubscription;initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=m({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function hb({ngZoneFactory:e,ignoreChangesOutsideZone:t,scheduleInRootZone:n}){return e??=()=>new U(j(D({},Tg()),{scheduleInRootZone:n})),[{provide:U,useFactory:e},{provide:Pn,multi:!0,useFactory:()=>{let r=p(fb,{optional:!0});return()=>r.initialize()}},{provide:Pn,multi:!0,useFactory:()=>{let r=p(pb);return()=>{r.initialize()}}},t===!0?{provide:Uh,useValue:!0}:[],{provide:Bh,useValue:n??Vh}]}function Tg(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}var pb=(()=>{class e{subscription=new q;initialized=!1;zone=p(U);pendingTasks=p(vt);initialize(){if(this.initialized)return;this.initialized=!0;let n=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(n=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{U.assertNotInAngularZone(),queueMicrotask(()=>{n!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(n),n=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{U.assertInAngularZone(),n??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=m({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var gb=(()=>{class e{appRef=p(pt);taskService=p(vt);ngZone=p(U);zonelessEnabled=p(_u);tracing=p(Kn,{optional:!0});disableScheduling=p(Uh,{optional:!0})??!1;zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run;schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}];subscriptions=new q;angularZoneId=this.zoneIsDefined?this.ngZone._inner?.get(Oi):null;scheduleInRootZone=!this.zonelessEnabled&&this.zoneIsDefined&&(p(Bh,{optional:!0})??!1);cancelScheduledCallback=null;useMicrotaskScheduler=!1;runningTick=!1;pendingRenderTaskId=null;constructor(){this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof ki||!this.zoneIsDefined)}notify(n){if(!this.zonelessEnabled&&n===5)return;let r=!1;switch(n){case 0:{this.appRef.dirtyFlags|=2;break}case 3:case 2:case 4:case 5:case 1:{this.appRef.dirtyFlags|=4;break}case 6:{this.appRef.dirtyFlags|=2,r=!0;break}case 12:{this.appRef.dirtyFlags|=16,r=!0;break}case 13:{this.appRef.dirtyFlags|=2,r=!0;break}case 11:{r=!0;break}case 9:case 8:case 7:case 10:default:this.appRef.dirtyFlags|=8}if(this.appRef.tracingSnapshot=this.tracing?.snapshot(this.appRef.tracingSnapshot)??null,!this.shouldScheduleTick(r))return;let o=this.useMicrotaskScheduler?sf:$h;this.pendingRenderTaskId=this.taskService.add(),this.scheduleInRootZone?this.cancelScheduledCallback=Zone.root.run(()=>o(()=>this.tick())):this.cancelScheduledCallback=this.ngZone.runOutsideAngular(()=>o(()=>this.tick()))}shouldScheduleTick(n){return!(this.disableScheduling&&!n||this.appRef.destroyed||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&Zone.current.get(Oi+this.angularZoneId))}tick(){if(this.runningTick||this.appRef.destroyed)return;if(this.appRef.dirtyFlags===0){this.cleanup();return}!this.zonelessEnabled&&this.appRef.dirtyFlags&7&&(this.appRef.dirtyFlags|=1);let n=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick()},void 0,this.schedulerTickApplyArgs)}catch(r){throw this.taskService.remove(n),r}finally{this.cleanup()}this.useMicrotaskScheduler=!0,sf(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(n)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let n=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(n)}}static \u0275fac=function(r){return new(r||e)};static \u0275prov=m({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function mb(){return typeof $localize<"u"&&$localize.locale||Gi}var dl=new w("",{providedIn:"root",factory:()=>p(dl,N.Optional|N.SkipSelf)||mb()});var Wi=new w(""),vb=new w("");function Tr(e){return!e.moduleRef}function yb(e){let t=Tr(e)?e.r3Injector:e.moduleRef.injector,n=t.get(U);return n.run(()=>{Tr(e)?e.r3Injector.resolveInjectorInitializers():e.moduleRef.resolveInjectorInitializers();let r=t.get(Ge,null),o;if(n.runOutsideAngular(()=>{o=n.onError.subscribe({next:i=>{r.handleError(i)}})}),Tr(e)){let i=()=>t.destroy(),s=e.platformInjector.get(Wi);s.add(i),t.onDestroy(()=>{o.unsubscribe(),s.delete(i)})}else{let i=()=>e.moduleRef.destroy(),s=e.platformInjector.get(Wi);s.add(i),e.moduleRef.onDestroy(()=>{yi(e.allPlatformModules,e.moduleRef),o.unsubscribe(),s.delete(i)})}return Eb(r,n,()=>{let i=t.get(cg);return i.runInitializers(),i.donePromise.then(()=>{let s=t.get(dl,Gi);if(BC(s||Gi),!t.get(vb,!0))return Tr(e)?t.get(pt):(e.allPlatformModules.push(e.moduleRef),e.moduleRef);if(Tr(e)){let c=t.get(pt);return e.rootComponent!==void 0&&c.bootstrap(e.rootComponent),c}else return Db(e.moduleRef,e.allPlatformModules),e.moduleRef})})})}function Db(e,t){let n=e.injector.get(pt);if(e._bootstrapComponents.length>0)e._bootstrapComponents.forEach(r=>n.bootstrap(r));else if(e.instance.ngDoBootstrap)e.instance.ngDoBootstrap(n);else throw new y(-403,!1);t.push(e)}function Eb(e,t,n){try{let r=n();return Xn(r)?r.catch(o=>{throw t.runOutsideAngular(()=>e.handleError(o)),o}):r}catch(r){throw t.runOutsideAngular(()=>e.handleError(r)),r}}var _g=(()=>{class e{_injector;_modules=[];_destroyListeners=[];_destroyed=!1;constructor(n){this._injector=n}bootstrapModuleFactory(n,r){let o=r?.scheduleInRootZone,i=()=>jD(r?.ngZone,j(D({},Tg({eventCoalescing:r?.ngZoneEventCoalescing,runCoalescing:r?.ngZoneRunCoalescing})),{scheduleInRootZone:o})),s=r?.ignoreChangesOutsideZone,a=[hb({ngZoneFactory:i,ignoreChangesOutsideZone:s}),{provide:tn,useExisting:gb}],c=jI(n.moduleType,this.injector,a);return yb({moduleRef:c,allPlatformModules:this._modules,platformInjector:this.injector})}bootstrapModule(n,r=[]){let o=ug({},r);return db(this.injector,o,n).then(i=>this.bootstrapModuleFactory(i,o))}onDestroy(n){this._destroyListeners.push(n)}get injector(){return this._injector}destroy(){if(this._destroyed)throw new y(404,!1);this._modules.slice().forEach(r=>r.destroy()),this._destroyListeners.forEach(r=>r());let n=this._injector.get(Wi,null);n&&(n.forEach(r=>r()),n.clear()),this._destroyed=!0}get destroyed(){return this._destroyed}static \u0275fac=function(r){return new(r||e)(E(ce))};static \u0275prov=m({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})(),Nr=null,Mg=new w("");function wb(e){if(Nr&&!Nr.get(Mg,!1))throw new y(400,!1);sC(),Nr=e;let t=e.get(_g);return bb(e),t}function fl(e,t,n=[]){let r=`Platform: ${t}`,o=new w(r);return(i=[])=>{let s=Rg();if(!s||s.injector.get(Mg,!1)){let a=[...n,...i,{provide:o,useValue:!0}];e?e(a):wb(Ib(a,r))}return Cb(o)}}function Ib(e=[],t){return ce.create({name:t,providers:[{provide:Ji,useValue:"platform"},{provide:Wi,useValue:new Set([()=>Nr=null])},...e]})}function Cb(e){let t=Rg();if(!t)throw new y(401,!1);return t}function Rg(){return Nr?.get(_g)??null}function bb(e){let t=e.get(xu,null);we(e,()=>{t?.forEach(n=>n())})}var er=(()=>{class e{static __NG_ELEMENT_ID__=Sb}return e})();function Sb(e){return Tb(he(),P(),(e&16)===16)}function Tb(e,t,n){if(qn(e)&&!n){let r=qe(e.index,t);return new Fr(r,r)}else if(e.type&175){let r=t[xe];return new Fr(r,t)}return null}var Xc=class{constructor(){}supports(t){return og(t)}create(t){return new eu(t)}},_b=(e,t)=>t,eu=class{length=0;collection;_linkedRecords=null;_unlinkedRecords=null;_previousItHead=null;_itHead=null;_itTail=null;_additionsHead=null;_additionsTail=null;_movesHead=null;_movesTail=null;_removalsHead=null;_removalsTail=null;_identityChangesHead=null;_identityChangesTail=null;_trackByFn;constructor(t){this._trackByFn=t||_b}forEachItem(t){let n;for(n=this._itHead;n!==null;n=n._next)t(n)}forEachOperation(t){let n=this._itHead,r=this._removalsHead,o=0,i=null;for(;n||r;){let s=!r||n&&n.currentIndex<Af(r,o,i)?n:r,a=Af(s,o,i),c=s.currentIndex;if(s===r)o--,r=r._nextRemoved;else if(n=n._next,s.previousIndex==null)o++;else{i||(i=[]);let u=a-o,l=c-o;if(u!=l){for(let h=0;h<u;h++){let f=h<i.length?i[h]:i[h]=0,g=f+h;l<=g&&g<u&&(i[h]=f+1)}let d=s.previousIndex;i[d]=l-u}}a!==c&&t(s,a,c)}}forEachPreviousItem(t){let n;for(n=this._previousItHead;n!==null;n=n._nextPrevious)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;n!==null;n=n._nextAdded)t(n)}forEachMovedItem(t){let n;for(n=this._movesHead;n!==null;n=n._nextMoved)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;n!==null;n=n._nextRemoved)t(n)}forEachIdentityChange(t){let n;for(n=this._identityChangesHead;n!==null;n=n._nextIdentityChange)t(n)}diff(t){if(t==null&&(t=[]),!og(t))throw new y(900,!1);return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let n=this._itHead,r=!1,o,i,s;if(Array.isArray(t)){this.length=t.length;for(let a=0;a<this.length;a++)i=t[a],s=this._trackByFn(a,i),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,i,s,a),r=!0):(r&&(n=this._verifyReinsertion(n,i,s,a)),Object.is(n.item,i)||this._addIdentityChange(n,i)),n=n._next}else o=0,JI(t,a=>{s=this._trackByFn(o,a),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,a,s,o),r=!0):(r&&(n=this._verifyReinsertion(n,a,s,o)),Object.is(n.item,a)||this._addIdentityChange(n,a)),n=n._next,o++}),this.length=o;return this._truncate(n),this.collection=t,this.isDirty}get isDirty(){return this._additionsHead!==null||this._movesHead!==null||this._removalsHead!==null||this._identityChangesHead!==null}_reset(){if(this.isDirty){let t;for(t=this._previousItHead=this._itHead;t!==null;t=t._next)t._nextPrevious=t._next;for(t=this._additionsHead;t!==null;t=t._nextAdded)t.previousIndex=t.currentIndex;for(this._additionsHead=this._additionsTail=null,t=this._movesHead;t!==null;t=t._nextMoved)t.previousIndex=t.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(t,n,r,o){let i;return t===null?i=this._itTail:(i=t._prev,this._remove(t)),t=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._reinsertAfter(t,i,o)):(t=this._linkedRecords===null?null:this._linkedRecords.get(r,o),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._moveAfter(t,i,o)):t=this._addAfter(new tu(n,r),i,o)),t}_verifyReinsertion(t,n,r,o){let i=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null);return i!==null?t=this._reinsertAfter(i,t._prev,o):t.currentIndex!=o&&(t.currentIndex=o,this._addToMoves(t,o)),t}_truncate(t){for(;t!==null;){let n=t._next;this._addToRemovals(this._unlink(t)),t=n}this._unlinkedRecords!==null&&this._unlinkedRecords.clear(),this._additionsTail!==null&&(this._additionsTail._nextAdded=null),this._movesTail!==null&&(this._movesTail._nextMoved=null),this._itTail!==null&&(this._itTail._next=null),this._removalsTail!==null&&(this._removalsTail._nextRemoved=null),this._identityChangesTail!==null&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(t,n,r){this._unlinkedRecords!==null&&this._unlinkedRecords.remove(t);let o=t._prevRemoved,i=t._nextRemoved;return o===null?this._removalsHead=i:o._nextRemoved=i,i===null?this._removalsTail=o:i._prevRemoved=o,this._insertAfter(t,n,r),this._addToMoves(t,r),t}_moveAfter(t,n,r){return this._unlink(t),this._insertAfter(t,n,r),this._addToMoves(t,r),t}_addAfter(t,n,r){return this._insertAfter(t,n,r),this._additionsTail===null?this._additionsTail=this._additionsHead=t:this._additionsTail=this._additionsTail._nextAdded=t,t}_insertAfter(t,n,r){let o=n===null?this._itHead:n._next;return t._next=o,t._prev=n,o===null?this._itTail=t:o._prev=t,n===null?this._itHead=t:n._next=t,this._linkedRecords===null&&(this._linkedRecords=new Zi),this._linkedRecords.put(t),t.currentIndex=r,t}_remove(t){return this._addToRemovals(this._unlink(t))}_unlink(t){this._linkedRecords!==null&&this._linkedRecords.remove(t);let n=t._prev,r=t._next;return n===null?this._itHead=r:n._next=r,r===null?this._itTail=n:r._prev=n,t}_addToMoves(t,n){return t.previousIndex===n||(this._movesTail===null?this._movesTail=this._movesHead=t:this._movesTail=this._movesTail._nextMoved=t),t}_addToRemovals(t){return this._unlinkedRecords===null&&(this._unlinkedRecords=new Zi),this._unlinkedRecords.put(t),t.currentIndex=null,t._nextRemoved=null,this._removalsTail===null?(this._removalsTail=this._removalsHead=t,t._prevRemoved=null):(t._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=t),t}_addIdentityChange(t,n){return t.item=n,this._identityChangesTail===null?this._identityChangesTail=this._identityChangesHead=t:this._identityChangesTail=this._identityChangesTail._nextIdentityChange=t,t}},tu=class{item;trackById;currentIndex=null;previousIndex=null;_nextPrevious=null;_prev=null;_next=null;_prevDup=null;_nextDup=null;_prevRemoved=null;_nextRemoved=null;_nextAdded=null;_nextMoved=null;_nextIdentityChange=null;constructor(t,n){this.item=t,this.trackById=n}},nu=class{_head=null;_tail=null;add(t){this._head===null?(this._head=this._tail=t,t._nextDup=null,t._prevDup=null):(this._tail._nextDup=t,t._prevDup=this._tail,t._nextDup=null,this._tail=t)}get(t,n){let r;for(r=this._head;r!==null;r=r._nextDup)if((n===null||n<=r.currentIndex)&&Object.is(r.trackById,t))return r;return null}remove(t){let n=t._prevDup,r=t._nextDup;return n===null?this._head=r:n._nextDup=r,r===null?this._tail=n:r._prevDup=n,this._head===null}},Zi=class{map=new Map;put(t){let n=t.trackById,r=this.map.get(n);r||(r=new nu,this.map.set(n,r)),r.add(t)}get(t,n){let r=t,o=this.map.get(r);return o?o.get(t,n):null}remove(t){let n=t.trackById;return this.map.get(n).remove(t)&&this.map.delete(n),t}get isEmpty(){return this.map.size===0}clear(){this.map.clear()}};function Af(e,t,n){let r=e.previousIndex;if(r===null)return r;let o=0;return n&&r<n.length&&(o=n[r]),r+t+o}function Of(){return new hl([new Xc])}var hl=(()=>{class e{factories;static \u0275prov=m({token:e,providedIn:"root",factory:Of});constructor(n){this.factories=n}static create(n,r){if(r!=null){let o=r.factories.slice();n=n.concat(o)}return new e(n)}static extend(n){return{provide:e,useFactory:r=>e.create(n,r||Of()),deps:[[e,new Cy,new Yf]]}}find(n){let r=this.factories.find(o=>o.supports(n));if(r!=null)return r;throw new y(901,!1)}}return e})();var Ng=fl(null,"core",[]),xg=(()=>{class e{constructor(n){}static \u0275fac=function(r){return new(r||e)(E(pt))};static \u0275mod=Je({type:e});static \u0275inj=Ze({})}return e})();function Ag(e){return typeof e=="boolean"?e:e!=null&&e!=="false"}function Mb(e,t=NaN){return!isNaN(parseFloat(e))&&!isNaN(Number(e))?Number(e):t}function pl(e){return _a(e)}function Rk(e,t){return No(e,t?.equal)}var ru=class{[pe];constructor(t){this[pe]=t}destroy(){this[pe].destroy()}};function Rb(e,t){!t?.injector&&lu(Rb);let n=t?.injector??p(ce),r=t?.manualCleanup!==!0?n.get(Nt):null,o,i=n.get(ku,null,{optional:!0}),s=n.get(tn);return i!==null&&!t?.forceRoot?(o=Ab(i.view,s,e),r instanceof Ai&&r._lView===i.view&&(r=null)):o=Ob(e,n.get(sg),s),o.injector=n,r!==null&&(o.onDestroyFn=r.onDestroy(()=>o.destroy())),new ru(o)}var Og=j(D({},Dn),{consumerIsAlwaysLive:!0,consumerAllowSignalWrites:!0,dirty:!0,hasRun:!1,cleanupFns:void 0,zone:null,kind:"effect",onDestroyFn:Pr,run(){if(this.dirty=!1,this.hasRun&&!_o(this))return;this.hasRun=!0;let e=r=>(this.cleanupFns??=[]).push(r),t=Dr(this),n=_i(!1);try{this.maybeCleanup(),this.fn(e)}finally{_i(n),To(this,t)}},maybeCleanup(){if(this.cleanupFns?.length)try{for(;this.cleanupFns.length;)this.cleanupFns.pop()()}finally{this.cleanupFns=[]}}}),Nb=j(D({},Og),{consumerMarkedDirty(){this.scheduler.schedule(this),this.notifier.notify(12)},destroy(){Er(this),this.onDestroyFn(),this.maybeCleanup(),this.scheduler.remove(this)}}),xb=j(D({},Og),{consumerMarkedDirty(){this.view[b]|=8192,Wn(this.view),this.notifier.notify(13)},destroy(){Er(this),this.onDestroyFn(),this.maybeCleanup(),this.view[Qt]?.delete(this)}});function Ab(e,t,n){let r=Object.create(xb);return r.view=e,r.zone=typeof Zone<"u"?Zone.current:null,r.notifier=t,r.fn=n,e[Qt]??=new Set,e[Qt].add(r),r.consumerMarkedDirty(r),r}function Ob(e,t,n){let r=Object.create(Nb);return r.fn=e,r.scheduler=t,r.notifier=n,r.zone=typeof Zone<"u"?Zone.current:null,r.scheduler.schedule(r),r.notifier.notify(12),r}function Nk(e,t){let n=Mt(e),r=t.elementInjector||Xi();return new rn(n).create(r,t.projectableNodes,t.hostElement,t.environmentInjector)}function kg(e){let t=Mt(e);if(!t)return null;let n=new rn(t);return{get selector(){return n.selector},get type(){return n.componentType},get inputs(){return n.inputs},get outputs(){return n.outputs},get ngContentSelectors(){return n.ngContentSelectors},get isStandalone(){return t.standalone},get isSignal(){return t.signals}}}var K=new w("");var Lg=null;function Xe(){return Lg}function gl(e){Lg??=e}var Wr=class{},Zr=(()=>{class e{historyGo(n){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=m({token:e,factory:()=>p(jg),providedIn:"platform"})}return e})(),ml=new w(""),jg=(()=>{class e extends Zr{_location;_history;_doc=p(K);constructor(){super(),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return Xe().getBaseHref(this._doc)}onPopState(n){let r=Xe().getGlobalEventTarget(this._doc,"window");return r.addEventListener("popstate",n,!1),()=>r.removeEventListener("popstate",n)}onHashChange(n){let r=Xe().getGlobalEventTarget(this._doc,"window");return r.addEventListener("hashchange",n,!1),()=>r.removeEventListener("hashchange",n)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(n){this._location.pathname=n}pushState(n,r,o){this._history.pushState(n,r,o)}replaceState(n,r,o){this._history.replaceState(n,r,o)}forward(){this._history.forward()}back(){this._history.back()}historyGo(n=0){this._history.go(n)}getState(){return this._history.state}static \u0275fac=function(r){return new(r||e)};static \u0275prov=m({token:e,factory:()=>new e,providedIn:"platform"})}return e})();function ms(e,t){return e?t?e.endsWith("/")?t.startsWith("/")?e+t.slice(1):e+t:t.startsWith("/")?e+t:`${e}/${t}`:e:t}function Pg(e){let t=e.search(/#|\?|$/);return e[t-1]==="/"?e.slice(0,t-1)+e.slice(t):e}function Fe(e){return e&&e[0]!=="?"?`?${e}`:e}var wt=(()=>{class e{historyGo(n){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=m({token:e,factory:()=>p(ys),providedIn:"root"})}return e})(),vs=new w(""),ys=(()=>{class e extends wt{_platformLocation;_baseHref;_removeListenerFns=[];constructor(n,r){super(),this._platformLocation=n,this._baseHref=r??this._platformLocation.getBaseHrefFromDOM()??p(K).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}prepareExternalUrl(n){return ms(this._baseHref,n)}path(n=!1){let r=this._platformLocation.pathname+Fe(this._platformLocation.search),o=this._platformLocation.hash;return o&&n?`${r}${o}`:r}pushState(n,r,o,i){let s=this.prepareExternalUrl(o+Fe(i));this._platformLocation.pushState(n,r,s)}replaceState(n,r,o,i){let s=this.prepareExternalUrl(o+Fe(i));this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static \u0275fac=function(r){return new(r||e)(E(Zr),E(vs,8))};static \u0275prov=m({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),At=(()=>{class e{_subject=new G;_basePath;_locationStrategy;_urlChangeListeners=[];_urlChangeSubscription=null;constructor(n){this._locationStrategy=n;let r=this._locationStrategy.getBaseHref();this._basePath=Fb(Pg(Fg(r))),this._locationStrategy.onPopState(o=>{this._subject.next({url:this.path(!0),pop:!0,state:o.state,type:o.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(n=!1){return this.normalize(this._locationStrategy.path(n))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(n,r=""){return this.path()==this.normalize(n+Fe(r))}normalize(n){return e.stripTrailingSlash(Pb(this._basePath,Fg(n)))}prepareExternalUrl(n){return n&&n[0]!=="/"&&(n="/"+n),this._locationStrategy.prepareExternalUrl(n)}go(n,r="",o=null){this._locationStrategy.pushState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+Fe(r)),o)}replaceState(n,r="",o=null){this._locationStrategy.replaceState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+Fe(r)),o)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(n=0){this._locationStrategy.historyGo?.(n)}onUrlChange(n){return this._urlChangeListeners.push(n),this._urlChangeSubscription??=this.subscribe(r=>{this._notifyUrlChangeListeners(r.url,r.state)}),()=>{let r=this._urlChangeListeners.indexOf(n);this._urlChangeListeners.splice(r,1),this._urlChangeListeners.length===0&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(n="",r){this._urlChangeListeners.forEach(o=>o(n,r))}subscribe(n,r,o){return this._subject.subscribe({next:n,error:r??void 0,complete:o??void 0})}static normalizeQueryParams=Fe;static joinWithSlash=ms;static stripTrailingSlash=Pg;static \u0275fac=function(r){return new(r||e)(E(wt))};static \u0275prov=m({token:e,factory:()=>kb(),providedIn:"root"})}return e})();function kb(){return new At(E(wt))}function Pb(e,t){if(!e||!t.startsWith(e))return t;let n=t.substring(e.length);return n===""||["/",";","?","#"].includes(n[0])?n:t}function Fg(e){return e.replace(/\/index.html$/,"")}function Fb(e){if(new RegExp("^(https?:)?//").test(e)){let[,n]=e.split(/\/\/[^\/]+/);return n}return e}var Dl=(()=>{class e extends wt{_platformLocation;_baseHref="";_removeListenerFns=[];constructor(n,r){super(),this._platformLocation=n,r!=null&&(this._baseHref=r)}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}path(n=!1){let r=this._platformLocation.hash??"#";return r.length>0?r.substring(1):r}prepareExternalUrl(n){let r=ms(this._baseHref,n);return r.length>0?"#"+r:r}pushState(n,r,o,i){let s=this.prepareExternalUrl(o+Fe(i))||this._platformLocation.pathname;this._platformLocation.pushState(n,r,s)}replaceState(n,r,o,i){let s=this.prepareExternalUrl(o+Fe(i))||this._platformLocation.pathname;this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static \u0275fac=function(r){return new(r||e)(E(Zr),E(vs,8))};static \u0275prov=m({token:e,factory:e.\u0275fac})}return e})();var Ds=class{$implicit;ngForOf;index;count;constructor(t,n,r,o){this.$implicit=t,this.ngForOf=n,this.index=r,this.count=o}get first(){return this.index===0}get last(){return this.index===this.count-1}get even(){return this.index%2===0}get odd(){return!this.even}},Bg=(()=>{class e{_viewContainer;_template;_differs;set ngForOf(n){this._ngForOf=n,this._ngForOfDirty=!0}set ngForTrackBy(n){this._trackByFn=n}get ngForTrackBy(){return this._trackByFn}_ngForOf=null;_ngForOfDirty=!0;_differ=null;_trackByFn;constructor(n,r,o){this._viewContainer=n,this._template=r,this._differs=o}set ngForTemplate(n){n&&(this._template=n)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;let n=this._ngForOf;!this._differ&&n&&(this._differ=this._differs.find(n).create(this.ngForTrackBy))}if(this._differ){let n=this._differ.diff(this._ngForOf);n&&this._applyChanges(n)}}_applyChanges(n){let r=this._viewContainer;n.forEachOperation((o,i,s)=>{if(o.previousIndex==null)r.createEmbeddedView(this._template,new Ds(o.item,this._ngForOf,-1,-1),s===null?void 0:s);else if(s==null)r.remove(i===null?void 0:i);else if(i!==null){let a=r.get(i);r.move(a,s),Vg(a,o)}});for(let o=0,i=r.length;o<i;o++){let a=r.get(o).context;a.index=o,a.count=i,a.ngForOf=this._ngForOf}n.forEachIdentityChange(o=>{let i=r.get(o.currentIndex);Vg(i,o)})}static ngTemplateContextGuard(n,r){return!0}static \u0275fac=function(r){return new(r||e)(Se(Et),Se(nn),Se(hl))};static \u0275dir=ln({type:e,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"}})}return e})();function Vg(e,t){e.context.$implicit=t.item}var Lb=(()=>{class e{_viewContainer;_context=new Es;_thenTemplateRef=null;_elseTemplateRef=null;_thenViewRef=null;_elseViewRef=null;constructor(n,r){this._viewContainer=n,this._thenTemplateRef=r}set ngIf(n){this._context.$implicit=this._context.ngIf=n,this._updateView()}set ngIfThen(n){Ug(n,!1),this._thenTemplateRef=n,this._thenViewRef=null,this._updateView()}set ngIfElse(n){Ug(n,!1),this._elseTemplateRef=n,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngIfUseIfTypeGuard;static ngTemplateGuard_ngIf;static ngTemplateContextGuard(n,r){return!0}static \u0275fac=function(r){return new(r||e)(Se(Et),Se(nn))};static \u0275dir=ln({type:e,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"}})}return e})(),Es=class{$implicit=null;ngIf=null};function Ug(e,t){if(e&&!e.createEmbeddedView)throw new y(2020,!1)}var jb=(()=>{class e{_viewContainerRef;_viewRef=null;ngTemplateOutletContext=null;ngTemplateOutlet=null;ngTemplateOutletInjector=null;constructor(n){this._viewContainerRef=n}ngOnChanges(n){if(this._shouldRecreateView(n)){let r=this._viewContainerRef;if(this._viewRef&&r.remove(r.indexOf(this._viewRef)),!this.ngTemplateOutlet){this._viewRef=null;return}let o=this._createContextForwardProxy();this._viewRef=r.createEmbeddedView(this.ngTemplateOutlet,o,{injector:this.ngTemplateOutletInjector??void 0})}}_shouldRecreateView(n){return!!n.ngTemplateOutlet||!!n.ngTemplateOutletInjector}_createContextForwardProxy(){return new Proxy({},{set:(n,r,o)=>this.ngTemplateOutletContext?Reflect.set(this.ngTemplateOutletContext,r,o):!1,get:(n,r,o)=>{if(this.ngTemplateOutletContext)return Reflect.get(this.ngTemplateOutletContext,r,o)}})}static \u0275fac=function(r){return new(r||e)(Se(Et))};static \u0275dir=ln({type:e,selectors:[["","ngTemplateOutlet",""]],inputs:{ngTemplateOutletContext:"ngTemplateOutletContext",ngTemplateOutlet:"ngTemplateOutlet",ngTemplateOutletInjector:"ngTemplateOutletInjector"},features:[$r]})}return e})();function Vb(e,t){return new y(2100,!1)}var vl=class{createSubscription(t,n){return pl(()=>t.subscribe({next:n,error:r=>{throw r}}))}dispose(t){pl(()=>t.unsubscribe())}},yl=class{createSubscription(t,n){return t.then(r=>n?.(r),r=>{throw r}),{unsubscribe:()=>{n=null}}}dispose(t){t.unsubscribe()}},Ub=new yl,Bb=new vl,$b=(()=>{class e{_ref;_latestValue=null;markForCheckOnValueUpdate=!0;_subscription=null;_obj=null;_strategy=null;constructor(n){this._ref=n}ngOnDestroy(){this._subscription&&this._dispose(),this._ref=null}transform(n){if(!this._obj){if(n)try{this.markForCheckOnValueUpdate=!1,this._subscribe(n)}finally{this.markForCheckOnValueUpdate=!0}return this._latestValue}return n!==this._obj?(this._dispose(),this.transform(n)):this._latestValue}_subscribe(n){this._obj=n,this._strategy=this._selectStrategy(n),this._subscription=this._strategy.createSubscription(n,r=>this._updateLatestValue(n,r))}_selectStrategy(n){if(Xn(n))return Ub;if(al(n))return Bb;throw Vb(e,n)}_dispose(){this._strategy.dispose(this._subscription),this._latestValue=null,this._subscription=null,this._obj=null}_updateLatestValue(n,r){n===this._obj&&(this._latestValue=r,this.markForCheckOnValueUpdate&&this._ref?.markForCheck())}static \u0275fac=function(r){return new(r||e)(Se(er,16))};static \u0275pipe=tg({name:"async",type:e,pure:!1})}return e})();var El=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=Je({type:e});static \u0275inj=Ze({})}return e})();function Yr(e,t){t=encodeURIComponent(t);for(let n of e.split(";")){let r=n.indexOf("="),[o,i]=r==-1?[n,""]:[n.slice(0,r),n.slice(r+1)];if(o.trim()===t)return decodeURIComponent(i)}return null}var ws="browser",$g="server";function Hb(e){return e===ws}function Is(e){return e===$g}var dn=class{};var Hg=(()=>{class e{static \u0275prov=m({token:e,providedIn:"root",factory:()=>new wl(p(K),window)})}return e})(),wl=class{document;window;offset=()=>[0,0];constructor(t,n){this.document=t,this.window=n}setOffset(t){Array.isArray(t)?this.offset=()=>t:this.offset=t}getScrollPosition(){return[this.window.scrollX,this.window.scrollY]}scrollToPosition(t){this.window.scrollTo(t[0],t[1])}scrollToAnchor(t){let n=zb(this.document,t);n&&(this.scrollToElement(n),n.focus())}setHistoryScrollRestoration(t){this.window.history.scrollRestoration=t}scrollToElement(t){let n=t.getBoundingClientRect(),r=n.left+this.window.pageXOffset,o=n.top+this.window.pageYOffset,i=this.offset();this.window.scrollTo(r-i[0],o-i[1])}};function zb(e,t){let n=e.getElementById(t)||e.getElementsByName(t)[0];if(n)return n;if(typeof e.createTreeWalker=="function"&&e.body&&typeof e.body.attachShadow=="function"){let r=e.createTreeWalker(e.body,NodeFilter.SHOW_ELEMENT),o=r.currentNode;for(;o;){let i=o.shadowRoot;if(i){let s=i.getElementById(t)||i.querySelector(`[name="${t}"]`);if(s)return s}o=r.nextNode()}}return null}var Ss=new w(""),Sl=(()=>{class e{_zone;_plugins;_eventNameToPlugin=new Map;constructor(n,r){this._zone=r,n.forEach(o=>{o.manager=this}),this._plugins=n.slice().reverse()}addEventListener(n,r,o,i){return this._findPluginFor(r).addEventListener(n,r,o,i)}getZone(){return this._zone}_findPluginFor(n){let r=this._eventNameToPlugin.get(n);if(r)return r;if(r=this._plugins.find(i=>i.supports(n)),!r)throw new y(5101,!1);return this._eventNameToPlugin.set(n,r),r}static \u0275fac=function(r){return new(r||e)(E(Ss),E(U))};static \u0275prov=m({token:e,factory:e.\u0275fac})}return e})(),Qr=class{_doc;constructor(t){this._doc=t}manager},Cs="ng-app-id";function zg(e){for(let t of e)t.remove()}function qg(e,t){let n=t.createElement("style");return n.textContent=e,n}function qb(e,t,n,r){let o=e.head?.querySelectorAll(`style[${Cs}="${t}"],link[${Cs}="${t}"]`);if(o)for(let i of o)i.removeAttribute(Cs),i instanceof HTMLLinkElement?r.set(i.href.slice(i.href.lastIndexOf("/")+1),{usage:0,elements:[i]}):i.textContent&&n.set(i.textContent,{usage:0,elements:[i]})}function Cl(e,t){let n=t.createElement("link");return n.setAttribute("rel","stylesheet"),n.setAttribute("href",e),n}var Tl=(()=>{class e{doc;appId;nonce;inline=new Map;external=new Map;hosts=new Set;isServer;constructor(n,r,o,i={}){this.doc=n,this.appId=r,this.nonce=o,this.isServer=Is(i),qb(n,r,this.inline,this.external),this.hosts.add(n.head)}addStyles(n,r){for(let o of n)this.addUsage(o,this.inline,qg);r?.forEach(o=>this.addUsage(o,this.external,Cl))}removeStyles(n,r){for(let o of n)this.removeUsage(o,this.inline);r?.forEach(o=>this.removeUsage(o,this.external))}addUsage(n,r,o){let i=r.get(n);i?i.usage++:r.set(n,{usage:1,elements:[...this.hosts].map(s=>this.addElement(s,o(n,this.doc)))})}removeUsage(n,r){let o=r.get(n);o&&(o.usage--,o.usage<=0&&(zg(o.elements),r.delete(n)))}ngOnDestroy(){for(let[,{elements:n}]of[...this.inline,...this.external])zg(n);this.hosts.clear()}addHost(n){this.hosts.add(n);for(let[r,{elements:o}]of this.inline)o.push(this.addElement(n,qg(r,this.doc)));for(let[r,{elements:o}]of this.external)o.push(this.addElement(n,Cl(r,this.doc)))}removeHost(n){this.hosts.delete(n)}addElement(n,r){return this.nonce&&r.setAttribute("nonce",this.nonce),this.isServer&&r.setAttribute(Cs,this.appId),n.appendChild(r)}static \u0275fac=function(r){return new(r||e)(E(K),E(Nu),E(Au,8),E(Qn))};static \u0275prov=m({token:e,factory:e.\u0275fac})}return e})(),Il={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/Math/MathML"},_l=/%COMP%/g;var Wg="%COMP%",Gb=`_nghost-${Wg}`,Wb=`_ngcontent-${Wg}`,Zb=!0,Yb=new w("",{providedIn:"root",factory:()=>Zb});function Qb(e){return Wb.replace(_l,e)}function Kb(e){return Gb.replace(_l,e)}function Zg(e,t){return t.map(n=>n.replace(_l,e))}var Ml=(()=>{class e{eventManager;sharedStylesHost;appId;removeStylesOnCompDestroy;doc;platformId;ngZone;nonce;tracingService;rendererByCompId=new Map;defaultRenderer;platformIsServer;constructor(n,r,o,i,s,a,c,u=null,l=null){this.eventManager=n,this.sharedStylesHost=r,this.appId=o,this.removeStylesOnCompDestroy=i,this.doc=s,this.platformId=a,this.ngZone=c,this.nonce=u,this.tracingService=l,this.platformIsServer=Is(a),this.defaultRenderer=new Kr(n,s,c,this.platformIsServer,this.tracingService)}createRenderer(n,r){if(!n||!r)return this.defaultRenderer;this.platformIsServer&&r.encapsulation===We.ShadowDom&&(r=j(D({},r),{encapsulation:We.Emulated}));let o=this.getOrCreateRenderer(n,r);return o instanceof bs?o.applyToHost(n):o instanceof Jr&&o.applyStyles(),o}getOrCreateRenderer(n,r){let o=this.rendererByCompId,i=o.get(r.id);if(!i){let s=this.doc,a=this.ngZone,c=this.eventManager,u=this.sharedStylesHost,l=this.removeStylesOnCompDestroy,d=this.platformIsServer,h=this.tracingService;switch(r.encapsulation){case We.Emulated:i=new bs(c,u,r,this.appId,l,s,a,d,h);break;case We.ShadowDom:return new bl(c,u,n,r,s,a,this.nonce,d,h);default:i=new Jr(c,u,r,l,s,a,d,h);break}o.set(r.id,i)}return i}ngOnDestroy(){this.rendererByCompId.clear()}componentReplaced(n){this.rendererByCompId.delete(n)}static \u0275fac=function(r){return new(r||e)(E(Sl),E(Tl),E(Nu),E(Yb),E(K),E(Qn),E(U),E(Au),E(Kn,8))};static \u0275prov=m({token:e,factory:e.\u0275fac})}return e})(),Kr=class{eventManager;doc;ngZone;platformIsServer;tracingService;data=Object.create(null);throwOnSyntheticProps=!0;constructor(t,n,r,o,i){this.eventManager=t,this.doc=n,this.ngZone=r,this.platformIsServer=o,this.tracingService=i}destroy(){}destroyNode=null;createElement(t,n){return n?this.doc.createElementNS(Il[n]||n,t):this.doc.createElement(t)}createComment(t){return this.doc.createComment(t)}createText(t){return this.doc.createTextNode(t)}appendChild(t,n){(Gg(t)?t.content:t).appendChild(n)}insertBefore(t,n,r){t&&(Gg(t)?t.content:t).insertBefore(n,r)}removeChild(t,n){n.remove()}selectRootElement(t,n){let r=typeof t=="string"?this.doc.querySelector(t):t;if(!r)throw new y(-5104,!1);return n||(r.textContent=""),r}parentNode(t){return t.parentNode}nextSibling(t){return t.nextSibling}setAttribute(t,n,r,o){if(o){n=o+":"+n;let i=Il[o];i?t.setAttributeNS(i,n,r):t.setAttribute(n,r)}else t.setAttribute(n,r)}removeAttribute(t,n,r){if(r){let o=Il[r];o?t.removeAttributeNS(o,n):t.removeAttribute(`${r}:${n}`)}else t.removeAttribute(n)}addClass(t,n){t.classList.add(n)}removeClass(t,n){t.classList.remove(n)}setStyle(t,n,r,o){o&(ht.DashCase|ht.Important)?t.style.setProperty(n,r,o&ht.Important?"important":""):t.style[n]=r}removeStyle(t,n,r){r&ht.DashCase?t.style.removeProperty(n):t.style[n]=""}setProperty(t,n,r){t!=null&&(t[n]=r)}setValue(t,n){t.nodeValue=n}listen(t,n,r,o){if(typeof t=="string"&&(t=Xe().getGlobalEventTarget(this.doc,t),!t))throw new y(5102,!1);let i=this.decoratePreventDefault(r);return this.tracingService?.wrapEventListener&&(i=this.tracingService.wrapEventListener(t,n,i)),this.eventManager.addEventListener(t,n,i,o)}decoratePreventDefault(t){return n=>{if(n==="__ngUnwrap__")return t;(this.platformIsServer?this.ngZone.runGuarded(()=>t(n)):t(n))===!1&&n.preventDefault()}}};function Gg(e){return e.tagName==="TEMPLATE"&&e.content!==void 0}var bl=class extends Kr{sharedStylesHost;hostEl;shadowRoot;constructor(t,n,r,o,i,s,a,c,u){super(t,i,s,c,u),this.sharedStylesHost=n,this.hostEl=r,this.shadowRoot=r.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let l=o.styles;l=Zg(o.id,l);for(let h of l){let f=document.createElement("style");a&&f.setAttribute("nonce",a),f.textContent=h,this.shadowRoot.appendChild(f)}let d=o.getExternalStyles?.();if(d)for(let h of d){let f=Cl(h,i);a&&f.setAttribute("nonce",a),this.shadowRoot.appendChild(f)}}nodeOrShadowRoot(t){return t===this.hostEl?this.shadowRoot:t}appendChild(t,n){return super.appendChild(this.nodeOrShadowRoot(t),n)}insertBefore(t,n,r){return super.insertBefore(this.nodeOrShadowRoot(t),n,r)}removeChild(t,n){return super.removeChild(null,n)}parentNode(t){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(t)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}},Jr=class extends Kr{sharedStylesHost;removeStylesOnCompDestroy;styles;styleUrls;constructor(t,n,r,o,i,s,a,c,u){super(t,i,s,a,c),this.sharedStylesHost=n,this.removeStylesOnCompDestroy=o;let l=r.styles;this.styles=u?Zg(u,l):l,this.styleUrls=r.getExternalStyles?.(u)}applyStyles(){this.sharedStylesHost.addStyles(this.styles,this.styleUrls)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles,this.styleUrls)}},bs=class extends Jr{contentAttr;hostAttr;constructor(t,n,r,o,i,s,a,c,u){let l=o+"-"+r.id;super(t,n,r,i,s,a,c,u,l),this.contentAttr=Qb(l),this.hostAttr=Kb(l)}applyToHost(t){this.applyStyles(),this.setAttribute(t,this.hostAttr,"")}createElement(t,n){let r=super.createElement(t,n);return super.setAttribute(r,this.contentAttr,""),r}};var Ts=class e extends Wr{supportsDOMEvents=!0;static makeCurrent(){gl(new e)}onAndCancel(t,n,r,o){return t.addEventListener(n,r,o),()=>{t.removeEventListener(n,r,o)}}dispatchEvent(t,n){t.dispatchEvent(n)}remove(t){t.remove()}createElement(t,n){return n=n||this.getDefaultDocument(),n.createElement(t)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(t){return t.nodeType===Node.ELEMENT_NODE}isShadowRoot(t){return t instanceof DocumentFragment}getGlobalEventTarget(t,n){return n==="window"?window:n==="document"?t:n==="body"?t.body:null}getBaseHref(t){let n=Jb();return n==null?null:Xb(n)}resetBaseElement(){Xr=null}getUserAgent(){return window.navigator.userAgent}getCookie(t){return Yr(document.cookie,t)}},Xr=null;function Jb(){return Xr=Xr||document.head.querySelector("base"),Xr?Xr.getAttribute("href"):null}function Xb(e){return new URL(e,document.baseURI).pathname}var _s=class{addToWindow(t){Ne.getAngularTestability=(r,o=!0)=>{let i=t.findTestabilityInTree(r,o);if(i==null)throw new y(5103,!1);return i},Ne.getAllAngularTestabilities=()=>t.getAllTestabilities(),Ne.getAllAngularRootElements=()=>t.getAllRootElements();let n=r=>{let o=Ne.getAllAngularTestabilities(),i=o.length,s=function(){i--,i==0&&r()};o.forEach(a=>{a.whenStable(s)})};Ne.frameworkStabilizers||(Ne.frameworkStabilizers=[]),Ne.frameworkStabilizers.push(n)}findTestabilityInTree(t,n,r){if(n==null)return null;let o=t.getTestability(n);return o??(r?Xe().isShadowRoot(n)?this.findTestabilityInTree(t,n.host,!0):this.findTestabilityInTree(t,n.parentElement,!0):null)}},eS=(()=>{class e{build(){return new XMLHttpRequest}static \u0275fac=function(r){return new(r||e)};static \u0275prov=m({token:e,factory:e.\u0275fac})}return e})(),Qg=(()=>{class e extends Qr{constructor(n){super(n)}supports(n){return!0}addEventListener(n,r,o,i){return n.addEventListener(r,o,i),()=>this.removeEventListener(n,r,o,i)}removeEventListener(n,r,o,i){return n.removeEventListener(r,o,i)}static \u0275fac=function(r){return new(r||e)(E(K))};static \u0275prov=m({token:e,factory:e.\u0275fac})}return e})(),Yg=["alt","control","meta","shift"],tS={"\b":"Backspace","	":"Tab","\x7F":"Delete","\x1B":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},nS={alt:e=>e.altKey,control:e=>e.ctrlKey,meta:e=>e.metaKey,shift:e=>e.shiftKey},Kg=(()=>{class e extends Qr{constructor(n){super(n)}supports(n){return e.parseEventName(n)!=null}addEventListener(n,r,o,i){let s=e.parseEventName(r),a=e.eventCallback(s.fullKey,o,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>Xe().onAndCancel(n,s.domEventName,a,i))}static parseEventName(n){let r=n.toLowerCase().split("."),o=r.shift();if(r.length===0||!(o==="keydown"||o==="keyup"))return null;let i=e._normalizeKey(r.pop()),s="",a=r.indexOf("code");if(a>-1&&(r.splice(a,1),s="code."),Yg.forEach(u=>{let l=r.indexOf(u);l>-1&&(r.splice(l,1),s+=u+".")}),s+=i,r.length!=0||i.length===0)return null;let c={};return c.domEventName=o,c.fullKey=s,c}static matchEventFullKeyCode(n,r){let o=tS[n.key]||n.key,i="";return r.indexOf("code.")>-1&&(o=n.code,i="code."),o==null||!o?!1:(o=o.toLowerCase(),o===" "?o="space":o==="."&&(o="dot"),Yg.forEach(s=>{if(s!==o){let a=nS[s];a(n)&&(i+=s+".")}}),i+=o,i===r)}static eventCallback(n,r,o){return i=>{e.matchEventFullKeyCode(i,n)&&o.runGuarded(()=>r(i))}}static _normalizeKey(n){return n==="esc"?"escape":n}static \u0275fac=function(r){return new(r||e)(E(K))};static \u0275prov=m({token:e,factory:e.\u0275fac})}return e})();function rS(){Ts.makeCurrent()}function oS(){return new Ge}function iS(){return Jh(document),document}var sS=[{provide:Qn,useValue:ws},{provide:xu,useValue:rS,multi:!0},{provide:K,useFactory:iS}],aS=fl(Ng,"browser",sS);var cS=[{provide:Gr,useClass:_s},{provide:il,useClass:ps,deps:[U,gs,Gr]},{provide:ps,useClass:ps,deps:[U,gs,Gr]}],uS=[{provide:Ji,useValue:"root"},{provide:Ge,useFactory:oS},{provide:Ss,useClass:Qg,multi:!0,deps:[K]},{provide:Ss,useClass:Kg,multi:!0,deps:[K]},Ml,Tl,Sl,{provide:Bn,useExisting:Ml},{provide:dn,useClass:eS},[]],lS=(()=>{class e{constructor(){}static \u0275fac=function(r){return new(r||e)};static \u0275mod=Je({type:e});static \u0275inj=Ze({providers:[...uS,...cS],imports:[El,xg]})}return e})();var nr=class{},eo=class{},Ot=class e{headers;normalizedNames=new Map;lazyInit;lazyUpdate=null;constructor(t){t?typeof t=="string"?this.lazyInit=()=>{this.headers=new Map,t.split(`
`).forEach(n=>{let r=n.indexOf(":");if(r>0){let o=n.slice(0,r),i=n.slice(r+1).trim();this.addHeaderEntry(o,i)}})}:typeof Headers<"u"&&t instanceof Headers?(this.headers=new Map,t.forEach((n,r)=>{this.addHeaderEntry(r,n)})):this.lazyInit=()=>{this.headers=new Map,Object.entries(t).forEach(([n,r])=>{this.setHeaderEntries(n,r)})}:this.headers=new Map}has(t){return this.init(),this.headers.has(t.toLowerCase())}get(t){this.init();let n=this.headers.get(t.toLowerCase());return n&&n.length>0?n[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(t){return this.init(),this.headers.get(t.toLowerCase())||null}append(t,n){return this.clone({name:t,value:n,op:"a"})}set(t,n){return this.clone({name:t,value:n,op:"s"})}delete(t,n){return this.clone({name:t,value:n,op:"d"})}maybeSetNormalizedName(t,n){this.normalizedNames.has(n)||this.normalizedNames.set(n,t)}init(){this.lazyInit&&(this.lazyInit instanceof e?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(t=>this.applyUpdate(t)),this.lazyUpdate=null))}copyFrom(t){t.init(),Array.from(t.headers.keys()).forEach(n=>{this.headers.set(n,t.headers.get(n)),this.normalizedNames.set(n,t.normalizedNames.get(n))})}clone(t){let n=new e;return n.lazyInit=this.lazyInit&&this.lazyInit instanceof e?this.lazyInit:this,n.lazyUpdate=(this.lazyUpdate||[]).concat([t]),n}applyUpdate(t){let n=t.name.toLowerCase();switch(t.op){case"a":case"s":let r=t.value;if(typeof r=="string"&&(r=[r]),r.length===0)return;this.maybeSetNormalizedName(t.name,n);let o=(t.op==="a"?this.headers.get(n):void 0)||[];o.push(...r),this.headers.set(n,o);break;case"d":let i=t.value;if(!i)this.headers.delete(n),this.normalizedNames.delete(n);else{let s=this.headers.get(n);if(!s)return;s=s.filter(a=>i.indexOf(a)===-1),s.length===0?(this.headers.delete(n),this.normalizedNames.delete(n)):this.headers.set(n,s)}break}}addHeaderEntry(t,n){let r=t.toLowerCase();this.maybeSetNormalizedName(t,r),this.headers.has(r)?this.headers.get(r).push(n):this.headers.set(r,[n])}setHeaderEntries(t,n){let r=(Array.isArray(n)?n:[n]).map(i=>i.toString()),o=t.toLowerCase();this.headers.set(o,r),this.maybeSetNormalizedName(t,o)}forEach(t){this.init(),Array.from(this.normalizedNames.keys()).forEach(n=>t(this.normalizedNames.get(n),this.headers.get(n)))}};var Rs=class{encodeKey(t){return Jg(t)}encodeValue(t){return Jg(t)}decodeKey(t){return decodeURIComponent(t)}decodeValue(t){return decodeURIComponent(t)}};function dS(e,t){let n=new Map;return e.length>0&&e.replace(/^\?/,"").split("&").forEach(o=>{let i=o.indexOf("="),[s,a]=i==-1?[t.decodeKey(o),""]:[t.decodeKey(o.slice(0,i)),t.decodeValue(o.slice(i+1))],c=n.get(s)||[];c.push(a),n.set(s,c)}),n}var fS=/%(\d[a-f0-9])/gi,hS={40:"@","3A":":",24:"$","2C":",","3B":";","3D":"=","3F":"?","2F":"/"};function Jg(e){return encodeURIComponent(e).replace(fS,(t,n)=>hS[n]??t)}function Ms(e){return`${e}`}var It=class e{map;encoder;updates=null;cloneFrom=null;constructor(t={}){if(this.encoder=t.encoder||new Rs,t.fromString){if(t.fromObject)throw new y(2805,!1);this.map=dS(t.fromString,this.encoder)}else t.fromObject?(this.map=new Map,Object.keys(t.fromObject).forEach(n=>{let r=t.fromObject[n],o=Array.isArray(r)?r.map(Ms):[Ms(r)];this.map.set(n,o)})):this.map=null}has(t){return this.init(),this.map.has(t)}get(t){this.init();let n=this.map.get(t);return n?n[0]:null}getAll(t){return this.init(),this.map.get(t)||null}keys(){return this.init(),Array.from(this.map.keys())}append(t,n){return this.clone({param:t,value:n,op:"a"})}appendAll(t){let n=[];return Object.keys(t).forEach(r=>{let o=t[r];Array.isArray(o)?o.forEach(i=>{n.push({param:r,value:i,op:"a"})}):n.push({param:r,value:o,op:"a"})}),this.clone(n)}set(t,n){return this.clone({param:t,value:n,op:"s"})}delete(t,n){return this.clone({param:t,value:n,op:"d"})}toString(){return this.init(),this.keys().map(t=>{let n=this.encoder.encodeKey(t);return this.map.get(t).map(r=>n+"="+this.encoder.encodeValue(r)).join("&")}).filter(t=>t!=="").join("&")}clone(t){let n=new e({encoder:this.encoder});return n.cloneFrom=this.cloneFrom||this,n.updates=(this.updates||[]).concat(t),n}init(){this.map===null&&(this.map=new Map),this.cloneFrom!==null&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(t=>this.map.set(t,this.cloneFrom.map.get(t))),this.updates.forEach(t=>{switch(t.op){case"a":case"s":let n=(t.op==="a"?this.map.get(t.param):void 0)||[];n.push(Ms(t.value)),this.map.set(t.param,n);break;case"d":if(t.value!==void 0){let r=this.map.get(t.param)||[],o=r.indexOf(Ms(t.value));o!==-1&&r.splice(o,1),r.length>0?this.map.set(t.param,r):this.map.delete(t.param)}else{this.map.delete(t.param);break}}}),this.cloneFrom=this.updates=null)}};var Ns=class{map=new Map;set(t,n){return this.map.set(t,n),this}get(t){return this.map.has(t)||this.map.set(t,t.defaultValue()),this.map.get(t)}delete(t){return this.map.delete(t),this}has(t){return this.map.has(t)}keys(){return this.map.keys()}};function pS(e){switch(e){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}function Xg(e){return typeof ArrayBuffer<"u"&&e instanceof ArrayBuffer}function em(e){return typeof Blob<"u"&&e instanceof Blob}function tm(e){return typeof FormData<"u"&&e instanceof FormData}function gS(e){return typeof URLSearchParams<"u"&&e instanceof URLSearchParams}var nm="Content-Type",rm="Accept",im="X-Request-URL",sm="text/plain",am="application/json",mS=`${am}, ${sm}, */*`,tr=class e{url;body=null;headers;context;reportProgress=!1;withCredentials=!1;responseType="json";method;params;urlWithParams;transferCache;constructor(t,n,r,o){this.url=n,this.method=t.toUpperCase();let i;if(pS(this.method)||o?(this.body=r!==void 0?r:null,i=o):i=r,i&&(this.reportProgress=!!i.reportProgress,this.withCredentials=!!i.withCredentials,i.responseType&&(this.responseType=i.responseType),i.headers&&(this.headers=i.headers),i.context&&(this.context=i.context),i.params&&(this.params=i.params),this.transferCache=i.transferCache),this.headers??=new Ot,this.context??=new Ns,!this.params)this.params=new It,this.urlWithParams=n;else{let s=this.params.toString();if(s.length===0)this.urlWithParams=n;else{let a=n.indexOf("?"),c=a===-1?"?":a<n.length-1?"&":"";this.urlWithParams=n+c+s}}}serializeBody(){return this.body===null?null:typeof this.body=="string"||Xg(this.body)||em(this.body)||tm(this.body)||gS(this.body)?this.body:this.body instanceof It?this.body.toString():typeof this.body=="object"||typeof this.body=="boolean"||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return this.body===null||tm(this.body)?null:em(this.body)?this.body.type||null:Xg(this.body)?null:typeof this.body=="string"?sm:this.body instanceof It?"application/x-www-form-urlencoded;charset=UTF-8":typeof this.body=="object"||typeof this.body=="number"||typeof this.body=="boolean"?am:null}clone(t={}){let n=t.method||this.method,r=t.url||this.url,o=t.responseType||this.responseType,i=t.transferCache??this.transferCache,s=t.body!==void 0?t.body:this.body,a=t.withCredentials??this.withCredentials,c=t.reportProgress??this.reportProgress,u=t.headers||this.headers,l=t.params||this.params,d=t.context??this.context;return t.setHeaders!==void 0&&(u=Object.keys(t.setHeaders).reduce((h,f)=>h.set(f,t.setHeaders[f]),u)),t.setParams&&(l=Object.keys(t.setParams).reduce((h,f)=>h.set(f,t.setParams[f]),l)),new e(n,r,s,{params:l,headers:u,context:d,reportProgress:c,responseType:o,withCredentials:a,transferCache:i})}},fn=function(e){return e[e.Sent=0]="Sent",e[e.UploadProgress=1]="UploadProgress",e[e.ResponseHeader=2]="ResponseHeader",e[e.DownloadProgress=3]="DownloadProgress",e[e.Response=4]="Response",e[e.User=5]="User",e}(fn||{}),rr=class{headers;status;statusText;url;ok;type;constructor(t,n=200,r="OK"){this.headers=t.headers||new Ot,this.status=t.status!==void 0?t.status:n,this.statusText=t.statusText||r,this.url=t.url||null,this.ok=this.status>=200&&this.status<300}},xs=class e extends rr{constructor(t={}){super(t)}type=fn.ResponseHeader;clone(t={}){return new e({headers:t.headers||this.headers,status:t.status!==void 0?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}},to=class e extends rr{body;constructor(t={}){super(t),this.body=t.body!==void 0?t.body:null}type=fn.Response;clone(t={}){return new e({body:t.body!==void 0?t.body:this.body,headers:t.headers||this.headers,status:t.status!==void 0?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}},no=class extends rr{name="HttpErrorResponse";message;error;ok=!1;constructor(t){super(t,0,"Unknown Error"),this.status>=200&&this.status<300?this.message=`Http failure during parsing for ${t.url||"(unknown url)"}`:this.message=`Http failure response for ${t.url||"(unknown url)"}: ${t.status} ${t.statusText}`,this.error=t.error||null}},vS=200,yS=204;function Rl(e,t){return{body:t,headers:e.headers,context:e.context,observe:e.observe,params:e.params,reportProgress:e.reportProgress,responseType:e.responseType,withCredentials:e.withCredentials,transferCache:e.transferCache}}var Os=(()=>{class e{handler;constructor(n){this.handler=n}request(n,r,o={}){let i;if(n instanceof tr)i=n;else{let c;o.headers instanceof Ot?c=o.headers:c=new Ot(o.headers);let u;o.params&&(o.params instanceof It?u=o.params:u=new It({fromObject:o.params})),i=new tr(n,r,o.body!==void 0?o.body:null,{headers:c,context:o.context,params:u,reportProgress:o.reportProgress,responseType:o.responseType||"json",withCredentials:o.withCredentials,transferCache:o.transferCache})}let s=C(i).pipe(Ue(c=>this.handler.handle(c)));if(n instanceof tr||o.observe==="events")return s;let a=s.pipe(re(c=>c instanceof to));switch(o.observe||"body"){case"body":switch(i.responseType){case"arraybuffer":return a.pipe(_(c=>{if(c.body!==null&&!(c.body instanceof ArrayBuffer))throw new y(2806,!1);return c.body}));case"blob":return a.pipe(_(c=>{if(c.body!==null&&!(c.body instanceof Blob))throw new y(2807,!1);return c.body}));case"text":return a.pipe(_(c=>{if(c.body!==null&&typeof c.body!="string")throw new y(2808,!1);return c.body}));case"json":default:return a.pipe(_(c=>c.body))}case"response":return a;default:throw new y(2809,!1)}}delete(n,r={}){return this.request("DELETE",n,r)}get(n,r={}){return this.request("GET",n,r)}head(n,r={}){return this.request("HEAD",n,r)}jsonp(n,r){return this.request("JSONP",n,{params:new It().append(r,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options(n,r={}){return this.request("OPTIONS",n,r)}patch(n,r,o={}){return this.request("PATCH",n,Rl(o,r))}post(n,r,o={}){return this.request("POST",n,Rl(o,r))}put(n,r,o={}){return this.request("PUT",n,Rl(o,r))}static \u0275fac=function(r){return new(r||e)(E(nr))};static \u0275prov=m({token:e,factory:e.\u0275fac})}return e})();var DS=new w("");function cm(e,t){return t(e)}function ES(e,t){return(n,r)=>t.intercept(n,{handle:o=>e(o,r)})}function wS(e,t,n){return(r,o)=>we(n,()=>t(r,i=>e(i,o)))}var um=new w(""),xl=new w(""),lm=new w(""),Al=new w("",{providedIn:"root",factory:()=>!0});function IS(){let e=null;return(t,n)=>{e===null&&(e=(p(um,{optional:!0})??[]).reduceRight(ES,cm));let r=p(vt);if(p(Al)){let i=r.add();return e(t,n).pipe(oe(()=>r.remove(i)))}else return e(t,n)}}var As=(()=>{class e extends nr{backend;injector;chain=null;pendingTasks=p(vt);contributeToStability=p(Al);constructor(n,r){super(),this.backend=n,this.injector=r}handle(n){if(this.chain===null){let r=Array.from(new Set([...this.injector.get(xl),...this.injector.get(lm,[])]));this.chain=r.reduceRight((o,i)=>wS(o,i,this.injector),cm)}if(this.contributeToStability){let r=this.pendingTasks.add();return this.chain(n,o=>this.backend.handle(o)).pipe(oe(()=>this.pendingTasks.remove(r)))}else return this.chain(n,r=>this.backend.handle(r))}static \u0275fac=function(r){return new(r||e)(E(eo),E(se))};static \u0275prov=m({token:e,factory:e.\u0275fac})}return e})();var CS=/^\)\]\}',?\n/,bS=RegExp(`^${im}:`,"m");function SS(e){return"responseURL"in e&&e.responseURL?e.responseURL:bS.test(e.getAllResponseHeaders())?e.getResponseHeader(im):null}var Nl=(()=>{class e{xhrFactory;constructor(n){this.xhrFactory=n}handle(n){if(n.method==="JSONP")throw new y(-2800,!1);let r=this.xhrFactory;return(r.\u0275loadImpl?$(r.\u0275loadImpl()):C(null)).pipe(me(()=>new F(i=>{let s=r.build();if(s.open(n.method,n.urlWithParams),n.withCredentials&&(s.withCredentials=!0),n.headers.forEach((v,I)=>s.setRequestHeader(v,I.join(","))),n.headers.has(rm)||s.setRequestHeader(rm,mS),!n.headers.has(nm)){let v=n.detectContentTypeHeader();v!==null&&s.setRequestHeader(nm,v)}if(n.responseType){let v=n.responseType.toLowerCase();s.responseType=v!=="json"?v:"text"}let a=n.serializeBody(),c=null,u=()=>{if(c!==null)return c;let v=s.statusText||"OK",I=new Ot(s.getAllResponseHeaders()),O=SS(s)||n.url;return c=new xs({headers:I,status:s.status,statusText:v,url:O}),c},l=()=>{let{headers:v,status:I,statusText:O,url:it}=u(),J=null;I!==yS&&(J=typeof s.response>"u"?s.responseText:s.response),I===0&&(I=J?vS:0);let pa=I>=200&&I<300;if(n.responseType==="json"&&typeof J=="string"){let hv=J;J=J.replace(CS,"");try{J=J!==""?JSON.parse(J):null}catch(pv){J=hv,pa&&(pa=!1,J={error:pv,text:J})}}pa?(i.next(new to({body:J,headers:v,status:I,statusText:O,url:it||void 0})),i.complete()):i.error(new no({error:J,headers:v,status:I,statusText:O,url:it||void 0}))},d=v=>{let{url:I}=u(),O=new no({error:v,status:s.status||0,statusText:s.statusText||"Unknown Error",url:I||void 0});i.error(O)},h=!1,f=v=>{h||(i.next(u()),h=!0);let I={type:fn.DownloadProgress,loaded:v.loaded};v.lengthComputable&&(I.total=v.total),n.responseType==="text"&&s.responseText&&(I.partialText=s.responseText),i.next(I)},g=v=>{let I={type:fn.UploadProgress,loaded:v.loaded};v.lengthComputable&&(I.total=v.total),i.next(I)};return s.addEventListener("load",l),s.addEventListener("error",d),s.addEventListener("timeout",d),s.addEventListener("abort",d),n.reportProgress&&(s.addEventListener("progress",f),a!==null&&s.upload&&s.upload.addEventListener("progress",g)),s.send(a),i.next({type:fn.Sent}),()=>{s.removeEventListener("error",d),s.removeEventListener("abort",d),s.removeEventListener("load",l),s.removeEventListener("timeout",d),n.reportProgress&&(s.removeEventListener("progress",f),a!==null&&s.upload&&s.upload.removeEventListener("progress",g)),s.readyState!==s.DONE&&s.abort()}})))}static \u0275fac=function(r){return new(r||e)(E(dn))};static \u0275prov=m({token:e,factory:e.\u0275fac})}return e})(),dm=new w(""),TS="XSRF-TOKEN",_S=new w("",{providedIn:"root",factory:()=>TS}),MS="X-XSRF-TOKEN",RS=new w("",{providedIn:"root",factory:()=>MS}),ro=class{},NS=(()=>{class e{doc;cookieName;lastCookieString="";lastToken=null;parseCount=0;constructor(n,r){this.doc=n,this.cookieName=r}getToken(){let n=this.doc.cookie||"";return n!==this.lastCookieString&&(this.parseCount++,this.lastToken=Yr(n,this.cookieName),this.lastCookieString=n),this.lastToken}static \u0275fac=function(r){return new(r||e)(E(K),E(_S))};static \u0275prov=m({token:e,factory:e.\u0275fac})}return e})();function xS(e,t){let n=e.url.toLowerCase();if(!p(dm)||e.method==="GET"||e.method==="HEAD"||n.startsWith("http://")||n.startsWith("https://"))return t(e);let r=p(ro).getToken(),o=p(RS);return r!=null&&!e.headers.has(o)&&(e=e.clone({headers:e.headers.set(o,r)})),t(e)}var Ol=function(e){return e[e.Interceptors=0]="Interceptors",e[e.LegacyInterceptors=1]="LegacyInterceptors",e[e.CustomXsrfConfiguration=2]="CustomXsrfConfiguration",e[e.NoXsrfProtection=3]="NoXsrfProtection",e[e.JsonpSupport=4]="JsonpSupport",e[e.RequestsMadeViaParent=5]="RequestsMadeViaParent",e[e.Fetch=6]="Fetch",e}(Ol||{});function AS(e,t){return{\u0275kind:e,\u0275providers:t}}function fm(...e){let t=[Os,Nl,As,{provide:nr,useExisting:As},{provide:eo,useFactory:()=>p(DS,{optional:!0})??p(Nl)},{provide:xl,useValue:xS,multi:!0},{provide:dm,useValue:!0},{provide:ro,useClass:NS}];for(let n of e)t.push(...n.\u0275providers);return Br(t)}var om=new w("");function hm(){return AS(Ol.LegacyInterceptors,[{provide:om,useFactory:IS},{provide:xl,useExisting:om,multi:!0}])}var OS=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=Je({type:e});static \u0275inj=Ze({providers:[fm(hm())]})}return e})();var pm=(()=>{class e{_doc;constructor(n){this._doc=n}getTitle(){return this._doc.title}setTitle(n){this._doc.title=n||""}static \u0275fac=function(r){return new(r||e)(E(K))};static \u0275prov=m({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var PS=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=m({token:e,factory:function(r){let o=null;return r?o=new(r||e):o=E(FS),o},providedIn:"root"})}return e})(),FS=(()=>{class e extends PS{_doc;constructor(n){super(),this._doc=n}sanitize(n,r){if(r==null)return null;switch(n){case Dt.NONE:return r;case Dt.HTML:return un(r,"HTML")?Qe(r):pp(this._doc,String(r)).toString();case Dt.STYLE:return un(r,"Style")?Qe(r):r;case Dt.SCRIPT:if(un(r,"Script"))return Qe(r);throw new y(5200,!1);case Dt.URL:return un(r,"URL")?Qe(r):as(String(r));case Dt.RESOURCE_URL:if(un(r,"ResourceURL"))return Qe(r);throw new y(5201,!1);default:throw new y(5202,!1)}}bypassSecurityTrustHtml(n){return op(n)}bypassSecurityTrustStyle(n){return ip(n)}bypassSecurityTrustScript(n){return sp(n)}bypassSecurityTrustUrl(n){return ap(n)}bypassSecurityTrustResourceUrl(n){return cp(n)}static \u0275fac=function(r){return new(r||e)(E(K))};static \u0275prov=m({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var R="primary",vo=Symbol("RouteTitle"),jl=class{params;constructor(t){this.params=t||{}}has(t){return Object.prototype.hasOwnProperty.call(this.params,t)}get(t){if(this.has(t)){let n=this.params[t];return Array.isArray(n)?n[0]:n}return null}getAll(t){if(this.has(t)){let n=this.params[t];return Array.isArray(n)?n:[n]}return[]}get keys(){return Object.keys(this.params)}};function gn(e){return new jl(e)}function Im(e,t,n){let r=n.path.split("/");if(r.length>e.length||n.pathMatch==="full"&&(t.hasChildren()||r.length<e.length))return null;let o={};for(let i=0;i<r.length;i++){let s=r[i],a=e[i];if(s[0]===":")o[s.substring(1)]=a;else if(s!==a.path)return null}return{consumed:e.slice(0,r.length),posParams:o}}function jS(e,t){if(e.length!==t.length)return!1;for(let n=0;n<e.length;++n)if(!et(e[n],t[n]))return!1;return!0}function et(e,t){let n=e?Vl(e):void 0,r=t?Vl(t):void 0;if(!n||!r||n.length!=r.length)return!1;let o;for(let i=0;i<n.length;i++)if(o=n[i],!Cm(e[o],t[o]))return!1;return!0}function Vl(e){return[...Object.keys(e),...Object.getOwnPropertySymbols(e)]}function Cm(e,t){if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return!1;let n=[...e].sort(),r=[...t].sort();return n.every((o,i)=>r[i]===o)}else return e===t}function bm(e){return e.length>0?e[e.length-1]:null}function jt(e){return Va(e)?e:Xn(e)?$(Promise.resolve(e)):C(e)}var VS={exact:Tm,subset:_m},Sm={exact:US,subset:BS,ignored:()=>!0};function gm(e,t,n){return VS[n.paths](e.root,t.root,n.matrixParams)&&Sm[n.queryParams](e.queryParams,t.queryParams)&&!(n.fragment==="exact"&&e.fragment!==t.fragment)}function US(e,t){return et(e,t)}function Tm(e,t,n){if(!hn(e.segments,t.segments)||!Fs(e.segments,t.segments,n)||e.numberOfChildren!==t.numberOfChildren)return!1;for(let r in t.children)if(!e.children[r]||!Tm(e.children[r],t.children[r],n))return!1;return!0}function BS(e,t){return Object.keys(t).length<=Object.keys(e).length&&Object.keys(t).every(n=>Cm(e[n],t[n]))}function _m(e,t,n){return Mm(e,t,t.segments,n)}function Mm(e,t,n,r){if(e.segments.length>n.length){let o=e.segments.slice(0,n.length);return!(!hn(o,n)||t.hasChildren()||!Fs(o,n,r))}else if(e.segments.length===n.length){if(!hn(e.segments,n)||!Fs(e.segments,n,r))return!1;for(let o in t.children)if(!e.children[o]||!_m(e.children[o],t.children[o],r))return!1;return!0}else{let o=n.slice(0,e.segments.length),i=n.slice(e.segments.length);return!hn(e.segments,o)||!Fs(e.segments,o,r)||!e.children[R]?!1:Mm(e.children[R],t,i,r)}}function Fs(e,t,n){return t.every((r,o)=>Sm[n](e[o].parameters,r.parameters))}var nt=class{root;queryParams;fragment;_queryParamMap;constructor(t=new L([],{}),n={},r=null){this.root=t,this.queryParams=n,this.fragment=r}get queryParamMap(){return this._queryParamMap??=gn(this.queryParams),this._queryParamMap}toString(){return zS.serialize(this)}},L=class{segments;children;parent=null;constructor(t,n){this.segments=t,this.children=n,Object.values(n).forEach(r=>r.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return Ls(this)}},kt=class{path;parameters;_parameterMap;constructor(t,n){this.path=t,this.parameters=n}get parameterMap(){return this._parameterMap??=gn(this.parameters),this._parameterMap}toString(){return Nm(this)}};function $S(e,t){return hn(e,t)&&e.every((n,r)=>et(n.parameters,t[r].parameters))}function hn(e,t){return e.length!==t.length?!1:e.every((n,r)=>n.path===t[r].path)}function HS(e,t){let n=[];return Object.entries(e.children).forEach(([r,o])=>{r===R&&(n=n.concat(t(o,r)))}),Object.entries(e.children).forEach(([r,o])=>{r!==R&&(n=n.concat(t(o,r)))}),n}var mn=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=m({token:e,factory:()=>new Pt,providedIn:"root"})}return e})(),Pt=class{parse(t){let n=new Bl(t);return new nt(n.parseRootSegment(),n.parseQueryParams(),n.parseFragment())}serialize(t){let n=`/${oo(t.root,!0)}`,r=WS(t.queryParams),o=typeof t.fragment=="string"?`#${qS(t.fragment)}`:"";return`${n}${r}${o}`}},zS=new Pt;function Ls(e){return e.segments.map(t=>Nm(t)).join("/")}function oo(e,t){if(!e.hasChildren())return Ls(e);if(t){let n=e.children[R]?oo(e.children[R],!1):"",r=[];return Object.entries(e.children).forEach(([o,i])=>{o!==R&&r.push(`${o}:${oo(i,!1)}`)}),r.length>0?`${n}(${r.join("//")})`:n}else{let n=HS(e,(r,o)=>o===R?[oo(e.children[R],!1)]:[`${o}:${oo(r,!1)}`]);return Object.keys(e.children).length===1&&e.children[R]!=null?`${Ls(e)}/${n[0]}`:`${Ls(e)}/(${n.join("//")})`}}function Rm(e){return encodeURIComponent(e).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function ks(e){return Rm(e).replace(/%3B/gi,";")}function qS(e){return encodeURI(e)}function Ul(e){return Rm(e).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function js(e){return decodeURIComponent(e)}function mm(e){return js(e.replace(/\+/g,"%20"))}function Nm(e){return`${Ul(e.path)}${GS(e.parameters)}`}function GS(e){return Object.entries(e).map(([t,n])=>`;${Ul(t)}=${Ul(n)}`).join("")}function WS(e){let t=Object.entries(e).map(([n,r])=>Array.isArray(r)?r.map(o=>`${ks(n)}=${ks(o)}`).join("&"):`${ks(n)}=${ks(r)}`).filter(n=>n);return t.length?`?${t.join("&")}`:""}var ZS=/^[^\/()?;#]+/;function kl(e){let t=e.match(ZS);return t?t[0]:""}var YS=/^[^\/()?;=#]+/;function QS(e){let t=e.match(YS);return t?t[0]:""}var KS=/^[^=?&#]+/;function JS(e){let t=e.match(KS);return t?t[0]:""}var XS=/^[^&#]+/;function eT(e){let t=e.match(XS);return t?t[0]:""}var Bl=class{url;remaining;constructor(t){this.url=t,this.remaining=t}parseRootSegment(){return this.consumeOptional("/"),this.remaining===""||this.peekStartsWith("?")||this.peekStartsWith("#")?new L([],{}):new L([],this.parseChildren())}parseQueryParams(){let t={};if(this.consumeOptional("?"))do this.parseQueryParam(t);while(this.consumeOptional("&"));return t}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(this.remaining==="")return{};this.consumeOptional("/");let t=[];for(this.peekStartsWith("(")||t.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),t.push(this.parseSegment());let n={};this.peekStartsWith("/(")&&(this.capture("/"),n=this.parseParens(!0));let r={};return this.peekStartsWith("(")&&(r=this.parseParens(!1)),(t.length>0||Object.keys(n).length>0)&&(r[R]=new L(t,n)),r}parseSegment(){let t=kl(this.remaining);if(t===""&&this.peekStartsWith(";"))throw new y(4009,!1);return this.capture(t),new kt(js(t),this.parseMatrixParams())}parseMatrixParams(){let t={};for(;this.consumeOptional(";");)this.parseParam(t);return t}parseParam(t){let n=QS(this.remaining);if(!n)return;this.capture(n);let r="";if(this.consumeOptional("=")){let o=kl(this.remaining);o&&(r=o,this.capture(r))}t[js(n)]=js(r)}parseQueryParam(t){let n=JS(this.remaining);if(!n)return;this.capture(n);let r="";if(this.consumeOptional("=")){let s=eT(this.remaining);s&&(r=s,this.capture(r))}let o=mm(n),i=mm(r);if(t.hasOwnProperty(o)){let s=t[o];Array.isArray(s)||(s=[s],t[o]=s),s.push(i)}else t[o]=i}parseParens(t){let n={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){let r=kl(this.remaining),o=this.remaining[r.length];if(o!=="/"&&o!==")"&&o!==";")throw new y(4010,!1);let i;r.indexOf(":")>-1?(i=r.slice(0,r.indexOf(":")),this.capture(i),this.capture(":")):t&&(i=R);let s=this.parseChildren();n[i]=Object.keys(s).length===1?s[R]:new L([],s),this.consumeOptional("//")}return n}peekStartsWith(t){return this.remaining.startsWith(t)}consumeOptional(t){return this.peekStartsWith(t)?(this.remaining=this.remaining.substring(t.length),!0):!1}capture(t){if(!this.consumeOptional(t))throw new y(4011,!1)}};function xm(e){return e.segments.length>0?new L([],{[R]:e}):e}function Am(e){let t={};for(let[r,o]of Object.entries(e.children)){let i=Am(o);if(r===R&&i.segments.length===0&&i.hasChildren())for(let[s,a]of Object.entries(i.children))t[s]=a;else(i.segments.length>0||i.hasChildren())&&(t[r]=i)}let n=new L(e.segments,t);return tT(n)}function tT(e){if(e.numberOfChildren===1&&e.children[R]){let t=e.children[R];return new L(e.segments.concat(t.segments),t.children)}return e}function cr(e){return e instanceof nt}function Om(e,t,n=null,r=null){let o=km(e);return Pm(o,t,n,r)}function km(e){let t;function n(i){let s={};for(let c of i.children){let u=n(c);s[c.outlet]=u}let a=new L(i.url,s);return i===e&&(t=a),a}let r=n(e.root),o=xm(r);return t??o}function Pm(e,t,n,r){let o=e;for(;o.parent;)o=o.parent;if(t.length===0)return Pl(o,o,o,n,r);let i=nT(t);if(i.toRoot())return Pl(o,o,new L([],{}),n,r);let s=rT(i,o,e),a=s.processChildren?so(s.segmentGroup,s.index,i.commands):Lm(s.segmentGroup,s.index,i.commands);return Pl(o,s.segmentGroup,a,n,r)}function Us(e){return typeof e=="object"&&e!=null&&!e.outlets&&!e.segmentPath}function co(e){return typeof e=="object"&&e!=null&&e.outlets}function Pl(e,t,n,r,o){let i={};r&&Object.entries(r).forEach(([c,u])=>{i[c]=Array.isArray(u)?u.map(l=>`${l}`):`${u}`});let s;e===t?s=n:s=Fm(e,t,n);let a=xm(Am(s));return new nt(a,i,o)}function Fm(e,t,n){let r={};return Object.entries(e.children).forEach(([o,i])=>{i===t?r[o]=n:r[o]=Fm(i,t,n)}),new L(e.segments,r)}var Bs=class{isAbsolute;numberOfDoubleDots;commands;constructor(t,n,r){if(this.isAbsolute=t,this.numberOfDoubleDots=n,this.commands=r,t&&r.length>0&&Us(r[0]))throw new y(4003,!1);let o=r.find(co);if(o&&o!==bm(r))throw new y(4004,!1)}toRoot(){return this.isAbsolute&&this.commands.length===1&&this.commands[0]=="/"}};function nT(e){if(typeof e[0]=="string"&&e.length===1&&e[0]==="/")return new Bs(!0,0,e);let t=0,n=!1,r=e.reduce((o,i,s)=>{if(typeof i=="object"&&i!=null){if(i.outlets){let a={};return Object.entries(i.outlets).forEach(([c,u])=>{a[c]=typeof u=="string"?u.split("/"):u}),[...o,{outlets:a}]}if(i.segmentPath)return[...o,i.segmentPath]}return typeof i!="string"?[...o,i]:s===0?(i.split("/").forEach((a,c)=>{c==0&&a==="."||(c==0&&a===""?n=!0:a===".."?t++:a!=""&&o.push(a))}),o):[...o,i]},[]);return new Bs(n,t,r)}var sr=class{segmentGroup;processChildren;index;constructor(t,n,r){this.segmentGroup=t,this.processChildren=n,this.index=r}};function rT(e,t,n){if(e.isAbsolute)return new sr(t,!0,0);if(!n)return new sr(t,!1,NaN);if(n.parent===null)return new sr(n,!0,0);let r=Us(e.commands[0])?0:1,o=n.segments.length-1+r;return oT(n,o,e.numberOfDoubleDots)}function oT(e,t,n){let r=e,o=t,i=n;for(;i>o;){if(i-=o,r=r.parent,!r)throw new y(4005,!1);o=r.segments.length}return new sr(r,!1,o-i)}function iT(e){return co(e[0])?e[0].outlets:{[R]:e}}function Lm(e,t,n){if(e??=new L([],{}),e.segments.length===0&&e.hasChildren())return so(e,t,n);let r=sT(e,t,n),o=n.slice(r.commandIndex);if(r.match&&r.pathIndex<e.segments.length){let i=new L(e.segments.slice(0,r.pathIndex),{});return i.children[R]=new L(e.segments.slice(r.pathIndex),e.children),so(i,0,o)}else return r.match&&o.length===0?new L(e.segments,{}):r.match&&!e.hasChildren()?$l(e,t,n):r.match?so(e,0,o):$l(e,t,n)}function so(e,t,n){if(n.length===0)return new L(e.segments,{});{let r=iT(n),o={};if(Object.keys(r).some(i=>i!==R)&&e.children[R]&&e.numberOfChildren===1&&e.children[R].segments.length===0){let i=so(e.children[R],t,n);return new L(e.segments,i.children)}return Object.entries(r).forEach(([i,s])=>{typeof s=="string"&&(s=[s]),s!==null&&(o[i]=Lm(e.children[i],t,s))}),Object.entries(e.children).forEach(([i,s])=>{r[i]===void 0&&(o[i]=s)}),new L(e.segments,o)}}function sT(e,t,n){let r=0,o=t,i={match:!1,pathIndex:0,commandIndex:0};for(;o<e.segments.length;){if(r>=n.length)return i;let s=e.segments[o],a=n[r];if(co(a))break;let c=`${a}`,u=r<n.length-1?n[r+1]:null;if(o>0&&c===void 0)break;if(c&&u&&typeof u=="object"&&u.outlets===void 0){if(!ym(c,u,s))return i;r+=2}else{if(!ym(c,{},s))return i;r++}o++}return{match:!0,pathIndex:o,commandIndex:r}}function $l(e,t,n){let r=e.segments.slice(0,t),o=0;for(;o<n.length;){let i=n[o];if(co(i)){let c=aT(i.outlets);return new L(r,c)}if(o===0&&Us(n[0])){let c=e.segments[t];r.push(new kt(c.path,vm(n[0]))),o++;continue}let s=co(i)?i.outlets[R]:`${i}`,a=o<n.length-1?n[o+1]:null;s&&a&&Us(a)?(r.push(new kt(s,vm(a))),o+=2):(r.push(new kt(s,{})),o++)}return new L(r,{})}function aT(e){let t={};return Object.entries(e).forEach(([n,r])=>{typeof r=="string"&&(r=[r]),r!==null&&(t[n]=$l(new L([],{}),0,r))}),t}function vm(e){let t={};return Object.entries(e).forEach(([n,r])=>t[n]=`${r}`),t}function ym(e,t,n){return e==n.path&&et(t,n.parameters)}var Vs="imperative",ee=function(e){return e[e.NavigationStart=0]="NavigationStart",e[e.NavigationEnd=1]="NavigationEnd",e[e.NavigationCancel=2]="NavigationCancel",e[e.NavigationError=3]="NavigationError",e[e.RoutesRecognized=4]="RoutesRecognized",e[e.ResolveStart=5]="ResolveStart",e[e.ResolveEnd=6]="ResolveEnd",e[e.GuardsCheckStart=7]="GuardsCheckStart",e[e.GuardsCheckEnd=8]="GuardsCheckEnd",e[e.RouteConfigLoadStart=9]="RouteConfigLoadStart",e[e.RouteConfigLoadEnd=10]="RouteConfigLoadEnd",e[e.ChildActivationStart=11]="ChildActivationStart",e[e.ChildActivationEnd=12]="ChildActivationEnd",e[e.ActivationStart=13]="ActivationStart",e[e.ActivationEnd=14]="ActivationEnd",e[e.Scroll=15]="Scroll",e[e.NavigationSkipped=16]="NavigationSkipped",e}(ee||{}),_e=class{id;url;constructor(t,n){this.id=t,this.url=n}},Ft=class extends _e{type=ee.NavigationStart;navigationTrigger;restoredState;constructor(t,n,r="imperative",o=null){super(t,n),this.navigationTrigger=r,this.restoredState=o}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}},je=class extends _e{urlAfterRedirects;type=ee.NavigationEnd;constructor(t,n,r){super(t,n),this.urlAfterRedirects=r}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}},be=function(e){return e[e.Redirect=0]="Redirect",e[e.SupersededByNewNavigation=1]="SupersededByNewNavigation",e[e.NoDataFromResolver=2]="NoDataFromResolver",e[e.GuardRejected=3]="GuardRejected",e}(be||{}),ur=function(e){return e[e.IgnoredSameUrlNavigation=0]="IgnoredSameUrlNavigation",e[e.IgnoredByUrlHandlingStrategy=1]="IgnoredByUrlHandlingStrategy",e}(ur||{}),tt=class extends _e{reason;code;type=ee.NavigationCancel;constructor(t,n,r,o){super(t,n),this.reason=r,this.code=o}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}},rt=class extends _e{reason;code;type=ee.NavigationSkipped;constructor(t,n,r,o){super(t,n),this.reason=r,this.code=o}},lr=class extends _e{error;target;type=ee.NavigationError;constructor(t,n,r,o){super(t,n),this.error=r,this.target=o}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}},uo=class extends _e{urlAfterRedirects;state;type=ee.RoutesRecognized;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},$s=class extends _e{urlAfterRedirects;state;type=ee.GuardsCheckStart;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Hs=class extends _e{urlAfterRedirects;state;shouldActivate;type=ee.GuardsCheckEnd;constructor(t,n,r,o,i){super(t,n),this.urlAfterRedirects=r,this.state=o,this.shouldActivate=i}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}},zs=class extends _e{urlAfterRedirects;state;type=ee.ResolveStart;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},qs=class extends _e{urlAfterRedirects;state;type=ee.ResolveEnd;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Gs=class{route;type=ee.RouteConfigLoadStart;constructor(t){this.route=t}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}},Ws=class{route;type=ee.RouteConfigLoadEnd;constructor(t){this.route=t}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}},Zs=class{snapshot;type=ee.ChildActivationStart;constructor(t){this.snapshot=t}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Ys=class{snapshot;type=ee.ChildActivationEnd;constructor(t){this.snapshot=t}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Qs=class{snapshot;type=ee.ActivationStart;constructor(t){this.snapshot=t}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Ks=class{snapshot;type=ee.ActivationEnd;constructor(t){this.snapshot=t}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},dr=class{routerEvent;position;anchor;type=ee.Scroll;constructor(t,n,r){this.routerEvent=t,this.position=n,this.anchor=r}toString(){let t=this.position?`${this.position[0]}, ${this.position[1]}`:null;return`Scroll(anchor: '${this.anchor}', position: '${t}')`}},lo=class{},fr=class{url;navigationBehaviorOptions;constructor(t,n){this.url=t,this.navigationBehaviorOptions=n}};function cT(e,t){return e.providers&&!e._injector&&(e._injector=qr(e.providers,t,`Route: ${e.path}`)),e._injector??t}function Le(e){return e.outlet||R}function uT(e,t){let n=e.filter(r=>Le(r)===t);return n.push(...e.filter(r=>Le(r)!==t)),n}function yo(e){if(!e)return null;if(e.routeConfig?._injector)return e.routeConfig._injector;for(let t=e.parent;t;t=t.parent){let n=t.routeConfig;if(n?._loadedInjector)return n._loadedInjector;if(n?._injector)return n._injector}return null}var Js=class{rootInjector;outlet=null;route=null;children;attachRef=null;get injector(){return yo(this.route?.snapshot)??this.rootInjector}constructor(t){this.rootInjector=t,this.children=new vn(this.rootInjector)}},vn=(()=>{class e{rootInjector;contexts=new Map;constructor(n){this.rootInjector=n}onChildOutletCreated(n,r){let o=this.getOrCreateContext(n);o.outlet=r,this.contexts.set(n,o)}onChildOutletDestroyed(n){let r=this.getContext(n);r&&(r.outlet=null,r.attachRef=null)}onOutletDeactivated(){let n=this.contexts;return this.contexts=new Map,n}onOutletReAttached(n){this.contexts=n}getOrCreateContext(n){let r=this.getContext(n);return r||(r=new Js(this.rootInjector),this.contexts.set(n,r)),r}getContext(n){return this.contexts.get(n)||null}static \u0275fac=function(r){return new(r||e)(E(se))};static \u0275prov=m({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Xs=class{_root;constructor(t){this._root=t}get root(){return this._root.value}parent(t){let n=this.pathFromRoot(t);return n.length>1?n[n.length-2]:null}children(t){let n=Hl(t,this._root);return n?n.children.map(r=>r.value):[]}firstChild(t){let n=Hl(t,this._root);return n&&n.children.length>0?n.children[0].value:null}siblings(t){let n=zl(t,this._root);return n.length<2?[]:n[n.length-2].children.map(o=>o.value).filter(o=>o!==t)}pathFromRoot(t){return zl(t,this._root).map(n=>n.value)}};function Hl(e,t){if(e===t.value)return t;for(let n of t.children){let r=Hl(e,n);if(r)return r}return null}function zl(e,t){if(e===t.value)return[t];for(let n of t.children){let r=zl(e,n);if(r.length)return r.unshift(t),r}return[]}var Te=class{value;children;constructor(t,n){this.value=t,this.children=n}toString(){return`TreeNode(${this.value})`}};function ir(e){let t={};return e&&e.children.forEach(n=>t[n.value.outlet]=n),t}var fo=class extends Xs{snapshot;constructor(t,n){super(t),this.snapshot=n,Jl(this,t)}toString(){return this.snapshot.toString()}};function jm(e){let t=lT(e),n=new z([new kt("",{})]),r=new z({}),o=new z({}),i=new z({}),s=new z(""),a=new Lt(n,r,i,s,o,R,e,t.root);return a.snapshot=t.root,new fo(new Te(a,[]),t)}function lT(e){let t={},n={},r={},o="",i=new pn([],t,r,o,n,R,e,null,{});return new ho("",new Te(i,[]))}var Lt=class{urlSubject;paramsSubject;queryParamsSubject;fragmentSubject;dataSubject;outlet;component;snapshot;_futureSnapshot;_routerState;_paramMap;_queryParamMap;title;url;params;queryParams;fragment;data;constructor(t,n,r,o,i,s,a,c){this.urlSubject=t,this.paramsSubject=n,this.queryParamsSubject=r,this.fragmentSubject=o,this.dataSubject=i,this.outlet=s,this.component=a,this._futureSnapshot=c,this.title=this.dataSubject?.pipe(_(u=>u[vo]))??C(void 0),this.url=t,this.params=n,this.queryParams=r,this.fragment=o,this.data=i}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=this.params.pipe(_(t=>gn(t))),this._paramMap}get queryParamMap(){return this._queryParamMap??=this.queryParams.pipe(_(t=>gn(t))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}};function ea(e,t,n="emptyOnly"){let r,{routeConfig:o}=e;return t!==null&&(n==="always"||o?.path===""||!t.component&&!t.routeConfig?.loadComponent)?r={params:D(D({},t.params),e.params),data:D(D({},t.data),e.data),resolve:D(D(D(D({},e.data),t.data),o?.data),e._resolvedData)}:r={params:D({},e.params),data:D({},e.data),resolve:D(D({},e.data),e._resolvedData??{})},o&&Um(o)&&(r.resolve[vo]=o.title),r}var pn=class{url;params;queryParams;fragment;data;outlet;component;routeConfig;_resolve;_resolvedData;_routerState;_paramMap;_queryParamMap;get title(){return this.data?.[vo]}constructor(t,n,r,o,i,s,a,c,u){this.url=t,this.params=n,this.queryParams=r,this.fragment=o,this.data=i,this.outlet=s,this.component=a,this.routeConfig=c,this._resolve=u}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=gn(this.params),this._paramMap}get queryParamMap(){return this._queryParamMap??=gn(this.queryParams),this._queryParamMap}toString(){let t=this.url.map(r=>r.toString()).join("/"),n=this.routeConfig?this.routeConfig.path:"";return`Route(url:'${t}', path:'${n}')`}},ho=class extends Xs{url;constructor(t,n){super(n),this.url=t,Jl(this,n)}toString(){return Vm(this._root)}};function Jl(e,t){t.value._routerState=e,t.children.forEach(n=>Jl(e,n))}function Vm(e){let t=e.children.length>0?` { ${e.children.map(Vm).join(", ")} } `:"";return`${e.value}${t}`}function Fl(e){if(e.snapshot){let t=e.snapshot,n=e._futureSnapshot;e.snapshot=n,et(t.queryParams,n.queryParams)||e.queryParamsSubject.next(n.queryParams),t.fragment!==n.fragment&&e.fragmentSubject.next(n.fragment),et(t.params,n.params)||e.paramsSubject.next(n.params),jS(t.url,n.url)||e.urlSubject.next(n.url),et(t.data,n.data)||e.dataSubject.next(n.data)}else e.snapshot=e._futureSnapshot,e.dataSubject.next(e._futureSnapshot.data)}function ql(e,t){let n=et(e.params,t.params)&&$S(e.url,t.url),r=!e.parent!=!t.parent;return n&&!r&&(!e.parent||ql(e.parent,t.parent))}function Um(e){return typeof e.title=="string"||e.title===null}var Bm=new w(""),Xl=(()=>{class e{activated=null;get activatedComponentRef(){return this.activated}_activatedRoute=null;name=R;activateEvents=new ye;deactivateEvents=new ye;attachEvents=new ye;detachEvents=new ye;routerOutletData=zh(void 0);parentContexts=p(vn);location=p(Et);changeDetector=p(er);inputBinder=p(Do,{optional:!0});supportsBindingToComponentInputs=!0;ngOnChanges(n){if(n.name){let{firstChange:r,previousValue:o}=n.name;if(r)return;this.isTrackedInParentContexts(o)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(o)),this.initializeOutletWithName()}}ngOnDestroy(){this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),this.inputBinder?.unsubscribeFromRouteData(this)}isTrackedInParentContexts(n){return this.parentContexts.getContext(n)?.outlet===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;let n=this.parentContexts.getContext(this.name);n?.route&&(n.attachRef?this.attach(n.attachRef,n.route):this.activateWith(n.route,n.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new y(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new y(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new y(4012,!1);this.location.detach();let n=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(n.instance),n}attach(n,r){this.activated=n,this._activatedRoute=r,this.location.insert(n.hostView),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(n.instance)}deactivate(){if(this.activated){let n=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(n)}}activateWith(n,r){if(this.isActivated)throw new y(4013,!1);this._activatedRoute=n;let o=this.location,s=n.snapshot.component,a=this.parentContexts.getOrCreateContext(this.name).children,c=new Gl(n,a,o.injector,this.routerOutletData);this.activated=o.createComponent(s,{index:o.length,injector:c,environmentInjector:r}),this.changeDetector.markForCheck(),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}static \u0275fac=function(r){return new(r||e)};static \u0275dir=ln({type:e,selectors:[["router-outlet"]],inputs:{name:"name",routerOutletData:[1,"routerOutletData"]},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],features:[$r]})}return e})(),Gl=class{route;childContexts;parent;outletData;constructor(t,n,r,o){this.route=t,this.childContexts=n,this.parent=r,this.outletData=o}get(t,n){return t===Lt?this.route:t===vn?this.childContexts:t===Bm?this.outletData:this.parent.get(t,n)}},Do=new w(""),ed=(()=>{class e{outletDataSubscriptions=new Map;bindActivatedRouteToOutletComponent(n){this.unsubscribeFromRouteData(n),this.subscribeToRouteData(n)}unsubscribeFromRouteData(n){this.outletDataSubscriptions.get(n)?.unsubscribe(),this.outletDataSubscriptions.delete(n)}subscribeToRouteData(n){let{activatedRoute:r}=n,o=zt([r.queryParams,r.params,r.data]).pipe(me(([i,s,a],c)=>(a=D(D(D({},i),s),a),c===0?C(a):Promise.resolve(a)))).subscribe(i=>{if(!n.isActivated||!n.activatedComponentRef||n.activatedRoute!==r||r.component===null){this.unsubscribeFromRouteData(n);return}let s=kg(r.component);if(!s){this.unsubscribeFromRouteData(n);return}for(let{templateName:a}of s.inputs)n.activatedComponentRef.setInput(a,i[a])});this.outletDataSubscriptions.set(n,o)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=m({token:e,factory:e.\u0275fac})}return e})(),td=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275cmp=eg({type:e,selectors:[["ng-component"]],exportAs:["emptyRouterOutlet"],decls:1,vars:0,template:function(r,o){r&1&&ll(0,"router-outlet")},dependencies:[Xl],encapsulation:2})}return e})();function nd(e){let t=e.children&&e.children.map(nd),n=t?j(D({},e),{children:t}):D({},e);return!n.component&&!n.loadComponent&&(t||n.loadChildren)&&n.outlet&&n.outlet!==R&&(n.component=td),n}function dT(e,t,n){let r=po(e,t._root,n?n._root:void 0);return new fo(r,t)}function po(e,t,n){if(n&&e.shouldReuseRoute(t.value,n.value.snapshot)){let r=n.value;r._futureSnapshot=t.value;let o=fT(e,t,n);return new Te(r,o)}else{if(e.shouldAttach(t.value)){let i=e.retrieve(t.value);if(i!==null){let s=i.route;return s.value._futureSnapshot=t.value,s.children=t.children.map(a=>po(e,a)),s}}let r=hT(t.value),o=t.children.map(i=>po(e,i));return new Te(r,o)}}function fT(e,t,n){return t.children.map(r=>{for(let o of n.children)if(e.shouldReuseRoute(r.value,o.value.snapshot))return po(e,r,o);return po(e,r)})}function hT(e){return new Lt(new z(e.url),new z(e.params),new z(e.queryParams),new z(e.fragment),new z(e.data),e.outlet,e.component,e)}var hr=class{redirectTo;navigationBehaviorOptions;constructor(t,n){this.redirectTo=t,this.navigationBehaviorOptions=n}},$m="ngNavigationCancelingError";function ta(e,t){let{redirectTo:n,navigationBehaviorOptions:r}=cr(t)?{redirectTo:t,navigationBehaviorOptions:void 0}:t,o=Hm(!1,be.Redirect);return o.url=n,o.navigationBehaviorOptions=r,o}function Hm(e,t){let n=new Error(`NavigationCancelingError: ${e||""}`);return n[$m]=!0,n.cancellationCode=t,n}function pT(e){return zm(e)&&cr(e.url)}function zm(e){return!!e&&e[$m]}var gT=(e,t,n,r)=>_(o=>(new Wl(t,o.targetRouterState,o.currentRouterState,n,r).activate(e),o)),Wl=class{routeReuseStrategy;futureState;currState;forwardEvent;inputBindingEnabled;constructor(t,n,r,o,i){this.routeReuseStrategy=t,this.futureState=n,this.currState=r,this.forwardEvent=o,this.inputBindingEnabled=i}activate(t){let n=this.futureState._root,r=this.currState?this.currState._root:null;this.deactivateChildRoutes(n,r,t),Fl(this.futureState.root),this.activateChildRoutes(n,r,t)}deactivateChildRoutes(t,n,r){let o=ir(n);t.children.forEach(i=>{let s=i.value.outlet;this.deactivateRoutes(i,o[s],r),delete o[s]}),Object.values(o).forEach(i=>{this.deactivateRouteAndItsChildren(i,r)})}deactivateRoutes(t,n,r){let o=t.value,i=n?n.value:null;if(o===i)if(o.component){let s=r.getContext(o.outlet);s&&this.deactivateChildRoutes(t,n,s.children)}else this.deactivateChildRoutes(t,n,r);else i&&this.deactivateRouteAndItsChildren(n,r)}deactivateRouteAndItsChildren(t,n){t.value.component&&this.routeReuseStrategy.shouldDetach(t.value.snapshot)?this.detachAndStoreRouteSubtree(t,n):this.deactivateRouteAndOutlet(t,n)}detachAndStoreRouteSubtree(t,n){let r=n.getContext(t.value.outlet),o=r&&t.value.component?r.children:n,i=ir(t);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);if(r&&r.outlet){let s=r.outlet.detach(),a=r.children.onOutletDeactivated();this.routeReuseStrategy.store(t.value.snapshot,{componentRef:s,route:t,contexts:a})}}deactivateRouteAndOutlet(t,n){let r=n.getContext(t.value.outlet),o=r&&t.value.component?r.children:n,i=ir(t);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);r&&(r.outlet&&(r.outlet.deactivate(),r.children.onOutletDeactivated()),r.attachRef=null,r.route=null)}activateChildRoutes(t,n,r){let o=ir(n);t.children.forEach(i=>{this.activateRoutes(i,o[i.value.outlet],r),this.forwardEvent(new Ks(i.value.snapshot))}),t.children.length&&this.forwardEvent(new Ys(t.value.snapshot))}activateRoutes(t,n,r){let o=t.value,i=n?n.value:null;if(Fl(o),o===i)if(o.component){let s=r.getOrCreateContext(o.outlet);this.activateChildRoutes(t,n,s.children)}else this.activateChildRoutes(t,n,r);else if(o.component){let s=r.getOrCreateContext(o.outlet);if(this.routeReuseStrategy.shouldAttach(o.snapshot)){let a=this.routeReuseStrategy.retrieve(o.snapshot);this.routeReuseStrategy.store(o.snapshot,null),s.children.onOutletReAttached(a.contexts),s.attachRef=a.componentRef,s.route=a.route.value,s.outlet&&s.outlet.attach(a.componentRef,a.route.value),Fl(a.route.value),this.activateChildRoutes(t,null,s.children)}else s.attachRef=null,s.route=o,s.outlet&&s.outlet.activateWith(o,s.injector),this.activateChildRoutes(t,null,s.children)}else this.activateChildRoutes(t,null,r)}},na=class{path;route;constructor(t){this.path=t,this.route=this.path[this.path.length-1]}},ar=class{component;route;constructor(t,n){this.component=t,this.route=n}};function mT(e,t,n){let r=e._root,o=t?t._root:null;return io(r,o,n,[r.value])}function vT(e){let t=e.routeConfig?e.routeConfig.canActivateChild:null;return!t||t.length===0?null:{node:e,guards:t}}function gr(e,t){let n=Symbol(),r=t.get(e,n);return r===n?typeof e=="function"&&!Bf(e)?e:t.get(e):r}function io(e,t,n,r,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=ir(t);return e.children.forEach(s=>{yT(s,i[s.value.outlet],n,r.concat([s.value]),o),delete i[s.value.outlet]}),Object.entries(i).forEach(([s,a])=>ao(a,n.getContext(s),o)),o}function yT(e,t,n,r,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=e.value,s=t?t.value:null,a=n?n.getContext(e.value.outlet):null;if(s&&i.routeConfig===s.routeConfig){let c=DT(s,i,i.routeConfig.runGuardsAndResolvers);c?o.canActivateChecks.push(new na(r)):(i.data=s.data,i._resolvedData=s._resolvedData),i.component?io(e,t,a?a.children:null,r,o):io(e,t,n,r,o),c&&a&&a.outlet&&a.outlet.isActivated&&o.canDeactivateChecks.push(new ar(a.outlet.component,s))}else s&&ao(t,a,o),o.canActivateChecks.push(new na(r)),i.component?io(e,null,a?a.children:null,r,o):io(e,null,n,r,o);return o}function DT(e,t,n){if(typeof n=="function")return n(e,t);switch(n){case"pathParamsChange":return!hn(e.url,t.url);case"pathParamsOrQueryParamsChange":return!hn(e.url,t.url)||!et(e.queryParams,t.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!ql(e,t)||!et(e.queryParams,t.queryParams);case"paramsChange":default:return!ql(e,t)}}function ao(e,t,n){let r=ir(e),o=e.value;Object.entries(r).forEach(([i,s])=>{o.component?t?ao(s,t.children.getContext(i),n):ao(s,null,n):ao(s,t,n)}),o.component?t&&t.outlet&&t.outlet.isActivated?n.canDeactivateChecks.push(new ar(t.outlet.component,o)):n.canDeactivateChecks.push(new ar(null,o)):n.canDeactivateChecks.push(new ar(null,o))}function Eo(e){return typeof e=="function"}function ET(e){return typeof e=="boolean"}function wT(e){return e&&Eo(e.canLoad)}function IT(e){return e&&Eo(e.canActivate)}function CT(e){return e&&Eo(e.canActivateChild)}function bT(e){return e&&Eo(e.canDeactivate)}function ST(e){return e&&Eo(e.canMatch)}function qm(e){return e instanceof ct||e?.name==="EmptyError"}var Ps=Symbol("INITIAL_VALUE");function pr(){return me(e=>zt(e.map(t=>t.pipe(Be(1),za(Ps)))).pipe(_(t=>{for(let n of t)if(n!==!0){if(n===Ps)return Ps;if(n===!1||TT(n))return n}return!0}),re(t=>t!==Ps),Be(1)))}function TT(e){return cr(e)||e instanceof hr}function _T(e,t){return Z(n=>{let{targetSnapshot:r,currentSnapshot:o,guards:{canActivateChecks:i,canDeactivateChecks:s}}=n;return s.length===0&&i.length===0?C(j(D({},n),{guardsResult:!0})):MT(s,r,o,e).pipe(Z(a=>a&&ET(a)?RT(r,i,e,t):C(a)),_(a=>j(D({},n),{guardsResult:a})))})}function MT(e,t,n,r){return $(e).pipe(Z(o=>kT(o.component,o.route,n,t,r)),ut(o=>o!==!0,!0))}function RT(e,t,n,r){return $(t).pipe(Ue(o=>_n(xT(o.route.parent,r),NT(o.route,r),OT(e,o.path,n),AT(e,o.route,n))),ut(o=>o!==!0,!0))}function NT(e,t){return e!==null&&t&&t(new Qs(e)),C(!0)}function xT(e,t){return e!==null&&t&&t(new Zs(e)),C(!0)}function AT(e,t,n){let r=t.routeConfig?t.routeConfig.canActivate:null;if(!r||r.length===0)return C(!0);let o=r.map(i=>ii(()=>{let s=yo(t)??n,a=gr(i,s),c=IT(a)?a.canActivate(t,e):we(s,()=>a(t,e));return jt(c).pipe(ut())}));return C(o).pipe(pr())}function OT(e,t,n){let r=t[t.length-1],i=t.slice(0,t.length-1).reverse().map(s=>vT(s)).filter(s=>s!==null).map(s=>ii(()=>{let a=s.guards.map(c=>{let u=yo(s.node)??n,l=gr(c,u),d=CT(l)?l.canActivateChild(r,e):we(u,()=>l(r,e));return jt(d).pipe(ut())});return C(a).pipe(pr())}));return C(i).pipe(pr())}function kT(e,t,n,r,o){let i=t&&t.routeConfig?t.routeConfig.canDeactivate:null;if(!i||i.length===0)return C(!0);let s=i.map(a=>{let c=yo(t)??o,u=gr(a,c),l=bT(u)?u.canDeactivate(e,t,n,r):we(c,()=>u(e,t,n,r));return jt(l).pipe(ut())});return C(s).pipe(pr())}function PT(e,t,n,r){let o=t.canLoad;if(o===void 0||o.length===0)return C(!0);let i=o.map(s=>{let a=gr(s,e),c=wT(a)?a.canLoad(t,n):we(e,()=>a(t,n));return jt(c)});return C(i).pipe(pr(),Gm(r))}function Gm(e){return ka(W(t=>{if(typeof t!="boolean")throw ta(e,t)}),_(t=>t===!0))}function FT(e,t,n,r){let o=t.canMatch;if(!o||o.length===0)return C(!0);let i=o.map(s=>{let a=gr(s,e),c=ST(a)?a.canMatch(t,n):we(e,()=>a(t,n));return jt(c)});return C(i).pipe(pr(),Gm(r))}var go=class{segmentGroup;constructor(t){this.segmentGroup=t||null}},mo=class extends Error{urlTree;constructor(t){super(),this.urlTree=t}};function or(e){return Ct(new go(e))}function LT(e){return Ct(new y(4e3,!1))}function jT(e){return Ct(Hm(!1,be.GuardRejected))}var Zl=class{urlSerializer;urlTree;constructor(t,n){this.urlSerializer=t,this.urlTree=n}lineralizeSegments(t,n){let r=[],o=n.root;for(;;){if(r=r.concat(o.segments),o.numberOfChildren===0)return C(r);if(o.numberOfChildren>1||!o.children[R])return LT(`${t.redirectTo}`);o=o.children[R]}}applyRedirectCommands(t,n,r,o,i){if(typeof n!="string"){let a=n,{queryParams:c,fragment:u,routeConfig:l,url:d,outlet:h,params:f,data:g,title:v}=o,I=we(i,()=>a({params:f,data:g,queryParams:c,fragment:u,routeConfig:l,url:d,outlet:h,title:v}));if(I instanceof nt)throw new mo(I);n=I}let s=this.applyRedirectCreateUrlTree(n,this.urlSerializer.parse(n),t,r);if(n[0]==="/")throw new mo(s);return s}applyRedirectCreateUrlTree(t,n,r,o){let i=this.createSegmentGroup(t,n.root,r,o);return new nt(i,this.createQueryParams(n.queryParams,this.urlTree.queryParams),n.fragment)}createQueryParams(t,n){let r={};return Object.entries(t).forEach(([o,i])=>{if(typeof i=="string"&&i[0]===":"){let a=i.substring(1);r[o]=n[a]}else r[o]=i}),r}createSegmentGroup(t,n,r,o){let i=this.createSegments(t,n.segments,r,o),s={};return Object.entries(n.children).forEach(([a,c])=>{s[a]=this.createSegmentGroup(t,c,r,o)}),new L(i,s)}createSegments(t,n,r,o){return n.map(i=>i.path[0]===":"?this.findPosParam(t,i,o):this.findOrReturn(i,r))}findPosParam(t,n,r){let o=r[n.path.substring(1)];if(!o)throw new y(4001,!1);return o}findOrReturn(t,n){let r=0;for(let o of n){if(o.path===t.path)return n.splice(r),o;r++}return t}},Yl={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function VT(e,t,n,r,o){let i=Wm(e,t,n);return i.matched?(r=cT(t,r),FT(r,t,n,o).pipe(_(s=>s===!0?i:D({},Yl)))):C(i)}function Wm(e,t,n){if(t.path==="**")return UT(n);if(t.path==="")return t.pathMatch==="full"&&(e.hasChildren()||n.length>0)?D({},Yl):{matched:!0,consumedSegments:[],remainingSegments:n,parameters:{},positionalParamSegments:{}};let o=(t.matcher||Im)(n,e,t);if(!o)return D({},Yl);let i={};Object.entries(o.posParams??{}).forEach(([a,c])=>{i[a]=c.path});let s=o.consumed.length>0?D(D({},i),o.consumed[o.consumed.length-1].parameters):i;return{matched:!0,consumedSegments:o.consumed,remainingSegments:n.slice(o.consumed.length),parameters:s,positionalParamSegments:o.posParams??{}}}function UT(e){return{matched:!0,parameters:e.length>0?bm(e).parameters:{},consumedSegments:e,remainingSegments:[],positionalParamSegments:{}}}function Dm(e,t,n,r){return n.length>0&&HT(e,n,r)?{segmentGroup:new L(t,$T(r,new L(n,e.children))),slicedSegments:[]}:n.length===0&&zT(e,n,r)?{segmentGroup:new L(e.segments,BT(e,n,r,e.children)),slicedSegments:n}:{segmentGroup:new L(e.segments,e.children),slicedSegments:n}}function BT(e,t,n,r){let o={};for(let i of n)if(oa(e,t,i)&&!r[Le(i)]){let s=new L([],{});o[Le(i)]=s}return D(D({},r),o)}function $T(e,t){let n={};n[R]=t;for(let r of e)if(r.path===""&&Le(r)!==R){let o=new L([],{});n[Le(r)]=o}return n}function HT(e,t,n){return n.some(r=>oa(e,t,r)&&Le(r)!==R)}function zT(e,t,n){return n.some(r=>oa(e,t,r))}function oa(e,t,n){return(e.hasChildren()||t.length>0)&&n.pathMatch==="full"?!1:n.path===""}function qT(e,t,n){return t.length===0&&!e.children[n]}var Ql=class{};function GT(e,t,n,r,o,i,s="emptyOnly"){return new Kl(e,t,n,r,o,s,i).recognize()}var WT=31,Kl=class{injector;configLoader;rootComponentType;config;urlTree;paramsInheritanceStrategy;urlSerializer;applyRedirects;absoluteRedirectCount=0;allowRedirects=!0;constructor(t,n,r,o,i,s,a){this.injector=t,this.configLoader=n,this.rootComponentType=r,this.config=o,this.urlTree=i,this.paramsInheritanceStrategy=s,this.urlSerializer=a,this.applyRedirects=new Zl(this.urlSerializer,this.urlTree)}noMatchError(t){return new y(4002,`'${t.segmentGroup}'`)}recognize(){let t=Dm(this.urlTree.root,[],[],this.config).segmentGroup;return this.match(t).pipe(_(({children:n,rootSnapshot:r})=>{let o=new Te(r,n),i=new ho("",o),s=Om(r,[],this.urlTree.queryParams,this.urlTree.fragment);return s.queryParams=this.urlTree.queryParams,i.url=this.urlSerializer.serialize(s),{state:i,tree:s}}))}match(t){let n=new pn([],Object.freeze({}),Object.freeze(D({},this.urlTree.queryParams)),this.urlTree.fragment,Object.freeze({}),R,this.rootComponentType,null,{});return this.processSegmentGroup(this.injector,this.config,t,R,n).pipe(_(r=>({children:r,rootSnapshot:n})),te(r=>{if(r instanceof mo)return this.urlTree=r.urlTree,this.match(r.urlTree.root);throw r instanceof go?this.noMatchError(r):r}))}processSegmentGroup(t,n,r,o,i){return r.segments.length===0&&r.hasChildren()?this.processChildren(t,n,r,i):this.processSegment(t,n,r,r.segments,o,!0,i).pipe(_(s=>s instanceof Te?[s]:[]))}processChildren(t,n,r,o){let i=[];for(let s of Object.keys(r.children))s==="primary"?i.unshift(s):i.push(s);return $(i).pipe(Ue(s=>{let a=r.children[s],c=uT(n,s);return this.processSegmentGroup(t,c,a,s,o)}),Ba((s,a)=>(s.push(...a),s)),St(null),Ua(),Z(s=>{if(s===null)return or(r);let a=Zm(s);return ZT(a),C(a)}))}processSegment(t,n,r,o,i,s,a){return $(n).pipe(Ue(c=>this.processSegmentAgainstRoute(c._injector??t,n,c,r,o,i,s,a).pipe(te(u=>{if(u instanceof go)return C(null);throw u}))),ut(c=>!!c),te(c=>{if(qm(c))return qT(r,o,i)?C(new Ql):or(r);throw c}))}processSegmentAgainstRoute(t,n,r,o,i,s,a,c){return Le(r)!==s&&(s===R||!oa(o,i,r))?or(o):r.redirectTo===void 0?this.matchSegmentAgainstRoute(t,o,r,i,s,c):this.allowRedirects&&a?this.expandSegmentAgainstRouteUsingRedirect(t,o,n,r,i,s,c):or(o)}expandSegmentAgainstRouteUsingRedirect(t,n,r,o,i,s,a){let{matched:c,parameters:u,consumedSegments:l,positionalParamSegments:d,remainingSegments:h}=Wm(n,o,i);if(!c)return or(n);typeof o.redirectTo=="string"&&o.redirectTo[0]==="/"&&(this.absoluteRedirectCount++,this.absoluteRedirectCount>WT&&(this.allowRedirects=!1));let f=new pn(i,u,Object.freeze(D({},this.urlTree.queryParams)),this.urlTree.fragment,Em(o),Le(o),o.component??o._loadedComponent??null,o,wm(o)),g=ea(f,a,this.paramsInheritanceStrategy);f.params=Object.freeze(g.params),f.data=Object.freeze(g.data);let v=this.applyRedirects.applyRedirectCommands(l,o.redirectTo,d,f,t);return this.applyRedirects.lineralizeSegments(o,v).pipe(Z(I=>this.processSegment(t,r,n,I.concat(h),s,!1,a)))}matchSegmentAgainstRoute(t,n,r,o,i,s){let a=VT(n,r,o,t,this.urlSerializer);return r.path==="**"&&(n.children={}),a.pipe(me(c=>c.matched?(t=r._injector??t,this.getChildConfig(t,r,o).pipe(me(({routes:u})=>{let l=r._loadedInjector??t,{parameters:d,consumedSegments:h,remainingSegments:f}=c,g=new pn(h,d,Object.freeze(D({},this.urlTree.queryParams)),this.urlTree.fragment,Em(r),Le(r),r.component??r._loadedComponent??null,r,wm(r)),v=ea(g,s,this.paramsInheritanceStrategy);g.params=Object.freeze(v.params),g.data=Object.freeze(v.data);let{segmentGroup:I,slicedSegments:O}=Dm(n,h,f,u);if(O.length===0&&I.hasChildren())return this.processChildren(l,u,I,g).pipe(_(J=>new Te(g,J)));if(u.length===0&&O.length===0)return C(new Te(g,[]));let it=Le(r)===i;return this.processSegment(l,u,I,O,it?R:i,!0,g).pipe(_(J=>new Te(g,J instanceof Te?[J]:[])))}))):or(n)))}getChildConfig(t,n,r){return n.children?C({routes:n.children,injector:t}):n.loadChildren?n._loadedRoutes!==void 0?C({routes:n._loadedRoutes,injector:n._loadedInjector}):PT(t,n,r,this.urlSerializer).pipe(Z(o=>o?this.configLoader.loadChildren(t,n).pipe(W(i=>{n._loadedRoutes=i.routes,n._loadedInjector=i.injector})):jT(n))):C({routes:[],injector:t})}};function ZT(e){e.sort((t,n)=>t.value.outlet===R?-1:n.value.outlet===R?1:t.value.outlet.localeCompare(n.value.outlet))}function YT(e){let t=e.value.routeConfig;return t&&t.path===""}function Zm(e){let t=[],n=new Set;for(let r of e){if(!YT(r)){t.push(r);continue}let o=t.find(i=>r.value.routeConfig===i.value.routeConfig);o!==void 0?(o.children.push(...r.children),n.add(o)):t.push(r)}for(let r of n){let o=Zm(r.children);t.push(new Te(r.value,o))}return t.filter(r=>!n.has(r))}function Em(e){return e.data||{}}function wm(e){return e.resolve||{}}function QT(e,t,n,r,o,i){return Z(s=>GT(e,t,n,r,s.extractedUrl,o,i).pipe(_(({state:a,tree:c})=>j(D({},s),{targetSnapshot:a,urlAfterRedirects:c}))))}function KT(e,t){return Z(n=>{let{targetSnapshot:r,guards:{canActivateChecks:o}}=n;if(!o.length)return C(n);let i=new Set(o.map(c=>c.route)),s=new Set;for(let c of i)if(!s.has(c))for(let u of Ym(c))s.add(u);let a=0;return $(s).pipe(Ue(c=>i.has(c)?JT(c,r,e,t):(c.data=ea(c,c.parent,e).resolve,C(void 0))),W(()=>a++),Mn(1),Z(c=>a===s.size?C(n):ie))})}function Ym(e){let t=e.children.map(n=>Ym(n)).flat();return[e,...t]}function JT(e,t,n,r){let o=e.routeConfig,i=e._resolve;return o?.title!==void 0&&!Um(o)&&(i[vo]=o.title),XT(i,e,t,r).pipe(_(s=>(e._resolvedData=s,e.data=ea(e,e.parent,n).resolve,null)))}function XT(e,t,n,r){let o=Vl(e);if(o.length===0)return C({});let i={};return $(o).pipe(Z(s=>e_(e[s],t,n,r).pipe(ut(),W(a=>{if(a instanceof hr)throw ta(new Pt,a);i[s]=a}))),Mn(1),_(()=>i),te(s=>qm(s)?ie:Ct(s)))}function e_(e,t,n,r){let o=yo(t)??r,i=gr(e,o),s=i.resolve?i.resolve(t,n):we(o,()=>i(t,n));return jt(s)}function Ll(e){return me(t=>{let n=e(t);return n?$(n).pipe(_(()=>t)):C(t)})}var rd=(()=>{class e{buildTitle(n){let r,o=n.root;for(;o!==void 0;)r=this.getResolvedTitleForRoute(o)??r,o=o.children.find(i=>i.outlet===R);return r}getResolvedTitleForRoute(n){return n.data[vo]}static \u0275fac=function(r){return new(r||e)};static \u0275prov=m({token:e,factory:()=>p(Qm),providedIn:"root"})}return e})(),Qm=(()=>{class e extends rd{title;constructor(n){super(),this.title=n}updateTitle(n){let r=this.buildTitle(n);r!==void 0&&this.title.setTitle(r)}static \u0275fac=function(r){return new(r||e)(E(pm))};static \u0275prov=m({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),yn=new w("",{providedIn:"root",factory:()=>({})}),mr=new w(""),ia=(()=>{class e{componentLoaders=new WeakMap;childrenLoaders=new WeakMap;onLoadStartListener;onLoadEndListener;compiler=p(Sg);loadComponent(n){if(this.componentLoaders.get(n))return this.componentLoaders.get(n);if(n._loadedComponent)return C(n._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(n);let r=jt(n.loadComponent()).pipe(_(Jm),W(i=>{this.onLoadEndListener&&this.onLoadEndListener(n),n._loadedComponent=i}),oe(()=>{this.componentLoaders.delete(n)})),o=new Sn(r,()=>new G).pipe(bn());return this.componentLoaders.set(n,o),o}loadChildren(n,r){if(this.childrenLoaders.get(r))return this.childrenLoaders.get(r);if(r._loadedRoutes)return C({routes:r._loadedRoutes,injector:r._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(r);let i=Km(r,this.compiler,n,this.onLoadEndListener).pipe(oe(()=>{this.childrenLoaders.delete(r)})),s=new Sn(i,()=>new G).pipe(bn());return this.childrenLoaders.set(r,s),s}static \u0275fac=function(r){return new(r||e)};static \u0275prov=m({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Km(e,t,n,r){return jt(e.loadChildren()).pipe(_(Jm),Z(o=>o instanceof rl||Array.isArray(o)?C(o):$(t.compileModuleAsync(o))),_(o=>{r&&r(e);let i,s,a=!1;return Array.isArray(o)?(s=o,a=!0):(i=o.create(n).injector,s=i.get(mr,[],{optional:!0,self:!0}).flat()),{routes:s.map(nd),injector:i}}))}function t_(e){return e&&typeof e=="object"&&"default"in e}function Jm(e){return t_(e)?e.default:e}var sa=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=m({token:e,factory:()=>p(n_),providedIn:"root"})}return e})(),n_=(()=>{class e{shouldProcessUrl(n){return!0}extract(n){return n}merge(n,r){return n}static \u0275fac=function(r){return new(r||e)};static \u0275prov=m({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),od=new w(""),id=new w("");function Xm(e,t,n){let r=e.get(id),o=e.get(K);return e.get(U).runOutsideAngular(()=>{if(!o.startViewTransition||r.skipNextTransition)return r.skipNextTransition=!1,new Promise(u=>setTimeout(u));let i,s=new Promise(u=>{i=u}),a=o.startViewTransition(()=>(i(),r_(e))),{onViewTransitionCreated:c}=r;return c&&we(e,()=>c({transition:a,from:t,to:n})),s})}function r_(e){return new Promise(t=>{Pu({read:()=>setTimeout(t)},{injector:e})})}var sd=new w(""),aa=(()=>{class e{currentNavigation=null;currentTransition=null;lastSuccessfulNavigation=null;events=new G;transitionAbortSubject=new G;configLoader=p(ia);environmentInjector=p(se);destroyRef=p(Nt);urlSerializer=p(mn);rootContexts=p(vn);location=p(At);inputBindingEnabled=p(Do,{optional:!0})!==null;titleStrategy=p(rd);options=p(yn,{optional:!0})||{};paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly";urlHandlingStrategy=p(sa);createViewTransition=p(od,{optional:!0});navigationErrorHandler=p(sd,{optional:!0});navigationId=0;get hasRequestedNavigation(){return this.navigationId!==0}transitions;afterPreactivation=()=>C(void 0);rootComponentType=null;destroyed=!1;constructor(){let n=o=>this.events.next(new Gs(o)),r=o=>this.events.next(new Ws(o));this.configLoader.onLoadEndListener=r,this.configLoader.onLoadStartListener=n,this.destroyRef.onDestroy(()=>{this.destroyed=!0})}complete(){this.transitions?.complete()}handleNavigationRequest(n){let r=++this.navigationId;this.transitions?.next(j(D({},n),{extractedUrl:this.urlHandlingStrategy.extract(n.rawUrl),targetSnapshot:null,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null,id:r}))}setupNavigations(n){return this.transitions=new z(null),this.transitions.pipe(re(r=>r!==null),me(r=>{let o=!1,i=!1;return C(r).pipe(me(s=>{if(this.navigationId>r.id)return this.cancelNavigationTransition(r,"",be.SupersededByNewNavigation),ie;this.currentTransition=r,this.currentNavigation={id:s.id,initialUrl:s.rawUrl,extractedUrl:s.extractedUrl,targetBrowserUrl:typeof s.extras.browserUrl=="string"?this.urlSerializer.parse(s.extras.browserUrl):s.extras.browserUrl,trigger:s.source,extras:s.extras,previousNavigation:this.lastSuccessfulNavigation?j(D({},this.lastSuccessfulNavigation),{previousNavigation:null}):null};let a=!n.navigated||this.isUpdatingInternalState()||this.isUpdatedBrowserUrl(),c=s.extras.onSameUrlNavigation??n.onSameUrlNavigation;if(!a&&c!=="reload"){let u="";return this.events.next(new rt(s.id,this.urlSerializer.serialize(s.rawUrl),u,ur.IgnoredSameUrlNavigation)),s.resolve(!1),ie}if(this.urlHandlingStrategy.shouldProcessUrl(s.rawUrl))return C(s).pipe(me(u=>(this.events.next(new Ft(u.id,this.urlSerializer.serialize(u.extractedUrl),u.source,u.restoredState)),u.id!==this.navigationId?ie:Promise.resolve(u))),QT(this.environmentInjector,this.configLoader,this.rootComponentType,n.config,this.urlSerializer,this.paramsInheritanceStrategy),W(u=>{r.targetSnapshot=u.targetSnapshot,r.urlAfterRedirects=u.urlAfterRedirects,this.currentNavigation=j(D({},this.currentNavigation),{finalUrl:u.urlAfterRedirects});let l=new uo(u.id,this.urlSerializer.serialize(u.extractedUrl),this.urlSerializer.serialize(u.urlAfterRedirects),u.targetSnapshot);this.events.next(l)}));if(a&&this.urlHandlingStrategy.shouldProcessUrl(s.currentRawUrl)){let{id:u,extractedUrl:l,source:d,restoredState:h,extras:f}=s,g=new Ft(u,this.urlSerializer.serialize(l),d,h);this.events.next(g);let v=jm(this.rootComponentType).snapshot;return this.currentTransition=r=j(D({},s),{targetSnapshot:v,urlAfterRedirects:l,extras:j(D({},f),{skipLocationChange:!1,replaceUrl:!1})}),this.currentNavigation.finalUrl=l,C(r)}else{let u="";return this.events.next(new rt(s.id,this.urlSerializer.serialize(s.extractedUrl),u,ur.IgnoredByUrlHandlingStrategy)),s.resolve(!1),ie}}),W(s=>{let a=new $s(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot);this.events.next(a)}),_(s=>(this.currentTransition=r=j(D({},s),{guards:mT(s.targetSnapshot,s.currentSnapshot,this.rootContexts)}),r)),_T(this.environmentInjector,s=>this.events.next(s)),W(s=>{if(r.guardsResult=s.guardsResult,s.guardsResult&&typeof s.guardsResult!="boolean")throw ta(this.urlSerializer,s.guardsResult);let a=new Hs(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot,!!s.guardsResult);this.events.next(a)}),re(s=>s.guardsResult?!0:(this.cancelNavigationTransition(s,"",be.GuardRejected),!1)),Ll(s=>{if(s.guards.canActivateChecks.length!==0)return C(s).pipe(W(a=>{let c=new zs(a.id,this.urlSerializer.serialize(a.extractedUrl),this.urlSerializer.serialize(a.urlAfterRedirects),a.targetSnapshot);this.events.next(c)}),me(a=>{let c=!1;return C(a).pipe(KT(this.paramsInheritanceStrategy,this.environmentInjector),W({next:()=>c=!0,complete:()=>{c||this.cancelNavigationTransition(a,"",be.NoDataFromResolver)}}))}),W(a=>{let c=new qs(a.id,this.urlSerializer.serialize(a.extractedUrl),this.urlSerializer.serialize(a.urlAfterRedirects),a.targetSnapshot);this.events.next(c)}))}),Ll(s=>{let a=c=>{let u=[];c.routeConfig?.loadComponent&&!c.routeConfig._loadedComponent&&u.push(this.configLoader.loadComponent(c.routeConfig).pipe(W(l=>{c.component=l}),_(()=>{})));for(let l of c.children)u.push(...a(l));return u};return zt(a(s.targetSnapshot.root)).pipe(St(null),Be(1))}),Ll(()=>this.afterPreactivation()),me(()=>{let{currentSnapshot:s,targetSnapshot:a}=r,c=this.createViewTransition?.(this.environmentInjector,s.root,a.root);return c?$(c).pipe(_(()=>r)):C(r)}),_(s=>{let a=dT(n.routeReuseStrategy,s.targetSnapshot,s.currentRouterState);return this.currentTransition=r=j(D({},s),{targetRouterState:a}),this.currentNavigation.targetRouterState=a,r}),W(()=>{this.events.next(new lo)}),gT(this.rootContexts,n.routeReuseStrategy,s=>this.events.next(s),this.inputBindingEnabled),Be(1),W({next:s=>{o=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new je(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects))),this.titleStrategy?.updateTitle(s.targetRouterState.snapshot),s.resolve(!0)},complete:()=>{o=!0}}),ci(this.transitionAbortSubject.pipe(W(s=>{throw s}))),oe(()=>{!o&&!i&&this.cancelNavigationTransition(r,"",be.SupersededByNewNavigation),this.currentTransition?.id===r.id&&(this.currentNavigation=null,this.currentTransition=null)}),te(s=>{if(this.destroyed)return r.resolve(!1),ie;if(i=!0,zm(s))this.events.next(new tt(r.id,this.urlSerializer.serialize(r.extractedUrl),s.message,s.cancellationCode)),pT(s)?this.events.next(new fr(s.url,s.navigationBehaviorOptions)):r.resolve(!1);else{let a=new lr(r.id,this.urlSerializer.serialize(r.extractedUrl),s,r.targetSnapshot??void 0);try{let c=we(this.environmentInjector,()=>this.navigationErrorHandler?.(a));if(c instanceof hr){let{message:u,cancellationCode:l}=ta(this.urlSerializer,c);this.events.next(new tt(r.id,this.urlSerializer.serialize(r.extractedUrl),u,l)),this.events.next(new fr(c.redirectTo,c.navigationBehaviorOptions))}else throw this.events.next(a),s}catch(c){this.options.resolveNavigationPromiseOnError?r.resolve(!1):r.reject(c)}}return ie}))}))}cancelNavigationTransition(n,r,o){let i=new tt(n.id,this.urlSerializer.serialize(n.extractedUrl),r,o);this.events.next(i),n.resolve(!1)}isUpdatingInternalState(){return this.currentTransition?.extractedUrl.toString()!==this.currentTransition?.currentUrlTree.toString()}isUpdatedBrowserUrl(){let n=this.urlHandlingStrategy.extract(this.urlSerializer.parse(this.location.path(!0))),r=this.currentNavigation?.targetBrowserUrl??this.currentNavigation?.extractedUrl;return n.toString()!==r?.toString()&&!this.currentNavigation?.extras.skipLocationChange}static \u0275fac=function(r){return new(r||e)};static \u0275prov=m({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function o_(e){return e!==Vs}var ev=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=m({token:e,factory:()=>p(i_),providedIn:"root"})}return e})(),ra=class{shouldDetach(t){return!1}store(t,n){}shouldAttach(t){return!1}retrieve(t){return null}shouldReuseRoute(t,n){return t.routeConfig===n.routeConfig}},i_=(()=>{class e extends ra{static \u0275fac=(()=>{let n;return function(o){return(n||(n=Su(e)))(o||e)}})();static \u0275prov=m({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),tv=(()=>{class e{urlSerializer=p(mn);options=p(yn,{optional:!0})||{};canceledNavigationResolution=this.options.canceledNavigationResolution||"replace";location=p(At);urlHandlingStrategy=p(sa);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";currentUrlTree=new nt;getCurrentUrlTree(){return this.currentUrlTree}rawUrlTree=this.currentUrlTree;getRawUrlTree(){return this.rawUrlTree}createBrowserPath({finalUrl:n,initialUrl:r,targetBrowserUrl:o}){let i=n!==void 0?this.urlHandlingStrategy.merge(n,r):r,s=o??i;return s instanceof nt?this.urlSerializer.serialize(s):s}commitTransition({targetRouterState:n,finalUrl:r,initialUrl:o}){r&&n?(this.currentUrlTree=r,this.rawUrlTree=this.urlHandlingStrategy.merge(r,o),this.routerState=n):this.rawUrlTree=o}routerState=jm(null);getRouterState(){return this.routerState}stateMemento=this.createStateMemento();updateStateMemento(){this.stateMemento=this.createStateMemento()}createStateMemento(){return{rawUrlTree:this.rawUrlTree,currentUrlTree:this.currentUrlTree,routerState:this.routerState}}resetInternalState({finalUrl:n}){this.routerState=this.stateMemento.routerState,this.currentUrlTree=this.stateMemento.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,n??this.rawUrlTree)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=m({token:e,factory:()=>p(s_),providedIn:"root"})}return e})(),s_=(()=>{class e extends tv{currentPageId=0;lastSuccessfulId=-1;restoredState(){return this.location.getState()}get browserPageId(){return this.canceledNavigationResolution!=="computed"?this.currentPageId:this.restoredState()?.\u0275routerPageId??this.currentPageId}registerNonRouterCurrentEntryChangeListener(n){return this.location.subscribe(r=>{r.type==="popstate"&&setTimeout(()=>{n(r.url,r.state,"popstate")})})}handleRouterEvent(n,r){n instanceof Ft?this.updateStateMemento():n instanceof rt?this.commitTransition(r):n instanceof uo?this.urlUpdateStrategy==="eager"&&(r.extras.skipLocationChange||this.setBrowserUrl(this.createBrowserPath(r),r)):n instanceof lo?(this.commitTransition(r),this.urlUpdateStrategy==="deferred"&&!r.extras.skipLocationChange&&this.setBrowserUrl(this.createBrowserPath(r),r)):n instanceof tt&&(n.code===be.GuardRejected||n.code===be.NoDataFromResolver)?this.restoreHistory(r):n instanceof lr?this.restoreHistory(r,!0):n instanceof je&&(this.lastSuccessfulId=n.id,this.currentPageId=this.browserPageId)}setBrowserUrl(n,{extras:r,id:o}){let{replaceUrl:i,state:s}=r;if(this.location.isCurrentPathEqualTo(n)||i){let a=this.browserPageId,c=D(D({},s),this.generateNgRouterState(o,a));this.location.replaceState(n,"",c)}else{let a=D(D({},s),this.generateNgRouterState(o,this.browserPageId+1));this.location.go(n,"",a)}}restoreHistory(n,r=!1){if(this.canceledNavigationResolution==="computed"){let o=this.browserPageId,i=this.currentPageId-o;i!==0?this.location.historyGo(i):this.getCurrentUrlTree()===n.finalUrl&&i===0&&(this.resetInternalState(n),this.resetUrlToCurrentUrlTree())}else this.canceledNavigationResolution==="replace"&&(r&&this.resetInternalState(n),this.resetUrlToCurrentUrlTree())}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.getRawUrlTree()),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(n,r){return this.canceledNavigationResolution==="computed"?{navigationId:n,\u0275routerPageId:r}:{navigationId:n}}static \u0275fac=(()=>{let n;return function(o){return(n||(n=Su(e)))(o||e)}})();static \u0275prov=m({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function ca(e,t){e.events.pipe(re(n=>n instanceof je||n instanceof tt||n instanceof lr||n instanceof rt),_(n=>n instanceof je||n instanceof rt?0:(n instanceof tt?n.code===be.Redirect||n.code===be.SupersededByNewNavigation:!1)?2:1),re(n=>n!==2),Be(1)).subscribe(()=>{t()})}var a_={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},c_={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"},ot=(()=>{class e{get currentUrlTree(){return this.stateManager.getCurrentUrlTree()}get rawUrlTree(){return this.stateManager.getRawUrlTree()}disposed=!1;nonRouterCurrentEntryChangeSubscription;console=p(ol);stateManager=p(tv);options=p(yn,{optional:!0})||{};pendingTasks=p(vt);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";navigationTransitions=p(aa);urlSerializer=p(mn);location=p(At);urlHandlingStrategy=p(sa);_events=new G;get events(){return this._events}get routerState(){return this.stateManager.getRouterState()}navigated=!1;routeReuseStrategy=p(ev);onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore";config=p(mr,{optional:!0})?.flat()??[];componentInputBindingEnabled=!!p(Do,{optional:!0});constructor(){this.resetConfig(this.config),this.navigationTransitions.setupNavigations(this).subscribe({error:n=>{this.console.warn(n)}}),this.subscribeToNavigationEvents()}eventsSubscription=new q;subscribeToNavigationEvents(){let n=this.navigationTransitions.events.subscribe(r=>{try{let o=this.navigationTransitions.currentTransition,i=this.navigationTransitions.currentNavigation;if(o!==null&&i!==null){if(this.stateManager.handleRouterEvent(r,i),r instanceof tt&&r.code!==be.Redirect&&r.code!==be.SupersededByNewNavigation)this.navigated=!0;else if(r instanceof je)this.navigated=!0;else if(r instanceof fr){let s=r.navigationBehaviorOptions,a=this.urlHandlingStrategy.merge(r.url,o.currentRawUrl),c=D({browserUrl:o.extras.browserUrl,info:o.extras.info,skipLocationChange:o.extras.skipLocationChange,replaceUrl:o.extras.replaceUrl||this.urlUpdateStrategy==="eager"||o_(o.source)},s);this.scheduleNavigation(a,Vs,null,c,{resolve:o.resolve,reject:o.reject,promise:o.promise})}}l_(r)&&this._events.next(r)}catch(o){this.navigationTransitions.transitionAbortSubject.next(o)}});this.eventsSubscription.add(n)}resetRootComponentType(n){this.routerState.root.component=n,this.navigationTransitions.rootComponentType=n}initialNavigation(){this.setUpLocationChangeListener(),this.navigationTransitions.hasRequestedNavigation||this.navigateToSyncWithBrowser(this.location.path(!0),Vs,this.stateManager.restoredState())}setUpLocationChangeListener(){this.nonRouterCurrentEntryChangeSubscription??=this.stateManager.registerNonRouterCurrentEntryChangeListener((n,r,o)=>{this.navigateToSyncWithBrowser(n,o,r)})}navigateToSyncWithBrowser(n,r,o){let i={replaceUrl:!0},s=o?.navigationId?o:null;if(o){let c=D({},o);delete c.navigationId,delete c.\u0275routerPageId,Object.keys(c).length!==0&&(i.state=c)}let a=this.parseUrl(n);this.scheduleNavigation(a,r,s,i)}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(n){this.config=n.map(nd),this.navigated=!1}ngOnDestroy(){this.dispose()}dispose(){this._events.unsubscribe(),this.navigationTransitions.complete(),this.nonRouterCurrentEntryChangeSubscription&&(this.nonRouterCurrentEntryChangeSubscription.unsubscribe(),this.nonRouterCurrentEntryChangeSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(n,r={}){let{relativeTo:o,queryParams:i,fragment:s,queryParamsHandling:a,preserveFragment:c}=r,u=c?this.currentUrlTree.fragment:s,l=null;switch(a??this.options.defaultQueryParamsHandling){case"merge":l=D(D({},this.currentUrlTree.queryParams),i);break;case"preserve":l=this.currentUrlTree.queryParams;break;default:l=i||null}l!==null&&(l=this.removeEmptyProps(l));let d;try{let h=o?o.snapshot:this.routerState.snapshot.root;d=km(h)}catch{(typeof n[0]!="string"||n[0][0]!=="/")&&(n=[]),d=this.currentUrlTree.root}return Pm(d,n,l,u??null)}navigateByUrl(n,r={skipLocationChange:!1}){let o=cr(n)?n:this.parseUrl(n),i=this.urlHandlingStrategy.merge(o,this.rawUrlTree);return this.scheduleNavigation(i,Vs,null,r)}navigate(n,r={skipLocationChange:!1}){return u_(n),this.navigateByUrl(this.createUrlTree(n,r),r)}serializeUrl(n){return this.urlSerializer.serialize(n)}parseUrl(n){try{return this.urlSerializer.parse(n)}catch{return this.urlSerializer.parse("/")}}isActive(n,r){let o;if(r===!0?o=D({},a_):r===!1?o=D({},c_):o=r,cr(n))return gm(this.currentUrlTree,n,o);let i=this.parseUrl(n);return gm(this.currentUrlTree,i,o)}removeEmptyProps(n){return Object.entries(n).reduce((r,[o,i])=>(i!=null&&(r[o]=i),r),{})}scheduleNavigation(n,r,o,i,s){if(this.disposed)return Promise.resolve(!1);let a,c,u;s?(a=s.resolve,c=s.reject,u=s.promise):u=new Promise((d,h)=>{a=d,c=h});let l=this.pendingTasks.add();return ca(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(l))}),this.navigationTransitions.handleNavigationRequest({source:r,restoredState:o,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,rawUrl:n,extras:i,resolve:a,reject:c,promise:u,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),u.catch(d=>Promise.reject(d))}static \u0275fac=function(r){return new(r||e)};static \u0275prov=m({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function u_(e){for(let t=0;t<e.length;t++)if(e[t]==null)throw new y(4008,!1)}function l_(e){return!(e instanceof lo)&&!(e instanceof fr)}var wo=class{};var nv=(()=>{class e{router;injector;preloadingStrategy;loader;subscription;constructor(n,r,o,i){this.router=n,this.injector=r,this.preloadingStrategy=o,this.loader=i}setUpPreloading(){this.subscription=this.router.events.pipe(re(n=>n instanceof je),Ue(()=>this.preload())).subscribe(()=>{})}preload(){return this.processRoutes(this.injector,this.router.config)}ngOnDestroy(){this.subscription&&this.subscription.unsubscribe()}processRoutes(n,r){let o=[];for(let i of r){i.providers&&!i._injector&&(i._injector=qr(i.providers,n,`Route: ${i.path}`));let s=i._injector??n,a=i._loadedInjector??s;(i.loadChildren&&!i._loadedRoutes&&i.canLoad===void 0||i.loadComponent&&!i._loadedComponent)&&o.push(this.preloadConfig(s,i)),(i.children||i._loadedRoutes)&&o.push(this.processRoutes(a,i.children??i._loadedRoutes))}return $(o).pipe(bt())}preloadConfig(n,r){return this.preloadingStrategy.preload(r,()=>{let o;r.loadChildren&&r.canLoad===void 0?o=this.loader.loadChildren(n,r):o=C(null);let i=o.pipe(Z(s=>s===null?C(void 0):(r._loadedRoutes=s.routes,r._loadedInjector=s.injector,this.processRoutes(s.injector??n,s.routes))));if(r.loadComponent&&!r._loadedComponent){let s=this.loader.loadComponent(r);return $([i,s]).pipe(bt())}else return i})}static \u0275fac=function(r){return new(r||e)(E(ot),E(se),E(wo),E(ia))};static \u0275prov=m({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),rv=new w(""),d_=(()=>{class e{urlSerializer;transitions;viewportScroller;zone;options;routerEventsSubscription;scrollEventsSubscription;lastId=0;lastSource="imperative";restoredId=0;store={};constructor(n,r,o,i,s={}){this.urlSerializer=n,this.transitions=r,this.viewportScroller=o,this.zone=i,this.options=s,s.scrollPositionRestoration||="disabled",s.anchorScrolling||="disabled"}init(){this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.setHistoryScrollRestoration("manual"),this.routerEventsSubscription=this.createScrollEvents(),this.scrollEventsSubscription=this.consumeScrollEvents()}createScrollEvents(){return this.transitions.events.subscribe(n=>{n instanceof Ft?(this.store[this.lastId]=this.viewportScroller.getScrollPosition(),this.lastSource=n.navigationTrigger,this.restoredId=n.restoredState?n.restoredState.navigationId:0):n instanceof je?(this.lastId=n.id,this.scheduleScrollEvent(n,this.urlSerializer.parse(n.urlAfterRedirects).fragment)):n instanceof rt&&n.code===ur.IgnoredSameUrlNavigation&&(this.lastSource=void 0,this.restoredId=0,this.scheduleScrollEvent(n,this.urlSerializer.parse(n.url).fragment))})}consumeScrollEvents(){return this.transitions.events.subscribe(n=>{n instanceof dr&&(n.position?this.options.scrollPositionRestoration==="top"?this.viewportScroller.scrollToPosition([0,0]):this.options.scrollPositionRestoration==="enabled"&&this.viewportScroller.scrollToPosition(n.position):n.anchor&&this.options.anchorScrolling==="enabled"?this.viewportScroller.scrollToAnchor(n.anchor):this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.scrollToPosition([0,0]))})}scheduleScrollEvent(n,r){this.zone.runOutsideAngular(()=>{setTimeout(()=>{this.zone.run(()=>{this.transitions.events.next(new dr(n,this.lastSource==="popstate"?this.store[this.restoredId]:null,r))})},0)})}ngOnDestroy(){this.routerEventsSubscription?.unsubscribe(),this.scrollEventsSubscription?.unsubscribe()}static \u0275fac=function(r){$p()};static \u0275prov=m({token:e,factory:e.\u0275fac})}return e})();function f_(e){return e.routerState.root}function Io(e,t){return{\u0275kind:e,\u0275providers:t}}function h_(){let e=p(ce);return t=>{let n=e.get(pt);if(t!==n.components[0])return;let r=e.get(ot),o=e.get(ov);e.get(cd)===1&&r.initialNavigation(),e.get(av,null,N.Optional)?.setUpPreloading(),e.get(rv,null,N.Optional)?.init(),r.resetRootComponentType(n.componentTypes[0]),o.closed||(o.next(),o.complete(),o.unsubscribe())}}var ov=new w("",{factory:()=>new G}),cd=new w("",{providedIn:"root",factory:()=>1});function iv(){let e=[{provide:cd,useValue:0},cl(()=>{let t=p(ce);return t.get(ml,Promise.resolve()).then(()=>new Promise(r=>{let o=t.get(ot),i=t.get(ov);ca(o,()=>{r(!0)}),t.get(aa).afterPreactivation=()=>(r(!0),i.closed?C(void 0):i),o.initialNavigation()}))})];return Io(2,e)}function sv(){let e=[cl(()=>{p(ot).setUpLocationChangeListener()}),{provide:cd,useValue:2}];return Io(3,e)}var av=new w("");function cv(e){return Io(0,[{provide:av,useExisting:nv},{provide:wo,useExisting:e}])}function uv(){return Io(8,[ed,{provide:Do,useExisting:ed}])}function lv(e){cn("NgRouterViewTransitions");let t=[{provide:od,useValue:Xm},{provide:id,useValue:D({skipNextTransition:!!e?.skipInitialTransition},e)}];return Io(9,t)}var dv=[At,{provide:mn,useClass:Pt},ot,vn,{provide:Lt,useFactory:f_,deps:[ot]},ia,[]],p_=(()=>{class e{constructor(){}static forRoot(n,r){return{ngModule:e,providers:[dv,[],{provide:mr,multi:!0,useValue:n},[],r?.errorHandler?{provide:sd,useValue:r.errorHandler}:[],{provide:yn,useValue:r||{}},r?.useHash?m_():v_(),g_(),r?.preloadingStrategy?cv(r.preloadingStrategy).\u0275providers:[],r?.initialNavigation?y_(r):[],r?.bindToComponentInputs?uv().\u0275providers:[],r?.enableViewTransitions?lv().\u0275providers:[],D_()]}}static forChild(n){return{ngModule:e,providers:[{provide:mr,multi:!0,useValue:n}]}}static \u0275fac=function(r){return new(r||e)};static \u0275mod=Je({type:e});static \u0275inj=Ze({})}return e})();function g_(){return{provide:rv,useFactory:()=>{let e=p(Hg),t=p(U),n=p(yn),r=p(aa),o=p(mn);return n.scrollOffset&&e.setOffset(n.scrollOffset),new d_(o,r,e,t,n)}}}function m_(){return{provide:wt,useClass:Dl}}function v_(){return{provide:wt,useClass:ys}}function y_(e){return[e.initialNavigation==="disabled"?sv().\u0275providers:[],e.initialNavigation==="enabledBlocking"?iv().\u0275providers:[]]}var ad=new w("");function D_(){return[{provide:ad,useFactory:h_},{provide:ul,multi:!0,useExisting:ad}]}var ua=class e{_isAuthenticated$=new z(!1);_isLoading$=new z(!1);_user$=new z(null);_token$=new z(null);_error$=new z(null);isAuthenticated$=this._isAuthenticated$.asObservable().pipe(Me());isLoading$=this._isLoading$.asObservable().pipe(Me());user$=this._user$.asObservable().pipe(Me());token$=this._token$.asObservable().pipe(Me());error$=this._error$.asObservable().pipe(Me());authState$=zt([this.isAuthenticated$,this.isLoading$,this.user$,this.token$,this.error$]).pipe(_(([t,n,r,o,i])=>({isAuthenticated:t,isLoading:n,user:r,token:o,error:i})),Me((t,n)=>t.isAuthenticated===n.isAuthenticated&&t.isLoading===n.isLoading&&t.user===n.user&&t.token===n.token&&t.error===n.error));get isAuthenticated(){return this._isAuthenticated$.value}get isLoading(){return this._isLoading$.value}get user(){return this._user$.value}get token(){return this._token$.value}get error(){return this._error$.value}get currentState(){return{isAuthenticated:this.isAuthenticated,isLoading:this.isLoading,user:this.user,token:this.token,error:this.error}}setAuthenticated(t){this._isAuthenticated$.next(t)}setLoading(t){this._isLoading$.next(t)}setUser(t){this._user$.next(t)}setToken(t){this._token$.next(t)}setError(t){this._error$.next(t)}clearError(){this._error$.next(null)}setState(t){t.isAuthenticated!==void 0&&this.setAuthenticated(t.isAuthenticated),t.isLoading!==void 0&&this.setLoading(t.isLoading),t.user!==void 0&&this.setUser(t.user),t.token!==void 0&&this.setToken(t.token),t.error!==void 0&&this.setError(t.error)}reset(){this._isAuthenticated$.next(!1),this._isLoading$.next(!1),this._user$.next(null),this._token$.next(null),this._error$.next(null)}setLoginSuccess(t,n){this.setState({isAuthenticated:!0,isLoading:!1,user:t,token:n,error:null})}setLoginFailure(t){this.setState({isAuthenticated:!1,isLoading:!1,user:null,token:null,error:t})}setLogoutState(){this.reset()}userRoles$=this.user$.pipe(_(t=>t?.role||[]),Me());isAdmin$=this.userRoles$.pipe(_(t=>Array.isArray(t)?t.includes("Admin")||t.includes("admin"):t==="Admin"||t==="admin"),Me());authRequired$=this.isAuthenticated$.pipe(_(t=>!t),Me());static \u0275fac=function(n){return new(n||e)};static \u0275prov=m({token:e,factory:e.\u0275fac,providedIn:"root"})};var ud={production:!1,apiUrl:"https://localhost:5000"};var la=class e{handleError(t){let n=t.error;if(t.error instanceof ErrorEvent)return{type:"NETWORK_ERROR",message:"Network error occurred. Please check your connection and try again.",details:t.error.message,statusCode:0};switch(t.status){case 400:return this.handleBadRequest(n,t.status);case 401:return this.handleUnauthorized(n,t.status);case 403:return this.handleForbidden(n,t.status);case 404:return this.handleNotFound(n,t.status);case 500:case 502:case 503:case 504:return this.handleServerError(n,t.status);default:return{type:"UNKNOWN_ERROR",message:"An unexpected error occurred. Please try again later.",details:n?.message,statusCode:t.status}}}handleBadRequest(t,n){if(t?.message)return t.message.toLowerCase().includes("otp")?{type:"OTP_INVALID",message:"Invalid or expired OTP. Please try again.",details:t.message,statusCode:n}:t.message.toLowerCase().includes("inactive")?{type:"ACCOUNT_INACTIVE",message:"Your account is inactive. Please contact support.",details:t.message,statusCode:n}:{type:"VALIDATION_ERROR",message:t.message,statusCode:n};if(t?.errors){let r=this.formatValidationErrors(t.errors);return{type:"VALIDATION_ERROR",message:r,details:t.errors,statusCode:n}}return{type:"VALIDATION_ERROR",message:"Please check your input and try again.",statusCode:n}}handleUnauthorized(t,n){return{type:"INVALID_CREDENTIALS",message:"Invalid email or password. Please try again.",details:t?.message,statusCode:n}}handleForbidden(t,n){return{type:"UNAUTHORIZED",message:"You do not have permission to perform this action.",details:t?.message,statusCode:n}}handleNotFound(t,n){return{type:"USER_NOT_FOUND",message:"User not found. Please check your credentials.",details:t?.message,statusCode:n}}handleServerError(t,n){return{type:"SERVER_ERROR",message:"Server error occurred. Please try again later.",details:t?.message,statusCode:n}}formatValidationErrors(t){return Array.isArray(t)?`Please fix the following issues: ${t.map(r=>r.description||r.message||"Validation error").join("; ")}`:`Please fix the following issues: ${Object.entries(t).map(([r,o])=>`${this.formatFieldName(r)}: ${o.join(", ")}`).join("; ")}`}formatFieldName(t){return t.replace(/([A-Z])/g," $1").replace(/^./,n=>n.toUpperCase()).trim()}createErrorObservable(t){let n=this.handleError(t);return Ct(()=>n)}static \u0275fac=function(n){return new(n||e)};static \u0275prov=m({token:e,factory:e.\u0275fac,providedIn:"root"})};var da=class e{TOKEN_KEY="chattrix_auth_token";USER_INFO_KEY="chattrix_user_info";REFRESH_TOKEN_KEY="chattrix_refresh_token";setToken(t){try{localStorage.setItem(this.TOKEN_KEY,t)}catch(n){console.error("Failed to store token:",n)}}getToken(){try{return localStorage.getItem(this.TOKEN_KEY)}catch(t){return console.error("Failed to retrieve token:",t),null}}setUserInfo(t){try{localStorage.setItem(this.USER_INFO_KEY,JSON.stringify(t))}catch(n){console.error("Failed to store user info:",n)}}getUserInfo(){try{let t=localStorage.getItem(this.USER_INFO_KEY);return t?JSON.parse(t):null}catch(t){return console.error("Failed to retrieve user info:",t),null}}setRefreshToken(t){try{localStorage.setItem(this.REFRESH_TOKEN_KEY,t)}catch(n){console.error("Failed to store refresh token:",n)}}getRefreshToken(){try{return localStorage.getItem(this.REFRESH_TOKEN_KEY)}catch(t){return console.error("Failed to retrieve refresh token:",t),null}}clearAll(){try{localStorage.removeItem(this.TOKEN_KEY),localStorage.removeItem(this.USER_INFO_KEY),localStorage.removeItem(this.REFRESH_TOKEN_KEY)}catch(t){console.error("Failed to clear storage:",t)}}isStorageAvailable(){try{let t="__storage_test__";return localStorage.setItem(t,t),localStorage.removeItem(t),!0}catch{return!1}}encrypt(t){return btoa(t)}decrypt(t){try{return atob(t)}catch{return t}}static \u0275fac=function(n){return new(n||e)};static \u0275prov=m({token:e,factory:e.\u0275fac,providedIn:"root"})};var fa=class e{decodeToken(t){if(!t||typeof t!="string")return null;try{let n=t.split(".");if(n.length!==3)return console.warn("Invalid token format"),null;let o=n[1].replace(/-/g,"+").replace(/_/g,"/"),i=o+"=".repeat((4-o.length%4)%4),s=decodeURIComponent(atob(i).split("").map(c=>"%"+("00"+c.charCodeAt(0).toString(16)).slice(-2)).join("")),a=JSON.parse(s);return this.validateTokenPayload(a)?a:null}catch(n){return console.error("Token decoding failed:",n),null}}validateTokenPayload(t){return t&&typeof t=="object"&&typeof t.exp=="number"&&t.exp>0}isTokenExpired(t){if(!t)return!0;let n=this.decodeToken(t);if(!n||!n.exp)return!0;let r=n.exp*1e3,o=Date.now(),i=5*60*1e3;return o>=r-i}isTokenExpiringSoon(t){if(!t)return!1;let n=this.decodeToken(t);if(!n||!n.exp)return!1;let r=n.exp*1e3,o=Date.now(),i=10*60*1e3;return r-o<=i&&r-o>0}getTokenRemainingTime(t){if(!t)return 0;let n=this.decodeToken(t);if(!n||!n.exp)return 0;let r=n.exp*1e3,o=Date.now();return Math.max(0,r-o)}isValidTokenFormat(t){if(!t||typeof t!="string")return!1;let n=t.split(".");return n.length===3&&n.every(r=>r.length>0)}getUserRoles(t){let n=this.decodeToken(t);return n?Array.isArray(n.Roles)?n.Roles:typeof n.Roles=="string"?[n.Roles]:[]:[]}hasRole(t,n){return this.getUserRoles(t).includes(n)}getUserId(t){let n=this.decodeToken(t);return n?.id||n?.nameid||null}getUserEmail(t){let n=this.decodeToken(t);return n?.Email||n?.email||null}static \u0275fac=function(n){return new(n||e)};static \u0275prov=m({token:e,factory:e.\u0275fac,providedIn:"root"})};var ha=class e{validateLoginRequest(t){let n={};return t.email?this.isValidEmail(t.email)||(n.email=["Please enter a valid email address"]):n.email=["Email is required"],t.password?t.password.length<6&&(n.password=["Password must be at least 6 characters long"]):n.password=["Password is required"],{isValid:Object.keys(n).length===0,errors:n}}validateRegisterRequest(t){let n={};t.fullName?t.fullName.trim().length<2?n.fullName=["Full name must be at least 2 characters long"]:t.fullName.length>100&&(n.fullName=["Full name must not exceed 100 characters"]):n.fullName=["Full name is required"],t.email?this.isValidEmail(t.email)||(n.email=["Please enter a valid email address"]):n.email=["Email is required"];let r=this.validatePassword(t.password);return r.length>0&&(n.password=r),t.phoneNumber&&!this.isValidPhoneNumber(t.phoneNumber)&&(n.phoneNumber=["Please enter a valid phone number"]),t.description&&t.description.length>500&&(n.description=["Description must not exceed 500 characters"]),{isValid:Object.keys(n).length===0,errors:n}}validateForgotPasswordRequest(t){let n={};return t.email?this.isValidEmail(t.email)||(n.email=["Please enter a valid email address"]):n.email=["Email is required"],{isValid:Object.keys(n).length===0,errors:n}}validateResetPasswordRequest(t){let n={};t.email?this.isValidEmail(t.email)||(n.email=["Please enter a valid email address"]):n.email=["Email is required"],t.resetToken?t.resetToken.length!==6&&(n.resetToken=["Reset token must be 6 digits"]):n.resetToken=["Reset token is required"];let r=this.validatePassword(t.newPassword);return r.length>0&&(n.newPassword=r),t.confirmPassword?t.newPassword!==t.confirmPassword&&(n.confirmPassword=["Passwords do not match"]):n.confirmPassword=["Please confirm your password"],{isValid:Object.keys(n).length===0,errors:n}}validateOtpRequest(t){let n={};return t.userId||(n.userId=["User ID is required"]),t.otp?/^\d{5}$/.test(t.otp)||(n.otp=["OTP must be 5 digits"]):n.otp=["OTP is required"],{isValid:Object.keys(n).length===0,errors:n}}isValidEmail(t){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t.trim())}validatePassword(t){let n=[];return t?(t.length<8&&n.push("Password must be at least 8 characters long"),t.length>128&&n.push("Password must not exceed 128 characters"),/[A-Z]/.test(t)||n.push("Password must contain at least one uppercase letter"),/[a-z]/.test(t)||n.push("Password must contain at least one lowercase letter"),/\d/.test(t)||n.push("Password must contain at least one number"),/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(t)||n.push("Password must contain at least one special character"),n):(n.push("Password is required"),n)}isValidPhoneNumber(t){return/^[\+]?[1-9][\d]{0,15}$/.test(t.replace(/[\s\-\(\)]/g,""))}sanitizeString(t){return t?t.trim().replace(/[<>]/g,"").replace(/javascript:/gi,"").replace(/on\w+=/gi,""):""}sanitizeEmail(t){return t?t.trim().toLowerCase():""}sanitizePhoneNumber(t){return t?t.replace(/[^\d\+\-\(\)\s]/g,""):""}static \u0275fac=function(n){return new(n||e)};static \u0275prov=m({token:e,factory:e.\u0275fac,providedIn:"root"})};var fv=class e{constructor(t,n,r,o,i,s,a){this.httpClient=t;this.errorHandler=n;this.storage=r;this.tokenValidator=o;this.inputValidator=i;this.authState=s;this.router=a;this.initializeAuthState()}API_URL=`${ud.apiUrl}/api/Account`;login(t){let n=this.inputValidator.validateLoginRequest(t);if(!n.isValid){let o=Object.values(n.errors).flat().join("; ");throw this.authState.setLoginFailure(o),new Error(o)}let r={email:this.inputValidator.sanitizeEmail(t.email),password:t.password};return this.authState.setLoading(!0),this.authState.clearError(),this.httpClient.post(`${this.API_URL}/Login`,r).pipe(W(o=>{if(o.isSuccess&&o.token){let i=this.mapTokenToUserInfo(o.token);i&&(this.storage.setToken(o.token),this.storage.setUserInfo(i),this.authState.setLoginSuccess(i,o.token))}else(o.data?.requiresOtp||o.data?.RequiresOtp)&&this.authState.setLoading(!1)}),te(o=>{let i=this.errorHandler.handleError(o);return this.authState.setLoginFailure(i.message),this.errorHandler.createErrorObservable(o)}),oe(()=>{this.authState.setLoading(!1)}))}register(t){let n=this.inputValidator.validateRegisterRequest(t);if(!n.isValid){let o=Object.values(n.errors).flat().join("; ");throw this.authState.setError(o),new Error(o)}let r={fullName:this.inputValidator.sanitizeString(t.fullName),email:this.inputValidator.sanitizeEmail(t.email),password:t.password,phoneNumber:t.phoneNumber?this.inputValidator.sanitizePhoneNumber(t.phoneNumber):void 0,description:t.description?this.inputValidator.sanitizeString(t.description):void 0};return this.authState.setLoading(!0),this.authState.clearError(),this.httpClient.post(`${this.API_URL}/Register`,r).pipe(W(o=>{o.isSuccess&&this.authState.setLoading(!1)}),te(o=>{let i=this.errorHandler.handleError(o);return this.authState.setError(i.message),this.errorHandler.createErrorObservable(o)}),oe(()=>{this.authState.setLoading(!1)}))}loginWith2FA(t,n){let r=this.inputValidator.validateOtpRequest({userId:t,otp:n});if(!r.isValid){let i=Object.values(r.errors).flat().join("; ");throw this.authState.setError(i),new Error(i)}this.authState.setLoading(!0),this.authState.clearError();let o={userId:t,otp:n};return this.httpClient.post(`${this.API_URL}/VerifyOtp`,o).pipe(W(i=>{if(i.isSuccess&&i.token){let s=this.mapTokenToUserInfo(i.token);s&&(this.storage.setToken(i.token),this.storage.setUserInfo(s),this.authState.setLoginSuccess(s,i.token))}}),te(i=>{let s=this.errorHandler.handleError(i);return this.authState.setError(s.message),this.errorHandler.createErrorObservable(i)}),oe(()=>{this.authState.setLoading(!1)}))}resendOtp(t){if(!t){let r="User ID is required";throw this.authState.setError(r),new Error(r)}this.authState.setLoading(!0),this.authState.clearError();let n={userId:t};return this.httpClient.post(`${this.API_URL}/ResendOtp`,n).pipe(te(r=>{let o=this.errorHandler.handleError(r);return this.authState.setError(o.message),this.errorHandler.createErrorObservable(r)}),oe(()=>{this.authState.setLoading(!1)}))}forgotPassword(t){let n=this.inputValidator.validateForgotPasswordRequest(t);if(!n.isValid){let o=Object.values(n.errors).flat().join("; ");throw this.authState.setError(o),new Error(o)}let r={email:this.inputValidator.sanitizeEmail(t.email)};return this.authState.setLoading(!0),this.authState.clearError(),this.httpClient.post(`${this.API_URL}/ForgotPassword`,r).pipe(te(o=>{let i=this.errorHandler.handleError(o);return this.authState.setError(i.message),this.errorHandler.createErrorObservable(o)}),oe(()=>{this.authState.setLoading(!1)}))}verifyResetToken(t){if(!t.email||!t.resetToken){let r="Email and reset token are required";throw this.authState.setError(r),new Error(r)}let n={email:this.inputValidator.sanitizeEmail(t.email),resetToken:t.resetToken.trim()};return this.authState.setLoading(!0),this.authState.clearError(),this.httpClient.post(`${this.API_URL}/VerifyResetToken`,n).pipe(te(r=>{let o=this.errorHandler.handleError(r);return this.authState.setError(o.message),this.errorHandler.createErrorObservable(r)}),oe(()=>{this.authState.setLoading(!1)}))}resetPassword(t){let n=this.inputValidator.validateResetPasswordRequest(t);if(!n.isValid){let o=Object.values(n.errors).flat().join("; ");throw this.authState.setError(o),new Error(o)}let r={email:this.inputValidator.sanitizeEmail(t.email),resetToken:t.resetToken.trim(),newPassword:t.newPassword,confirmPassword:t.confirmPassword};return this.authState.setLoading(!0),this.authState.clearError(),this.httpClient.post(`${this.API_URL}/ResetPassword`,r).pipe(te(o=>{let i=this.errorHandler.handleError(o);return this.authState.setError(i.message),this.errorHandler.createErrorObservable(o)}),oe(()=>{this.authState.setLoading(!1)}))}getToken(){return this.storage.getToken()}getUserInfo(){return this.storage.getUserInfo()}isLoggedIn(){let t=this.getToken();return t!==null&&!this.tokenValidator.isTokenExpired(t)}isTokenExpired(){let t=this.getToken();return this.tokenValidator.isTokenExpired(t)}hasRole(t){let n=this.getUserInfo();return!n||!n.role?!1:Array.isArray(n.role)?n.role.includes(t):n.role===t}logout(){this.storage.clearAll(),this.authState.setLogoutState(),this.router.navigate(["/auth/login"])}refreshSession(){if(this.isTokenExpired())this.logout();else{let t=this.getToken();if(t){let n=this.mapTokenToUserInfo(t);n&&(this.storage.setUserInfo(n),this.authState.setUser(n))}}}refreshUserInfoFromToken(){let t=this.getToken();if(t&&!this.isTokenExpired()){let n=this.mapTokenToUserInfo(t);if(n)return this.storage.setUserInfo(n),this.authState.setUser(n),!0}return!1}get authState$(){return this.authState.authState$}get isAuthenticated$(){return this.authState.isAuthenticated$}get isLoading$(){return this.authState.isLoading$}get user$(){return this.authState.user$}get error$(){return this.authState.error$}get userRoles$(){return this.authState.userRoles$}get isAdmin$(){return this.authState.isAdmin$}initializeAuthState(){let t=this.storage.getToken(),n=this.storage.getUserInfo();t&&!this.tokenValidator.isTokenExpired(t)&&n?this.authState.setLoginSuccess(n,t):(this.storage.clearAll(),this.authState.reset())}mapTokenToUserInfo(t){let n=this.tokenValidator.decodeToken(t);return n?{id:n.id||n.nameid||"",name:n.FullName||n.name||"",email:n.Email||n.email||"",role:n.Roles||[],isActive:n.IsActive||!0,phoneNumber:n.PhoneNumber||void 0,description:n.Description||void 0}:null}debugToken(){if(!ud.production){let t=this.getToken();if(t){let n=this.tokenValidator.decodeToken(t);console.log("Token Debug Info:",{token:t.substring(0,20)+"...",decoded:n,isExpired:this.tokenValidator.isTokenExpired(t),remainingTime:this.tokenValidator.getTokenRemainingTime(t)})}else console.log("No token found")}}static \u0275fac=function(n){return new(n||e)(E(Os),E(la),E(da),E(fa),E(ha),E(ua),E(ot))};static \u0275prov=m({token:e,factory:e.\u0275fac,providedIn:"root"})};export{D as a,j as b,yv as c,q as d,F as e,G as f,z as g,ie as h,$ as i,C as j,Ct as k,_ as l,zt as m,_n as n,Wv as o,Zv as p,Yv as q,re as r,Qv as s,te as t,Kv as u,Be as v,Me as w,oe as x,ey as y,Ha as z,ty as A,ny as B,za as C,me as D,ci as E,W as F,y as G,Vf as H,m as I,Ze as J,w as K,E as L,p as M,se as N,$r as O,YO as P,QO as Q,KO as R,JO as S,Su as T,ce as U,of as V,ye as W,U as X,Ge as Y,an as Z,XO as _,$D as $,Nu as aa,Qn as ba,ek as ca,Au as da,nE as ea,Pu as fa,Dt as ga,tk as ha,nk as ia,nn as ja,Bn as ka,Xu as la,Se as ma,Et as na,lk as oa,$n as pa,eg as qa,Je as ra,ln as sa,GI as ta,nC as ua,Xn as va,pt as wa,lg as xa,wC as ya,IC as za,CC as Aa,hk as Ba,pk as Ca,yg as Da,Dg as Ea,ll as Fa,Eg as Ga,wg as Ha,LC as Ia,gk as Ja,VC as Ka,Ig as La,mk as Ma,vk as Na,yk as Oa,WC as Pa,Dk as Qa,ZC as Ra,YC as Sa,Ek as Ta,wk as Ua,Ik as Va,Ck as Wa,KC as Xa,Cg as Ya,eb as Za,bk as _a,Sk as $a,Tk as ab,_k as bb,Mk as cb,er as db,Ag as eb,Mb as fb,pl as gb,Rk as hb,Rb as ib,Nk as jb,K as kb,Xe as lb,At as mb,Bg as nb,Lb as ob,jb as pb,$b as qb,El as rb,Hb as sb,Ml as tb,aS as ub,lS as vb,Ot as wb,no as xb,Os as yb,um as zb,OS as Ab,PS as Bb,Lt as Cb,Xl as Db,ot as Eb,p_ as Fb,ud as Gb,la as Hb,ua as Ib,fv as Jb};
